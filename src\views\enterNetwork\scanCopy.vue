<template>
  <div>
    <van-nav-bar
      title="高清识别"
      left-arrow
      @click-left="onleftClick"
    ></van-nav-bar>
    <scan-code
      @success="handleSuccess"
      @error="handleError"
      continue
      definition
    ></scan-code>
  </div>
</template>

<script>
import { NavBar, Toast } from "vant";
import scanCode from "@/components/scanCode.vue";
export default {
  name: "scanCopy",
  components: {
    [NavBar.name]: NavBar,
    [Toast.name]: Toast,
    scanCode
  },
  data() {
    return {
      token: "",
      groupId: "",
      deptId: ""
    };
  },
  created() {
    this.token = this.$route.query.token;
    this.groupId = this.$route.query.groupId;
    this.deptId = this.$route.query.deptId;
  },
  methods: {
    onleftClick() {
      this.$router.go(-1);
    },
    handleSuccess(data) {
      console.log(data);
      const url = new URL(data);
      const path = url.pathname;
      const queryParams = new URLSearchParams(url.search);
      queryParams.append("token", this.token);
      queryParams.append("groupId", this.groupId);
      queryParams.append("deptId", this.deptId);

      if (path.startsWith("/notice/enterNetworkLogin")) {
        this.$router.push({
          name: path.replace(/^\/notice\//, ""),
          query: Object.fromEntries(queryParams)
        });
      }
    },
    handleError(err) {
      Toast.fail({
        duration: 2000,
        message: err.result
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
