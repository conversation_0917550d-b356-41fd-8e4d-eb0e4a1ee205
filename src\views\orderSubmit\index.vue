<template>
  <div class="order-page">
    <!-- 固定导航栏 -->
    <header class="page-header">
      <div class="nav-bar">
        <div class="nav-back" @click="goBack">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M15 18L9 12L15 6"
              stroke="white"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
      </div>
    </header>

    <!-- 可滚动主内容区域 -->
    <main class="page-main">
      <div class="scroll-content">
        <!-- 背景图区域 -->
        <div class="bg-section">
          <!-- 主订单卡片 -->
          <div class="order-card">
            <div class="order-header">
              <h2 class="order-title">{{productData.productName}}</h2>
              <!-- <p class="order-number">订单号：OVO-T-202507070000001</p> -->
            </div>

            <!-- 检查项目列表 -->
            <div class="check-list">
              <!-- 加载状态 -->
              <div v-if="pageLoading" class="loading-container">
                <van-loading type="spinner" color="#FF9900"
                  >加载检查项目中...</van-loading
                >
              </div>
              <!-- 检查项目列表 -->
              <div v-else-if="modules.length > 0">
                <div
                  class="check-item"
                  v-for="module in modules"
                  :key="module.id"
                >
                  <span class="check-text">{{ module.moduleName }}</span>
                  <span class="check-icon">✓</span>
                </div>
              </div>
              <!-- 空状态 -->
              <div v-else class="empty-state">
                <van-empty description="暂无检查项目" />
              </div>
            </div>

            <!-- 产品信息表格 -->
            <div class="product-table">
              <div class="table-row">
                <span class="label">产品类型</span>
                <span class="value">{{ productData.topType }}</span>
              </div>
              <div class="table-row">
                <span class="label">产品小类</span>
                <span class="value">{{ productData.firstType }}</span>
              </div>
              <div class="table-row">
                <span class="label">单价</span>
                <span class="value">{{ productData.unitPrice }}</span>
              </div>
              <div class="table-row">
                <span class="label">购买时常（月）</span>
                <div class="value">
                  <div class="stepper-container">
                    <button class="stepper-btn" @click="decreaseMainPeriod">
                      -
                    </button>
                    <span class="quantity">{{ mainPeriod }}</span>
                    <button class="stepper-btn" @click="mainPeriod++">+</button>
                  </div>
                </div>
              </div>
              <div class="table-row">
                <span class="label">购买数量</span>
                <div class="value">
                  <div class="stepper-container">
                    <button class="stepper-btn" @click="decreaseMainQuantity">
                      -
                    </button>
                    <span class="quantity">{{ mainQuantity }}</span>
                    <button class="stepper-btn" @click="mainQuantity++">
                      +
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
          <!-- 配套产品列表 -->
          <div class="section-title">
            <span>配套产品列表</span>
          </div>

          <!-- 配套产品卡片 -->
          <div
            class="product-card"
            v-for="(product, index) in products"
            :key="index"
          >
            <h3 class="product-name">{{ product.name }}</h3>
            <div class="product-table">
              <div class="table-row">
                <span class="label">物料编码</span>
                <span class="value">{{ product.description }}</span>
              </div>
              <div class="table-row">
                <span class="label">产品类型</span>
                <span class="value">{{ product.type }}</span>
              </div>
              <div class="table-row">
                <span class="label">产品小类</span>
                <span class="value">{{ product.category }}</span>
              </div>
              <div class="table-row">
                <span class="label">单价</span>
                <span class="value">{{ product.price }}</span>
              </div>
              <div class="table-row">
                <span class="label">购买时常（月）</span>
                <div class="value">
                  <div class="stepper-container">
                    <button
                      class="stepper-btn"
                      @click="decreaseProductPeriod(product)"
                    >
                      -
                    </button>
                    <span class="quantity">{{ product.period }}</span>
                    <button class="stepper-btn" @click="product.period++">
                      +
                    </button>
                  </div>
                </div>
              </div>
              <div class="table-row">
                <span class="label">购买数量</span>
                <div class="value">
                  <div class="stepper-container">
                    <button
                      class="stepper-btn"
                      @click="decreaseProductQuantity(product)"
                    >
                      -
                    </button>
                    <span class="quantity">{{ product.quantity }}</span>
                    <button class="stepper-btn" @click="product.quantity++">
                      +
                    </button>
                  </div>
                </div>
              </div>
              <div class="table-row">
                <span class="label">应付金额</span>
                <span class="value">{{ product.amount }}</span>
              </div>
            </div>
          </div>
          <!-- 配件列表容器 -->
          <div class="accessories-container">
            <!-- 加载状态 -->
            <div v-if="pageLoading" class="loading-container">
              <van-loading type="spinner" color="#FF9900"
                >加载配件列表中...</van-loading
              >
            </div>

            <!-- 配件列表 -->
            <div v-else-if="relatedProducts.length > 0">
              <div
                class="product-card"
                v-for="accessory in relatedProducts"
                :key="accessory.id"
              >
                <h3 class="product-name">{{ accessory.productName }}</h3>
                <div class="product-table">
                  <div class="table-row">
                    <span class="label">物料编码</span>
                    <span class="value">{{ accessory.materialCoding }}</span>
                  </div>
                  <div class="table-row">
                    <span class="label">产品类型</span>
                    <span class="value">{{
                      getProductTypeName(accessory.topType)
                    }}</span>
                  </div>
                  <div class="table-row">
                    <span class="label">产品小类</span>
                    <span class="value">{{
                      getProductSubTypeName(accessory.firstType)
                    }}</span>
                  </div>
                  <div class="table-row">
                    <span class="label">单价</span>
                    <span class="value">¥{{ accessory.unitPrice }}</span>
                  </div>
                  <!-- 只有软件类型（topType=1）才显示购买时长 -->
                  <div class="table-row" v-if="accessory.topType === 1">
                    <span class="label">购买时常（月）</span>
                    <div class="value">
                      <div class="stepper-container">
                        <button
                          class="stepper-btn"
                          @click="decreaseAccessoryPeriod(accessory)"
                        >
                          -
                        </button>
                        <span class="quantity">{{ accessory.period }}</span>
                        <button class="stepper-btn" @click="accessory.period++">
                          +
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="table-row">
                    <span class="label">购买数量</span>
                    <div class="value">
                      <div class="stepper-container">
                        <button
                          class="stepper-btn"
                          @click="decreaseAccessoryQuantity(accessory)"
                        >
                          -
                        </button>
                        <span class="quantity">{{ accessory.quantity }}</span>
                        <button
                          class="stepper-btn"
                          @click="accessory.quantity++"
                        >
                          +
                        </button>
                      </div>
                    </div>
                  </div>
                  <div class="table-row">
                    <span class="label">应付金额</span>
                    <span class="value"
                      >¥{{ calculateAccessoryAmount(accessory) }}</span
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- 配件列表空状态 -->
            <div v-else class="empty-state">
              <van-empty description="暂无配件信息" />
            </div>
          </div>

          <!-- 客户信息 -->
          <div class="section-title">客户信息</div>

          <div class="customer-card">
            <div class="customer-section">
              <!-- <h4 class="section-subtitle">客户信息</h4> -->
              <div class="customer-table">
                <div class="table-row">
                  <span class="label">客户名称</span>
                  <span class="value">苏州万店堂网络科技有限公司</span>
                </div>
                <div class="table-row clickable" @click="openStorePopup">
                  <span class="label">门店名称</span>
                  <div class="value-with-arrow">
                    <span class="value">{{ selectedStore }}</span>
                    <van-icon name="arrow" class="arrow-icon" />
                  </div>
                </div>
                <div class="table-row clickable" @click="openManagerPopup">
                  <span class="label">店长</span>
                  <div class="value-with-arrow">
                    <span class="value">{{ selectedManager }}</span>
                    <van-icon name="arrow" class="arrow-icon" />
                  </div>
                </div>
              </div>
            </div>

            <div class="customer-section">
              <div class="customer-table">
                <div class="table-row clickable" @click="openAddressPopup">
                  <span class="section-subtitle">收货地址</span>
                  <div class="value-with-arrow">
                    <span class="value">{{ selectedAddress.name }}</span>
                    <van-icon name="arrow" class="arrow-icon" />
                  </div>
                </div>
                <div class="table-row">
                  <span class="label">收货人</span>
                  <span class="value">{{ selectedAddress.receiver }}</span>
                </div>
                <div class="table-row">
                  <span class="label">联系方式</span>
                  <span class="value">{{ selectedAddress.phone }}</span>
                </div>
                <div class="table-row">
                  <span class="label">地址</span>
                  <span class="value">{{ selectedAddress.detail }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 固定底部操作栏 -->
    <footer class="page-footer">
      <div class="total-price">¥800.00</div>
      <button class="confirm-button" @click="confirmOrder">确认下单</button>
    </footer>

    <!-- 门店选择 -->
    <van-popup
      v-model="showStorePopup"
      position="bottom"
      :style="{ height: '60%' }"
      round
    >
      <div class="store-popup">
        <div class="popup-header">
          <span class="popup-title">请选择门店</span>
          <van-icon name="cross" @click="showStorePopup = false" />
        </div>
        <!-- 搜索框 -->
        <div class="search-container">
          <van-search
            v-model="storeQueryParams.keyword"
            placeholder="搜索门店名称"
            @search="searchStore"
            @input="searchStore"
            @clear="searchStore"
          />
        </div>

        <div class="store-list">
          <!-- 加载状态 -->
          <div v-if="storeLoading" class="loading-container">
            <van-loading type="spinner" color="#FF9900">加载中...</van-loading>
          </div>

          <!-- 门店列表 -->
          <div v-else>
            <div
              v-for="store in filteredStoreOptions"
              :key="store.id"
              class="store-item"
              :class="{ selected: store.id === selectedStoreId }"
              @click="selectStore(store)"
            >
              <div class="store-info">
                <div class="store-name">{{ store.name }}</div>
                <div class="store-detail" v-if="store.address">
                  {{ store.address }}
                </div>
              </div>
              <van-icon
                v-if="store.id === selectedStoreId"
                name="success"
                color="#FF9900"
              />
            </div>

            <!-- 空状态 -->
            <div v-if="filteredStoreOptions.length === 0" class="empty-state">
              <van-empty description="暂无门店数据" />
            </div>
          </div>
        </div>

        <!-- 新增门店按钮 -->
        <div class="add-store-btn">
          <van-button
            type="default"
            block
            icon="plus"
            @click="openAddStoreForm"
          >
            新增门店
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 店长选择 -->
    <van-popup
      v-model="showManagerPopup"
      position="bottom"
      :style="{ height: '60%' }"
      round
    >
      <div class="manager-popup">
        <div class="popup-header">
          <span class="popup-title">请选择店长</span>
          <van-icon name="cross" @click="showManagerPopup = false" />
        </div>
        <!-- 搜索框 -->
        <div class="search-container">
          <van-search
            v-model="managerQueryParams.keyword"
            placeholder="搜索店长姓名"
            @search="searchManager"
            @input="searchManager"
            @clear="searchManager"
          />
        </div>

        <div class="manager-list">
          <!-- 加载状态 -->
          <div v-if="managerLoading" class="loading-container">
            <van-loading type="spinner" color="#FF9900">加载中...</van-loading>
          </div>

          <!-- 店长列表 -->
          <div v-else>
            <div
              v-for="manager in filteredManagerOptions"
              :key="manager.id"
              class="manager-item"
              :class="{ selected: manager.id === selectedManagerId }"
              @click="selectManager(manager)"
            >
              <div class="manager-info">
                <div class="manager-name">{{ manager.name }}</div>
                <div class="manager-detail" v-if="manager.phone">
                  {{ manager.phone }}
                </div>
              </div>
              <van-icon
                v-if="manager.id === selectedManagerId"
                name="success"
                color="#FF9900"
              />
            </div>

            <!-- 空状态 -->
            <div v-if="filteredManagerOptions.length === 0" class="empty-state">
              <van-empty description="暂无店长数据" />
            </div>
          </div>
        </div>

        <!-- 新增店长按钮 -->
        <div class="add-manager-btn">
          <van-button
            type="default"
            block
            icon="plus"
            @click="openAddManagerForm"
          >
            新增店长
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 收货地址选择 -->
    <van-popup
      v-model="showAddressPopup"
      position="bottom"
      :style="{ height: '60%' }"
      round
    >
      <div class="address-popup">
        <div class="popup-header">
          <span class="popup-title">请选择收货地址</span>
          <van-icon name="cross" @click="showAddressPopup = false" />
        </div>
        <!-- 搜索框 -->
        <div class="search-container">
          <van-search
            v-model="addressQueryParams.keyword"
            placeholder="搜索收货人、手机号或地址"
            @search="searchAddress"
            @input="searchAddress"
            @clear="searchAddress"
          />
        </div>

        <div class="address-list">
          <!-- 加载状态 -->
          <div v-if="addressLoading" class="loading-container">
            <van-loading type="spinner" color="#FF9900">加载中...</van-loading>
          </div>

          <!-- 地址列表 -->
          <div v-else>
            <div
              v-for="address in addressList"
              :key="address.id"
              class="address-item"
              :class="{ selected: address.id === selectedAddress.id }"
              @click="selectAddress(address)"
            >
              <div class="address-info">
                <div class="address-name">{{ address.name }}</div>
                <div class="address-detail">
                  {{ address.receiver }} {{ address.phone }}
                </div>
                <div class="address-full">{{ address.detail }}</div>
              </div>
              <van-icon
                v-if="address.id === selectedAddress.id"
                name="success"
                color="#FF9900"
              />
            </div>

            <!-- 空状态 -->
            <div v-if="addressList.length === 0" class="empty-state">
              <van-empty description="暂无地址数据" />
            </div>
          </div>
        </div>

        <!-- 新增地址按钮 -->
        <div class="add-address-btn">
          <van-button
            type="default"
            block
            icon="plus"
            @click="openAddAddressForm"
          >
            新增地址
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 新增地址表单组件 -->
    <AddAddressForm
      v-model="showAddAddressForm"
      :customerId="2876"
      @save="handleSaveAddress"
    />

    <!-- 新增门店表单组件 -->
    <AddStoreForm v-model="showAddStoreForm" @save="handleSaveStore" />

    <!-- 新增店长表单组件 -->
    <AddManagerForm v-model="showAddManagerForm" @save="handleSaveManager" />
  </div>
</template>

<script>
// api
import {
  getMasterOrderAddressList,
  insertOrUpdateMasterOrderAddress,
  getDeptByGroupId,
  searchUsersByRole,
  detail,
  queryProductTypesToTree,
} from "@/api/orderManage";
import {
  ActionSheet,
  Popup,
  Icon,
  Search,
  Loading,
  Empty,
  Button,
  Toast
} from "vant";
// components
import AddAddressForm from "./AddAddressForm.vue";
import AddStoreForm from "./AddStoreForm.vue";
import AddManagerForm from "./AddManagerForm.vue";

export default {
  name: "OrderPageOptimized",
  components: {
    [ActionSheet.name]: ActionSheet,
    [Popup.name]: Popup,
    [Icon.name]: Icon,
    [Search.name]: Search,
    [Loading.name]: Loading,
    [Empty.name]: Empty,
    [Button.name]: Button,
    AddAddressForm,
    AddStoreForm,
    AddManagerForm
  },
  data() {
    return {
      mainPeriod: 12,
      mainQuantity: 1,

      // 弹出层状态
      showStorePopup: false,
      showManagerPopup: false,
      showAddressPopup: false,
      showAddAddressForm: false,
      showAddStoreForm: false,
      showAddManagerForm: false,

      // 选择的数据
      selectedStore: "",
      selectedStoreId: null,
      selectedManager: "",
      selectedManagerId: null,
      selectedAddress: {},
      productData: {},
      // 门店选项数据 - 从接口获取
      storeOptions: [],

      // 店长选项数据 - 从接口获取
      managerOptions: [],

      // 地址列表 - 从接口获取
      addressList: [],

      // 地址查询参数
      addressQueryParams: {
        no: 1,
        limit: 20,
        groupId: 1084, // 根据实际业务设置
        keyword: ""
      },

      // 加载状态
      addressLoading: false,

      // 搜索防抖定时器
      searchTimer: null,

      // 用户数据
      userDatas: {
        groupId: 1084 // 根据实际业务设置
      },

      // 门店查询参数
      storeQueryParams: {
        shopName: "",
        groupId: 1084,
        isEle: 1,
        keyword: "" // 用于搜索框绑定
      },

      // 门店加载状态
      storeLoading: false,

      // 门店搜索防抖定时器
      storeSearchTimer: null,

      // 店长查询参数
      managerQueryParams: {
        roleId: 2,
        groupId: 1084, // 根据实际业务设置
        pageNum: 1,
        pageSize: 50,
        userName: "",
        franchiseeType: 0,
        keyword: "" // 用于搜索框绑定
      },

      // 店长加载状态
      managerLoading: false,

      // 店长搜索防抖定时器
      managerSearchTimer: null,

      // 页面加载状态
      pageLoading: true,

      // 检查项目列表数据 - 从接口获取
      modules: [],

      // 配件产品数据 - 从接口获取
      relatedProducts: [],

      products: [
        
      ]
    };
  },
  computed: {
    // 过滤后的门店列表（现在直接返回storeOptions，因为搜索通过接口实现）
    filteredStoreOptions() {
      return this.storeOptions;
    },

    // 过滤后的店长列表（现在直接返回managerOptions，因为搜索通过接口实现）
    filteredManagerOptions() {
      return this.managerOptions;
    }
  },
  methods: {
    goBack() {
      console.log("返回上一页");
    },
    confirmOrder() {
      console.log("确认下单");

      // 获取当前选中门店的正确ID
      const storeInfo = this.getSelectedStoreId();
      if (storeInfo) {
        console.log("门店信息:", storeInfo);
        // 根据门店类型使用不同的字段名
        if (storeInfo.type === "deptAddressId") {
          console.log("新增门店，使用 deptAddressId:", storeInfo.id);
        } else {
          console.log("已存在门店，使用 deptId:", storeInfo.id);
        }
      }
    },

    // 主产品时长减少
    decreaseMainPeriod() {
      if (this.mainPeriod > 1) {
        this.mainPeriod--;
      }
    },

    // 主产品数量减少
    decreaseMainQuantity() {
      if (this.mainQuantity > 1) {
        this.mainQuantity--;
      }
    },

    // 配套产品时长减少
    decreaseProductPeriod(product) {
      if (product.period > 1) {
        product.period--;
      }
    },

    // 配套产品数量减少
    decreaseProductQuantity(product) {
      if (product.quantity > 1) {
        product.quantity--;
      }
    },

    // 配件相关操作方法
    decreaseAccessoryPeriod(accessory) {
      if (accessory.period > 1) {
        accessory.period--;
      }
    },

    decreaseAccessoryQuantity(accessory) {
      if (accessory.quantity > 1) {
        accessory.quantity--;
      }
    },

    // 计算配件应付金额
    calculateAccessoryAmount(accessory) {
      // 只有软件类型（topType=1）才按时长计算，其他类型时长固定为1
      const period = accessory.topType === 1 ? accessory.period : 1;
      return accessory.unitPrice * accessory.quantity * period;
    },

    // 获取产品类型名称（根据topType）
    getProductTypeName(topType) {
      const typeMap = {
        1: "软件",
        2: "安装",
        4: "硬件",
        5: "打包"
      };
      return typeMap[topType] || "未知类型";
    },

    // 获取产品小类名称（根据firstType）
    getProductSubTypeName(firstType) {
      const subTypeMap = {
        201: "摄像头",
        202: "智能定点摄像机",
        203: "监控设备",
        293: "网络设备",
        294: "存储设备"
      };
      return subTypeMap[firstType] || "未知小类";
    },

    // 显示门店选择
    openStorePopup() {
      this.showStorePopup = true;
      // 如果门店列表为空，则加载门店数据
      if (this.storeOptions.length === 0) {
        this.loadStoreList();
      }
    },

    // 显示店长选择
    openManagerPopup() {
      this.showManagerPopup = true;
      // 如果店长列表为空，则加载店长数据
      if (this.managerOptions.length === 0) {
        this.loadManagerList();
      }
    },

    // 显示地址选择
    openAddressPopup() {
      this.showAddressPopup = true;
      // 如果地址列表为空，则加载地址数据
      if (this.addressList.length === 0) {
        this.loadAddressList();
      }
    },

    // 加载地址列表
    async loadAddressList() {
      try {
        this.addressLoading = true;
        const response = await getMasterOrderAddressList(
          this.addressQueryParams
        );

        if (response && response.data && response.data.records) {
          // 将接口返回的数据转换为页面需要的格式
          this.addressList = response.data.records.map(item => ({
            id: item.id,
            name: `${item.province} ${item.city} ${item.area}`, // 组合省市区
            receiver: item.name, // 收件人姓名
            phone: item.mobile, // 手机号
            detail: item.address, // 详细地址
            // 保留原始数据，以备后用
            originalData: item
          }));

          // 如果还没有选中的地址，默认选择第一个
          if (!this.selectedAddress.id && this.addressList.length > 0) {
            this.selectedAddress = this.addressList[0];
          }
        }
      } catch (error) {
        console.error("加载地址列表失败:", error);
        // 可以在这里添加错误提示
        Toast("加载地址列表失败，请重试");
      } finally {
        this.addressLoading = false;
      }
    },

    // 加载门店列表
    async loadStoreList() {
      try {
        this.storeLoading = true;

        // 构造接口参数，排除不需要的字段
        const apiParams = {
          shopName: this.storeQueryParams.shopName,
          groupId: this.storeQueryParams.groupId,
          isEle: this.storeQueryParams.isEle
          // 注意：不传递 keyword 字段给后端
        };

        const response = await getDeptByGroupId(apiParams);

        if (response.length > 0) {
          // 将接口返回的数据转换为页面需要的格式
          let storeList = response.map(item => ({
            id: item.id,
            name: item.name, // 门店名称
            address: item.address || item.shopAddress || "", // 门店地址
            location: item.location || "", // 行政区编码
            groupId: item.groupId,
            // 保留原始数据，以备后用
            originalData: item
          }));

          // 限制只显示前100条数据
          if (storeList.length > 100) {
            storeList = storeList.slice(0, 100);
          }

          this.storeOptions = storeList;

          // 如果还没有选中的门店，默认选择第一个
          if (!this.selectedStoreId && this.storeOptions.length > 0) {
            this.selectedStore = this.storeOptions[0].name;
            this.selectedStoreId = this.storeOptions[0].id;
          }
        }
      } catch (error) {
        Toast("加载门店列表失败，请重试");
      } finally {
        this.storeLoading = false;
      }
    },

    // 加载店长列表
    async loadManagerList() {
      try {
        this.managerLoading = true;

        // 构造接口参数，排除不需要的字段
        const apiParams = {
          roleId: this.managerQueryParams.roleId,
          groupId: this.managerQueryParams.groupId,
          pageNum: this.managerQueryParams.pageNum,
          pageSize: this.managerQueryParams.pageSize,
          userName: this.managerQueryParams.userName,
          franchiseeType: this.managerQueryParams.franchiseeType
          // 注意：不传递 keyword 字段给后端
        };

        const response = await searchUsersByRole(apiParams);
        console.log(response, "response");

        if (response && response.data) {
          // 将接口返回的数据转换为页面需要的格式
          const managerList = response.data.map(item => ({
            id: item.id,
            name: item.showName, // 使用 showName 作为显示名称
            phone: item.phone || item.mobile || "", // 手机号
            userName: item.userName || "", // 用户名
            // 保留原始数据，以备后用
            originalData: item
          }));

          this.managerOptions = managerList;

          // 如果还没有选中的店长，默认选择第一个
          if (!this.selectedManagerId && this.managerOptions.length > 0) {
            this.selectedManager = this.managerOptions[0].name;
            this.selectedManagerId = this.managerOptions[0].id;
          }
        }
      } catch (error) {
        console.error("加载店长列表失败:", error);
        Toast("加载店长列表失败，请重试");
      } finally {
        this.managerLoading = false;
      }
    },

    // 选择门店
    selectStore(store) {
      this.selectedStore = store.name;
      this.selectedStoreId = store.id;
      // 注意：提交订单时需要区分两种门店ID
      // 新增门店：使用 store.deptAddressId 或 store.isNewStore 标识
      // 已存在门店：使用 store.id (即 deptId)
      this.showStorePopup = false;
    },

    // 获取当前选中门店的正确ID（用于订单提交）
    getSelectedStoreId() {
      const selectedStore = this.storeOptions.find(
        store => store.id === this.selectedStoreId
      );
      if (selectedStore) {
        // 如果是新增门店，返回 deptAddressId
        if (selectedStore.isNewStore && selectedStore.deptAddressId) {
          return {
            id: selectedStore.deptAddressId,
            type: "deptAddressId", // 新增门店
            storeData: selectedStore
          };
        } else {
          // 已存在门店，返回 deptId
          return {
            id: selectedStore.id,
            type: "deptId", // 已存在门店
            storeData: selectedStore
          };
        }
      }
      return null;
    },

    // 选择店长
    selectManager(manager) {
      this.selectedManager = manager.name;
      this.selectedManagerId = manager.id;
      this.showManagerPopup = false;
    },

    // 选择地址
    selectAddress(address) {
      this.selectedAddress = address;
      this.showAddressPopup = false;
    },

    // 搜索地址（防抖处理）
    searchAddress() {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置新的定时器
      this.searchTimer = setTimeout(() => {
        this.addressQueryParams.no = 1; // 重置页码
        this.loadAddressList();
      }, 500); // 500ms防抖
    },

    // 搜索门店（防抖处理）
    searchStore() {
      // 清除之前的定时器
      if (this.storeSearchTimer) {
        clearTimeout(this.storeSearchTimer);
      }

      // 设置新的定时器
      this.storeSearchTimer = setTimeout(() => {
        // 更新搜索参数并重新加载门店列表
        this.storeQueryParams.shopName = this.storeQueryParams.keyword || "";
        this.loadStoreList();
      }, 500); // 500ms防抖
    },

    // 搜索店长（防抖处理）
    searchManager() {
      // 清除之前的定时器
      if (this.managerSearchTimer) {
        clearTimeout(this.managerSearchTimer);
      }

      // 设置新的定时器
      this.managerSearchTimer = setTimeout(() => {
        // 更新搜索参数并重新加载店长列表
        this.managerQueryParams.userName =
          this.managerQueryParams.keyword || "";
        this.loadManagerList();
      }, 500); // 500ms防抖
    },

    // 打开新增地址表单
    openAddAddressForm() {
      this.showAddAddressForm = true;
    },

    // 打开新增门店表单
    openAddStoreForm() {
      console.log("点击新增门店按钮");
      this.showAddStoreForm = true;
    },

    // 打开新增店长表单
    openAddManagerForm() {
      this.showAddManagerForm = true;
    },

    // 处理保存新地址
    async handleSaveAddress(saveRequest) {
      try {
        // 调用新增地址的API
        const response = await insertOrUpdateMasterOrderAddress(
          saveRequest.data
        );

        console.log("保存地址成功:", response);

        // 重新加载地址列表
        await this.loadAddressList();

        // 提示保存成功
        Toast("地址保存成功");

        // 调用成功回调，关闭弹窗
        saveRequest.onSuccess();
      } catch (error) {
        console.error("保存地址失败:", error);

        // 获取具体的错误信息
        let errorMessage = "保存地址失败，请重试";
        if (error && error.message) {
          errorMessage = error.message;
        } else if (error && error.result) {
          errorMessage = error.result;
        } else if (error && typeof error === "string") {
          errorMessage = error;
        }

        // 调用失败回调，不关闭弹窗，显示具体错误信息
        saveRequest.onError(errorMessage);
      }
    },

    // 处理保存新门店
    async handleSaveStore(saveRequest) {
      try {
        // 调用新增门店的API
        const response = await insertOrUpdateMasterOrderAddress(
          saveRequest.data
        );

        console.log("保存门店成功:", response);

        // 注意：新增的门店在接口列表中查不到，需要前端手动添加到列表
        // 新增门店使用 deptAddressId，已存在门店使用 deptId
        const newStore = {
          id: response.data || response.deptAddressId, // 使用接口返回的ID
          deptAddressId: response.data || response.deptAddressId, // 新增门店的ID字段
          name: saveRequest.data.name,
          address: `${saveRequest.data.province} ${saveRequest.data.city} ${saveRequest.data.area} ${saveRequest.data.address}`,
          province: saveRequest.data.province,
          city: saveRequest.data.city,
          area: saveRequest.data.area,
          detailAddress: saveRequest.data.address,
          isNewStore: true, // 标记为新增门店
          originalData: {
            id: response.data || response.deptAddressId,
            name: saveRequest.data.name,
            address: saveRequest.data.address,
            province: saveRequest.data.province,
            city: saveRequest.data.city,
            area: saveRequest.data.area
          }
        };

        // 将新门店添加到列表顶部
        this.storeOptions.unshift(newStore);

        // 自动选中新添加的门店
        this.selectedStore = newStore.name;
        this.selectedStoreId = newStore.id;

        // 提示保存成功
        Toast("门店添加成功");

        // 调用成功回调，关闭弹窗
        saveRequest.onSuccess();
      } catch (error) {
        console.error("保存门店失败:", error);

        // 获取具体的错误信息
        let errorMessage = "保存门店失败，请重试";
        if (error && error.message) {
          errorMessage = error.message;
        } else if (error && error.result) {
          errorMessage = error.result;
        } else if (error && typeof error === "string") {
          errorMessage = error;
        }

        // 调用失败回调，不关闭弹窗，显示具体错误信息
        saveRequest.onError(errorMessage);
      }
    },

    // 处理保存新店长
    async handleSaveManager(saveRequest) {
      try {
        // 这里可以调用保存店长的API
        console.log("保存店长数据:", saveRequest.data);

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 新增店长成功后，手动添加到列表（因为接口可能查不到新增的）
        const newManager = {
          id: Date.now(), // 使用时间戳作为临时ID
          name: saveRequest.data.displayName, // 使用显示名作为店长名称
          phone: saveRequest.data.phone,
          userName: saveRequest.data.username,
          isNewManager: true, // 标记为新增店长
          originalData: {
            showName: saveRequest.data.displayName,
            userName: saveRequest.data.username,
            phone: saveRequest.data.phone
          }
        };

        // 将新店长添加到列表顶部
        this.managerOptions.unshift(newManager);

        // 自动选中新添加的店长
        this.selectedManager = newManager.name;
        this.selectedManagerId = newManager.id;

        // 提示保存成功
        Toast("店长添加成功");

        // 调用成功回调，关闭弹窗
        saveRequest.onSuccess();
      } catch (error) {
        console.error("保存店长失败:", error);

        // 获取具体的错误信息
        let errorMessage = "保存店长失败，请重试";
        if (error && error.message) {
          errorMessage = error.message;
        }

        // 调用失败回调，不关闭弹窗，显示具体错误信息
        saveRequest.onError(errorMessage);
      }
    },

    // 查询产品详情
    async detail() {
      try {
        this.pageLoading = true;
        const res = await detail({
          id: 1019
        });
        console.log(res, "接口返回数据");

        // 将接口返回的数据赋值给组件数据
        if (res) {
          const data = res;
          this.productData = data;

          // 渲染检查项目列表
          if (data.modules && Array.isArray(data.modules)) {
            this.modules = data.modules;
            console.log("检查项目列表:", this.modules);
          }

          // 渲染配件列表
          if (data.relatedProducts && Array.isArray(data.relatedProducts)) {
            this.relatedProducts = data.relatedProducts.map(item => ({
              ...item,
              // 确保每个配件都有默认的数量和时长
              quantity: item.quantity || 1,
              // 根据产品类型设置默认时长：软件类型默认12个月，其他类型默认1个月
              period: item.period || (item.topType === 1 ? 12 : 1)
            }));
            console.log("配件列表:", this.relatedProducts);
          }
        }
      } catch (error) {
        Toast("获取订单详情失败，请重试");
      } finally {
        this.pageLoading = false;
      }
    }
  },

  // 组件挂载时初始化数据
  mounted() {
    this.detail();
  },

  // 组件销毁时清理定时器
  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
    if (this.storeSearchTimer) {
      clearTimeout(this.storeSearchTimer);
      this.storeSearchTimer = null;
    }
    if (this.managerSearchTimer) {
      clearTimeout(this.managerSearchTimer);
      this.managerSearchTimer = null;
    }
  }
};
</script>

<style scoped>
@import "./index.scss";
</style>
