<template>
  <div class="upload-file" ref="uploadFile">
    <slot :max="max">
      <grid
        :fileList="uploadList"
        :max="max"
        :disabled="disabled"
        @on-del="handleDelete"
        @on-choose="chooseFile"
      ></grid>
    </slot>
    <!-- 文件上传input,隐藏不可见 -->
    <input
      type="file"
      ref="file"
      :disabled="uploading"
      :multiple="multiple"
      :accept="accept"
      hidden
      @change="fileChange"
    />
  </div>
</template>

<script>
import grid from "./grid.vue";
import OSSUpload from "./OssUpload.js";
// import { date } from "quasar";
import moment from "moment";
import AcceptType from "./data/AcceptType.js";
import FileSuffixType from "./data/FileSuffixType.js";
import { Toast } from "vant";

let { videoAccept, imageAccept } = AcceptType;
let {
  videoSuffixList,
  docSuffixList,
  excelSuffixList,
  pptSuffixList,
  pdfSuffixList,
  zipSuffixList,
} = FileSuffixType;
// let oneKB = 1024;

let allAccept = []
  .concat(
    videoAccept,
    imageAccept,
    // pdfAccept,
    // excelAccept,
    // docAccept,
    // zipAccept,
    // txtAccept,
    // pptAccept
  )
  .join(",");
// 文件类型
const FileTypeData = {
  VIDEO: 1, // 视频
  IMAGE: 2, // 图片
  PDF: 3, // pdf
  EXCEL: 4, // excel
  WORD: 5, // word
  ZIP: 6, // zip
  PPT: 7, // ppt
  TXT: 8,
};

export default {
  components: {
    grid,
  },
  props: {
    value: {
      type: Array,
      default() {
        return [];
      },
    },
    /**
     * 是否自动上传
     */
    autoUpload: {
      type: Boolean,
      default: true,
    },
    /**
     * 上传接受类型
     */
    accept: {
      type: String,
      default: allAccept,
      // default: "*"
    },
    /**
     * 是否多选
     */
    multiple: {
      type: Boolean,
      default: true,
    },
    /**
     * 上传文件大小限制, 单位为KB, 默认值为1024, 参考GitHub上传头像设置
     * @link https://github.com/settings/profile
     */
    size: {
      type: Number,
      default: 1024 * 1024 * 10,
    },
    /**
     * 最大上传文件个数
     */
    max: {
      type: Number,
      default: 9,
    },

    /**
     * 最大上传文件个数
     */
    uploadKey: {
      type: String,
      default: "chat/" + moment().format("YYYY-MM-DD") + "/",
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      uploadList: [], //上传的文件列表
      uploading: false,
      isHighlight: false,
      // uploadKey: "storeplan/" + date.formatDate(new Date(), "YYYY-MM-DD") + "/"
    };
  },
  watch: {
    value(newVal) {
      this.uploadList = newVal;
    },
    uploadList(newFiles) {
      var flag = false;
      newFiles.forEach(item => {
        if(!item.fileurl){
          flag = true;
          return
        }
        delete item.client
      })
      if(flag) return;
      this.$emit("input", newFiles);
    },
  },
  mounted() {
    if(this.value.length) {
      this.value.forEach(item => {
        item.progress = 100;
        item.fileUrl = item.fileurl;
      });
      this.uploadList = this.value
    }
  },
  methods: {
    /**
     * 文件上传区点击调起文件选择
     * @public
     */
    chooseFile() {
      //如果 uploading 的 true，有文件正在上传，那么就不给上传
      if (this.uploading) {
        this.$Message.warning("有文件正在上传，请稍后！");
        return false;
      }
      this.$refs.file.click();
    },

    /**
     * 文件上传区点击调起文件选择
     * @public
     */
    removeFile() {},

    /**
     * 复位上传
     * @public
     */
    resetUpload() {
      this.clearFile();
      this.uploadList = [];
    },

    /**
     * 清除文件
     */
    clearFile() {
      this.$refs.file.value = "";
    },

    /**
     * 检查文件类型
     */
    checkFileType(file) {
      let ret = true;

      if (!file) return false;
      let accept = this.accept;
      if (!accept || accept == "*") {
        return true;
      }

      let acceptList = this.accept.split(",");
      ret = acceptList.some((item) => {
        return file.type.indexOf(item) > -1;
      });

      return ret;
    },

    /**
     * 检查重复文件名
     */
    checkRepeatName(file) {
      let fileNameArr = this.uploadList;
      let index = fileNameArr.findIndex((item) => item.name === file.name);
      if (index === -1) {
        return false;
      } else {
        return true;
      }
    },

    /**
     * 检查文件大小
     */
    checkFileSize(file) {
      if (file.size > this.size) {
        return true;
      }
      return false;
    },

    /**
     * 文件选择
     */
    fileChange(e) {
      // 存储需要上传的文件列表
      let fileList = Array.from(e.target.files);
      if (!fileList.length) return;

      fileList.forEach((item) => {
        // 判断文件是否到达上传最大值
        if (this.uploadList.length >= this.max) {
          let mesg = `最多上传${this.max}文件`;
          // this.$Message.info({ content: mesg });
          Toast.fail({
            duration: 2000,
            message: mesg,
          });
          this.$emit("on-error", { message: mesg });
          return;
        }

        // 检查同名文件
        if (this.checkRepeatName(item)) {
          let mesg = "存在同名文件";
          // this.$Message.info({ content: mesg });
          Toast.fail({
            duration: 2000,
            message: mesg,
          });
          this.$emit("on-error", { message: mesg });
          return;
        }
        // 验证文件大小
        if (this.checkFileSize(item)) {
          let sizeStr = this.beautifyFilesize(this.size);
          let mesg = `单个文件大小不能超过${sizeStr}`;
          Toast.fail({
            duration: 2000,
            message: mesg,
          });
          // this.$Message.info({ content: mesg });
          this.$emit("on-error", { message: mesg });
          return;
        }

        // 验证文件类型
        if (!this.checkFileType(item)) {
          let mesg = "文件类型不符合";
          Toast.fail({
            duration: 2000,
            message: mesg,
          });
          // this.$Message.info({ content: mesg });
          this.$emit("on-error", { message: mesg });
          return;
        }

        let { name, size, type } = item;

        let url = URL.createObjectURL(item); // 创建预览url
        let fileType = this._getFileType(item);
        let client = null;

        client = new OSSUpload(item, { uploadKey: this.uploadKey });

        // 上传开始
        client.on("start", (data) => {
          this.uploading = true;
          let uplaodFile = this.getUploadFile(item);
          let fid = new Date().getTime() + uplaodFile.size;
          uplaodFile.fid = fid;
          this.$emit("on-upload-start", uplaodFile, data);
        });

        // 上传中
        client.on("uploading", (data) => {
          let result = this.updateUploadData(item, data);
          this.$emit("on-upload-uploading", result, data);
        });

        // 上传结束
        client.on("complate", (data) => {
          let result = this.updateUploadData(item, data);
          this.$emit("on-upload-success", result, data);
          this.uploading = false;
        });

        // 上传出错
        client.on("error", (data) => {
          let uplaodFile = this.getUploadFile(item);
          this.$emit("on-error", uplaodFile, data);
        });

        // 存储上传文件信息
        let newData = {
          name,
          size,
          type,
          client,
          fileUrl: url,
          fileSuffix: fileType,
          duration: 0,
        };

        // 文件类型
        if (videoSuffixList.indexOf(fileType) !== -1 || this.hasVideo(item)) {
          newData.fileType = FileTypeData.VIDEO;
          this.getVideoDuration(newData.fileUrl).then((dat) => {
            newData.duration = dat;
          });
        } else if (this.hasImage(item)) {
          newData.fileType = FileTypeData.IMAGE;
        } else if (docSuffixList.indexOf(fileType) !== -1) {
          newData.fileType = FileTypeData.WORD;
        } else if (excelSuffixList.indexOf(fileType) !== -1) {
          newData.fileType = FileTypeData.EXCEL;
        } else if (pdfSuffixList.indexOf(fileType) !== -1) {
          newData.fileType = FileTypeData.PDF;
        } else if (zipSuffixList.indexOf(fileType) !== -1) {
          newData.fileType = FileTypeData.ZIP;
        } else if (pptSuffixList.indexOf(fileType) !== -1) {
          newData.fileType = FileTypeData.PPT;
        }
        // 添加文件到列表
        this.uploadList.push(newData);

        // 上传文件到服务器
        if (this.autoUpload) {
          this.upload(client);
        }
      });
      this.$emit("on-file-change", this.uploadList);
      this.clearFile();
    },

    /**
     * 上传文件
     */
    upload(client) {
      // 上传文件
      client && client.upload();
    },

    getUploadFile(file) {
      let index = -1;
      // 查找目标文件
      this.uploadList.find((item, i) => {
        if (item.name === file.name) {
          index = i;
          return true;
        }
        return false;
      });
      if (index === -1) return;
      return this.uploadList[index];
    },

    updateUploadData(file, data) {
      let index = -1;
      // 查找目标文件
      let target = this.uploadList.find((item, i) => {
        if (item.name === file.name) {
          index = i;
          return true;
        }
        return false;
      });

      if (index === -1) return;
      this.uploadList[index] = Object.assign({}, target, data);
      this.uploadList = this.uploadList.concat([]);
      return this.uploadList[index];
    },

    /**
     * 删除上传文件
     */
    handleDelete(data) {
      // 如果未上传完则先暂停下载
      if (data.progress < 100) {
        let target = this.uploadList.find((item) => {
          return item.name === data.name;
        });

        target.client.pause();
      }

      // 从上传列表中删除文件
      let list = this.uploadList.filter((item) => {
        return item.name !== data.name;
      });
      this.uploadList = list;
      this.uploading = false;
      this.$emit("del",data);
    },

    /**
     * 暂停上传文件
     */
    handlePause(data) {
      if (data.progress === 100) return;

      // 查找目标文件
      let index = 0;
      let target = this.uploadList.find((item, i) => {
        if (item.name === data.name) {
          index = i;
          return true;
        }
        return false;
      });
      // 暂停上传并更新上传状态
      target.client.pause();
      this.uploadList[index] = Object.assign({}, this.uploadList[index], {
        uploadState: "pause",
      });
      this.uploadList = this.uploadList.concat([]);
    },

    /**
     * 继续或开始上传文件
     */
    handleStart(data) {
      let target = this.uploadList.find((item) => {
        return item.name === data.name;
      });

      this.upload(target.client);
    },

    /**
     * 拖拽进入事件，不通过此事件阻止默认行为，不会触发drop事件
     */
    handleDragover(e) {
      e.preventDefault();
      this.isHighlight = true;
    },

    /**
     * 拖拽进入事件，不通过此事件阻止默认行为，不会触发drop事件
     */
    handleDrop(e) {
      e.preventDefault();
      this.isHighlight = false;
      const files = e.dataTransfer && e.dataTransfer.files;
      this.fileChange({ target: { files, value: "" } });
    },

    /**
     * 获取视频播放时长
     */
    getVideoDuration(url) {
      // Promise初始化
      let video = document.createElement("video");
      video.style.display = "none";
      this.$refs.uploadFile.appendChild(video);
      video.src = url;
      return new Promise((resolve) => {
        video.ondurationchange = () => {
          resolve(video.duration);
          this.$refs.uploadFile.removeChild(video);
        };
      });
    },

    /**
     * 格式化文件大小
     */
    beautifyFilesize(filesize) {
      if (typeof filesize !== "number") {
        throw new Error("Filesize is not number");
      }
      if (filesize < 1024) {
        return `${filesize} B`;
      } else if (filesize < 1024 * 1024) {
        return `${(filesize / 1024).toFixed(1)} KB`;
      } else if (filesize < 1024 * 1024 * 1024) {
        return `${(filesize / (1024 * 1024)).toFixed(1)} MB`;
      } else {
        return `${(filesize / (1024 * 1024 * 1024)).toFixed(1)} GB`;
      }
    },

    /**
     *判断是否是视频
     */
    hasVideo(file) {
      let { type } = file;
      let fileTypeArray = type.split("/");
      let fileType = fileTypeArray[0];

      if (fileType.toLowerCase() === "video") {
        return true;
      }

      return false;
    },

    /**
     *判断是否是视频
     */
    hasImage(file) {
      let { type } = file;
      let fileTypeArray = type.split("/");
      let fileType = fileTypeArray[0];

      if (fileType === "image") {
        return true;
      }

      return false;
    },

    /**
     *获取文件后缀
     */
    _getFileType(file) {
      // let reg = /\.[^\.]+$/;
      // let fileType = file.name.match(reg);
      let fileNameArray = file.name.split(".");
      let fileType = fileNameArray[fileNameArray.length - 1];
      return fileType;
    },
  },
};
</script>

<style lang="scss" scoped>
.upload-file {
  // width: 100%;
  // height: 100%;
  // display: flex;
  // justify-content: center;

  .upload-file-box {
  }

  .upload-box--highlight,
  .upload-box:hover {
    border-color: #2d8cf0;
  }
}
</style>
