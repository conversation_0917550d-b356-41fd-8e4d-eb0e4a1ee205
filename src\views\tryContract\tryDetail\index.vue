<template>
  <div class="try-detail">
    <navbar title="合同/订单详情" />
    <main>
      <div class="custorm-info">
        <p :style="{ color: cluingStateColor[detail.status - 1] }">
          {{
            contractStatus.find((item) => item.value == detail.status)
              ? contractStatus.find((item) => item.value == detail.status).dname
              : ""
          }}
        </p>
        <div class="custorm-name">
          <p>{{ detail.customerName }}</p>
          <div>试用合同编号：{{ detail.contractNo }}</div>
        </div>

        <div class="custorm-message">
          <div class="item">
            <label for="">销售负责人</label>
            <div class="content">{{ detail.userName }}</div>
          </div>
          <div class="item">
            <label for="">客户签约人</label>
            <div class="content">{{ detail.signUser }}</div>
          </div>
        </div>
      </div>
      <div class="basic-info">
        <van-tabs
          v-model="active"
          color="#FF9900"
          title-active-color="#FF9900"
          title-inactive-color="#7F7F7F"
          :swipeable="true"
          :animated="true"
        >
          <van-tab title="基础信息" title-style="font-size:14px;">
            <basic :info="detail" />
          </van-tab>
          <van-tab title="设备费用" title-style="font-size:14px">
            <equipmentCost :equipmentList="equipmentList" />
          </van-tab>
          <van-tab title="服务费用" title-style="font-size:14px">
            <serviceCost :seviceList="seviceList" />
          </van-tab>
          <van-tab title="施工费用" title-style="font-size:14px">
            <buildCost :buildList="buildList" />
          </van-tab>
          <van-tab title="发货信息" title-style="font-size:14px">
            <shipMessage
              :loanAddress="loanAddress"
              :deliverStatusList="deliverStatus"
            />
          </van-tab>
        </van-tabs>
      </div>
    </main>
  </div>
</template>

<script>
// api
import { getContractById, getLoanAddressList } from "@/api/tryContract";
import { getAllDict } from "@/api/delivery";
// components
import navbar from "@/components/navbar.vue";
import basic from "./components/basic.vue";
import equipmentCost from "./components/equipmentCost.vue";
import serviceCost from "./components/serviceCost.vue";
import buildCost from "./components/buildCost.vue";
import shipMessage from "./components/shipMessage.vue";
import { Tab, Tabs } from "vant";

import { findDeeply } from "../../../common/utils";
export default {
  components: {
    navbar,
    basic,
    equipmentCost,
    serviceCost,
    buildCost,
    shipMessage,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs
  },
  data() {
    return {
      active: 0,
      detail: {},
      seviceList: [],
      equipmentList: [],
      buildList: [],
      loanAddress: [],
      contractStatus: [],
      deliverStatus: [],
      cluingStateColor: [
        "red",
        "blue",
        "green",
        "red",
        "pink",
        "magenta",
        "green",
        "orange"
      ]
    };
  },
  created() {
    this.getAllDict();
  },
  mounted() {
    let { id } = this.$route.query;
    this.getContractById(id);
    this.getLoanAddressList(id);
  },
  methods: {
    async getContractById(id) {
      try {
        const res = await getContractById({
          id: id
        });
        this.detail = res;
        // 根据topType 判断一下服务费用还是设备费用 topType分类一下 1软件服务、2施工、4硬件
        this.seviceList = res.products.filter((item) => {
          return item.topType === 1;
        });
        this.equipmentList = res.products.filter((item) => {
          return item.topType === 4;
        });
        this.buildList = res.products.filter((item) => {
          return item.topType === 2;
        });
      } catch (error) {
        console.log(error);
      }
    },

    async getLoanAddressList(id) {
      try {
        const res = await getLoanAddressList({
          id: id
        });
        this.loanAddress = res || [];
      } catch (error) {
        console.log(error);
      }
    },

    /**
     * 全部字典项
     */
    async getAllDict() {
      const res = await getAllDict();
      sessionStorage.setItem("dictTree", JSON.stringify(res));
      if (res) {
        this.getOption();
      }
    },

    /**
     * 字典项
     */
    getOption() {
      let dictTree = JSON.parse(sessionStorage.getItem("dictTree"));
      const contractStatus = findDeeply(
        dictTree,
        (item) => item.type === "contract_status"
      );
      const deliverStatus = findDeeply(
        dictTree,
        (item) => item.type === "deliver_status"
      );

      this.contractStatus = [...contractStatus.children] || [];
      this.deliverStatus = [...deliverStatus.children] || [];
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>