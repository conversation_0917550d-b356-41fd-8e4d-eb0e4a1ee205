<template>
  <van-popup
    v-model="show"
    position="right"
    :style="{ height: '100%', width: '100%' }"
  >
    <div class="popup">
      <div class="header">
        <img
          src="../../../assets/img/ico_back-t.png"
          alt=""
          @click="show = false"
        />
        <div class="title">筛选</div>
        <div></div>
      </div>
      <div class="popup_main">
        <div class="item">
          <p>延期申请单号</p>
          <div class="v-input">
            <input
              :value="delayNo"
              @input="
                event => {
                  delayNo = event.target.value;
                }
              "
              type="text"
              placeholder="请输入延期申请单号"
            />
          </div>
        </div>

        <div class="item">
          <p>客户名称</p>
          <div class="v-input">
            <input
              :value="customerName"
              @input="
                event => {
                  customerName = event.target.value;
                }
              "
              type="text"
              placeholder="请输入客户名称"
            />
          </div>
        </div>

        <div class="item">
          <p>门店名称</p>
          <div class="v-input">
            <input
              :value="deptName"
              @input="
                event => {
                  deptName = event.target.value;
                }
              "
              type="text"
              placeholder="请输入门店名称"
            />
          </div>
        </div>

        <div class="item">
          <p>状态</p>
          <div class="item-status">
            <div
              class="item-list"
              :class="activeIndices.includes(e.value) ? 'active-item-list' : ''"
              v-for="(e, eIndex) in statusList"
              :key="eIndex"
              @click="changeStatus(e, e.value)"
            >
              {{ e.dname }}
            </div>
          </div>
        </div>
      </div>
      <div class="footer">
        <van-button class="resect" @click="reset">重置</van-button>
        <van-button class="sure" color="#FF9900" @click="sure">确认</van-button>
      </div>
    </div>
  </van-popup>
</template>

<script>
import { Popup, Button, DatetimePicker } from "vant";
export default {
  components: {
    [Popup.name]: Popup,
    [Button.name]: Button,
    [DatetimePicker.name]: DatetimePicker
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeIndices: [],
      show: false,
      delayNo: "",
      customerName: "",
      deptName: "",
      statusList: [
        { value: 1, dname: "草稿" },
        { value: 2, dname: "审批中" },
        { value: 3, dname: "已生效" },
        { value: 4, dname: "已失效" },
        { value: 5, dname: "驳回" }
      ]
    };
  },
  watch: {
    value(val) {
      this.show = val;
    },
    show(val) {
      this.$emit("input", val);
    }
  },

  mounted() {},

  methods: {
    // 查询
    sure() {
      this.$emit("sure", {
        delayNo: this.delayNo,
        customerName: this.customerName,
        deptName: this.deptName,
        state: this.activeIndices.join(",")
      });
      this.show = false;
    },

    // 重置
    reset() {
      this.delayNo = "";
      this.customerName = "";
      this.deptName = "";
      this.activeIndices = [];
      this.sure();
    },

    // 改变状态
    changeStatus(e, value) {
      const index = this.activeIndices.indexOf(value);
      if (index >= 0) {
        this.activeIndices.splice(index, 1);
      } else {
        this.activeIndices.push(value);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.popup {
  padding: 0 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  .header {
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    img {
      width: 20px;
      height: 20px;
    }

    .title {
      width: 96px;
      height: 24px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #000000;
      text-align: center;
      line-height: 24px;
    }
  }

  .popup_main {
    flex: 1;
    .item {
      margin: 8px 0;

      p {
        height: 44px;
        line-height: 44px;
        font-family: PingFangSC-SNaNpxibold;
        font-weight: 600;
        font-size: 14px;
        color: #000000;
      }

      .v-input {
        height: 40px;
        background: #f7f7f7;
        border-radius: 6px;
        display: flex;
        align-items: center;
        padding: 0 12px;

        input {
          height: 33px;
          width: 100%;
          background: #f7f7f7;
          border: none;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 15px;
          color: #7f7f7f;
        }

        input::-webkit-input-placeholder {
          /* WebKit browsers，webkit内核浏览器 */
          color: #a1a1a1;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 15px;
          color: #b2b2b2;
          letter-spacing: 0;
        }
      }

      .item-date {
        display: flex;
        align-items: center;
        .v-date {
          width: 159px;
          height: 40px;
          background: #f7f7f7;
          border-radius: 6px;
          display: flex;
          align-items: center;
          padding: 0 12px;

          input {
            height: 33px;
            width: 88%;
            background: #f7f7f7;
            border: none;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 15px;
            color: #7f7f7f;
          }

          input::-webkit-input-placeholder {
            /* WebKit browsers，webkit内核浏览器 */
            color: #a1a1a1;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 15px;
            color: #b2b2b2;
            letter-spacing: 0;
            text-align: center;
          }
        }

        .line {
          width: 15px;
          height: 1px;
          background: #e5e5e5;
          margin: 0 6px;
        }
      }
    }

    .item-status {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .item-list {
        box-sizing: border-box;
        width: 106px;
        height: 40px;
        background: #f7f7f7;
        border-radius: 6px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #7f7f7f;
        margin-bottom: 12px;
      }

      .active-item-list {
        box-sizing: border-box;
        font-weight: 500;
        background: #ff99001a;
        color: #ff9900;
        border: 1px solid #ff9900;
      }
    }
  }

  .footer {
    width: 100%;
    height: 52px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .resect {
      width: 120px;
      height: 44px;
      background: #ffffff;
      border: 0.5px solid #e5e5e5;
      border-radius: 8px;
      font-size: 17px;
    }

    .sure {
      width: 215px;
      height: 44px;
      background: #ff9900;
      border-radius: 8px;
      font-size: 17px;
    }
  }
}
</style>
