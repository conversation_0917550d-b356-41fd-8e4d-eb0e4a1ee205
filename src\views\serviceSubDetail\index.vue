<template>
  <div class="servicePage">
    <!-- nav -->
    <navbar title="详情页" :routerLink="true" @goBack="goBack" />
    <!-- main -->
    <main>
      <div class="price-presentation">
        <img
          v-if="data.productMainUrl"
          class="presentation"
          :src="data.productMainUrl"
          alt=""
        />
        <img
          class="presentation"
          v-else
          src="../../assets/service/zw.png"
          alt=""
        />
        <!-- 未优惠 -->
        <div v-if="!countdown" class="undiscounted">
          <div class="price-top">
            <span class="unit">￥</span>
            <span class="price-num">{{ data.amount }}</span>
            <span>起</span>
          </div>
          <p class="product-name">{{ data.productName }}</p>
        </div>
        <!-- 优惠 -->
        <div class="price" v-else>
          <div class="price-top">
            <div>
              <span class="unit">￥</span>
              <span class="price-num">{{ data.amount }}</span>
              <span>起</span>
            </div>
            <div class="text">
              <p style="margin-bottom: 6px">{{ data.strategyName }}</p>
              <span>{{ countdown }}</span>
            </div>
          </div>
          <p class="product-name">{{ data.productName }}</p>
        </div>
      </div>

      <div class="select" @click="changePopup">
        <p>
          已选：{{
            setMeal[selectIndex] ? setMeal[selectIndex].productPackageName : ""
          }}
        </p>
        <img
          class="icon"
          src="../../assets/service/ico_rightarrow.png"
          alt=""
        />
      </div>
      <!-- 详情 -->
      <div class="product-detail">
        <p>详情</p>
        <div v-for="(item, index) in productDescribe" :key="index">
          <div class="detail-txt" v-show="item.type == 'text'">
            {{ item.content }}
          </div>
          <div
            v-for="(e, eIndex) in item.fileList"
            :key="eIndex"
            v-show="item.type == 'image'"
          >
          <van-image  class="product-detail-img" :src="e.fileurl" fit="cover" />
          </div>
        </div>
      </div>
    </main>
    <!-- footer -->
    <footer>
      <button class="btn" @click="showPopup = true">
        {{ data.amount }} 开通试用
      </button>
    </footer>

    <!-- Popup -->
    <van-popup
      v-model="showPopup"
      position="bottom"
      round
      :style="{ height: '60%' }"
    >
      <div class="pop">
        <div class="popHeader">
          <div class="popHeader-top">
            <span></span>
            <img
              src="../../assets/service/ico_close.png"
              alt=""
              @click="showPopup = false"
            />
          </div>

          <!-- 地址 -->
          <div class="address" @click.stop="changeAddress">
            <div class="address-txt">
              <p>
                {{ addressObj.district ? addressObj.district : "省市区地址" }}
              </p>
              <p class="content">
                {{ addressObj.address ? addressObj.address : "地址信息" }}
              </p>
              <p>
                {{ addressObj.recipient ? addressObj.recipient : "姓名" }}
                {{ addressObj.mobile ? addressObj.mobile : "手机号" }}
              </p>
            </div>
            <img
              class="icon"
              src="../../assets/service/ico_rightarrow.png"
              alt=""
            />
          </div>
        </div>

        <div class="popContent">
          <!-- 产品 -->
          <div class="product">
            <img
              class="product-img"
              src="../../assets/service/moren.png"
              alt=""
            />
            <div class="product-message">
              <p class="title">
                {{
                  setMeal[selectIndex]
                    ? setMeal[selectIndex].productPackageName
                    : ""
                }}
              </p>
              <p class="content">
                {{
                  setMeal[selectIndex]
                    ? setMeal[selectIndex].setMealContent
                    : ""
                }}
              </p>
              <p class="price">
                ￥{{
                  setMeal[selectIndex] && setMeal[selectIndex].flag
                    ? setMeal[selectIndex] && setMeal[selectIndex].amount
                    : setMeal[selectIndex] && setMeal[selectIndex].unitAmount
                }}
              </p>
            </div>
          </div>

          <!-- 套餐选择 -->
          <div class="setMeal">
            <p>套餐选择</p>
            <div class="set-product">
              <div
                v-for="(e, eIndex) in setMeal"
                :key="eIndex"
                @click="changeProduct(eIndex)"
                :class="
                  selectIndex == eIndex
                    ? 'active-set-product-list'
                    : 'set-product-list'
                "
              >
                {{ e.productPackageName }}
              </div>
            </div>
          </div>
        </div>
        <div class="popFooter">
          <button class="btn" @click="placeOrder">
            立即支付
            {{
              setMeal[selectIndex] && setMeal[selectIndex].flag
                ? setMeal[selectIndex] && setMeal[selectIndex].amount
                : setMeal[selectIndex] && setMeal[selectIndex].unitAmount
            }}
          </button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
// api
import {
  getProductDetail,
  getPackageDetail,
  saveOrder,
  payH5
} from "../../api/serviceSubDetail";
// components
import navbar from "../../components/navbar.vue";
import { Popup, Toast } from "vant";
import { Image as VanImage } from 'vant';
export default {
  components: {
    navbar,
    [Popup.name]: Popup,
    [Toast.name]: Toast,
    [VanImage.name]: VanImage
  },
  data() {
    return {
      selectIndex: 0,
      showPopup: false,
      productDescribe: [],
      data: {},
      countdown: "", // 倒计时
      timer: null, // 用于存储定时器
      setMeal: [], //套餐
      addressObj: {},
      selectProduct: {}
    };
  },
  mounted() {
    let { id } = this.$route.query;
    id && this.getProductDetail(id);
    let path = sessionStorage.getItem("path");
    if (path == "addressManage") {
      this.showPopup = true;
      sessionStorage.removeItem("path");
    }
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    clearInterval(this.timer);
  },
  methods: {
    //服务订阅产品详情
    async getProductDetail(detailId) {
      const { recipient, mobile, district, address, isChange, id } =
        this.$route.params;

      try {
        const res = await getProductDetail({
          id: detailId
        });
        this.productDescribe = res && res.productDescribe ? JSON.parse(res.productDescribe) : [];
        this.data = res;
        res?.packageList.forEach((item) => {
          const getProductNames = (list) =>
            list
              ?.map(
                (e) =>
                  (e.nums && e.nums > 0 ? e.nums + "个" : "") + e.productName
              )
              .join("");

          let productTypes = [
            "hardwareList",
            "constructionList",
            "serviceList"
          ];
          productTypes.forEach((type) => {
            item[`${type}Content`] = getProductNames(item[type]);
          });
          item.setMealContent = productTypes
            .map((type) => item[`${type}Content`])
            .filter(Boolean)
            .join("、");
        });
        this.setMeal = res.packageList;
        // 如果是从地址管理页面跳转过来的，需要将地址信息带入 不需要取默认地址
        if (isChange) {
          this.addressObj = { recipient, mobile, district, address, id };
        } else {
          if (res.addressList.length > 0) {
            this.addressObj = res.addressList ? res.addressList[0] : {};
            this.addressObj.district = this.addressObj?.district
              ? this.addressObj?.district?.replace(/\//g, " ")
              : "";
          }
        }

        if (res.strategyId) {
          this.startCountdown(res);
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 外层倒计时显示
    startCountdown(val) {
      // 这里我手写一个到期时间，方便测试
      // let effectiveEndTime = "2024-07-10 10:17:30";
      this.timer = setInterval(() => {
        const now = new Date();
        const end = new Date(val.effectiveEndTime);
        const diff = end - now;

        if (diff > 0) {
          const days = Math.floor(diff / 1000 / 60 / 60 / 24);
          const hours = Math.floor((diff / 1000 / 60 / 60) % 24);
          const minutes = Math.floor((diff / 1000 / 60) % 60);
          const seconds = Math.floor((diff / 1000) % 60);

          this.countdown = "";
          if (days > 10) {
            this.countdown = "10+天后结束";
          } else if (days > 0) {
            this.countdown = `${days}天后结束`;
          } else {
            if (hours > 0) this.countdown += `${hours}小时`;
            if (minutes > 0) this.countdown += `${minutes}分钟`;
            if (seconds > 0) this.countdown += `${seconds}秒`;
            this.countdown += "后结束";
          }
        } else {
          // 倒计时结束，清除定时器
          this.data.amount = val.unitAmount;
          this.countdown = "";
          clearInterval(this.timer);
        }
      }, 100);
    },

    // 下单前调用接口查看最新价格
    async placeOrder() {
      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        loadingType: "spinner",
        duration: 0 // 持续展示 toast
      });
      try {
        const res = await getPackageDetail({
          id: this.data.id, //产品id
          packageId: this.setMeal[this.selectIndex].id //套餐id
        });
        if (res) {
          this.saveOrder(res);
        }
      } catch (error) {
        if (error) {
          Toast.clear();
        }
      }
    },

    // 下单接口
    async saveOrder(val) {
      try {
        const res = await saveOrder({
          trialProductId: this.data.id, //产品id
          trialPackageId: this.setMeal[this.selectIndex].id, //套餐id
          addressId: this.addressObj.id, //地址id
          price: val.flag ? val.amount : val.unitAmount //优惠生效 true:总价 false:原价
        });
        if (res) {
          let userAgent = navigator.userAgent;
          let client = /(Android)/i.test(userAgent)
            ? "Android"
            : /(iPhone|iPad|iPod|iOS|Mac|Mac)/i.test(userAgent)
            ? "iOS"
            : "Unknown";

          if (client !== "Unknown") {
            let params = {
              orderName: res.orderName,
              orderNo: res.orderNo,
              price: val.flag ? val.amount : val.unitAmount,
              client: client
            };
            this.payH5(params);
          }
        }
      } catch (error) {
        if (error) {
          Toast.clear();
        }
      }
    },

    // 拉起支付
    async payH5(val) {
      try {
        const res = await payH5({
          orderName: val.orderName,
          orderNo: val.orderNo,
          client: val.client,
          minute: "5",
          price: val.price * 100 //单位是分 所以*100
        });
        if (res) {
          Toast.clear();
          window.location.href = res;
        }
      } catch (error) {
        if (error) {
          Toast.clear();
        }
      }
    },

    changeProduct(index) {
      this.selectIndex = index;
    },
    changePopup() {
      this.showPopup = !this.showPopup;
    },

    // 跳转地址管理
    changeAddress() {
      let routeData = {
        name: "addressManage",
        query: {
          id: this.$route.query.id
        }
      };
      if (this.addressObj.id) {
        routeData.query.addressId = this.addressObj.id;
      }
      this.$router.push(routeData);
    },

    // serviceSubDetail 页面返回app
    goBack() {
      console.log("返回app列表");
      this.backToApp();
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>