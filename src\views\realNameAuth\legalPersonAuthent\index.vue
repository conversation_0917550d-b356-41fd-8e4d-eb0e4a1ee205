<template>
  <div class="legalPersonAuthent">
    <tabs :isActiveB="true" :message="message"></tabs>
    <main>
      <div class="tips">实名认证，以下方式任选其一</div>
      <div>
        <van-radio-group v-model="radio">
          <van-radio name="1" checked-color="#FF9900">法人手机号认证</van-radio>
          <van-radio
            name="2"
            checked-color="#FF9900"
            disabled
            style="margin-left: 32px"
            >授权书认证</van-radio
          >
        </van-radio-group>
        <div class="input-box">
          <item-input
            v-model.trim="phone"
            :label="'法人手机号'"
            :placeholder="'请输入法人手机号'"
            must
          ></item-input>
        </div>
        <div class="input-box">
          <item-input
            ref="inputVerify"
            :focusInput="'inputVerify'"
            v-model="verifyCode"
            :label="'验证码'"
            :placeholder="'请输入验证码'"
            must
          ></item-input>
          <span class="autofill" v-if="showVerify" @click="sendVcode"
            >获取验证码</span
          >
          <span class="autofill-add" v-else>重新获取({{ count }}s)</span>
        </div>
      </div>
    </main>
    <footer>
      <div class="btn-box">
        <van-button
          class="btn"
          type="default"
          color="#FF9900"
          block
          :disabled="!(phone && verifyCode)"
          @click="verifyVcode"
          >确认提交</van-button
        >
      </div>
    </footer>
  </div>
</template>

<script>
import tabs from "../components/tabs.vue";
import itemInput from "@/components/item-input.vue";
import { Button, RadioGroup, Radio, Toast } from "vant";
// api
import { sendVcode, verifyVcode } from "@/api/realNameAuth";
export default {
  components: {
    tabs,
    itemInput,
    [Button.name]: Button,
    [Radio.name]: Radio,
    [RadioGroup.name]: RadioGroup,
    [Toast.name]: Toast
  },
  data() {
    return {
      count: "",
      showVerify: true,
      timer: null,
      message: {
        stepA: "企业认证",
        stepB: "实名认证"
      },
      radio: "1",
      name: "",
      idCode: "",
      phone: "",
      account: "",
      verifyCode: "", //验证码
      vCodeKey: "",
      creditCode: ""
    };
  },
  created() {
    let { name, idCode, account, creditCode } = this.$route?.query;
    if (name && idCode && account && creditCode) {
      this.name = name;
      this.idCode = idCode;
      this.account = account;
      this.creditCode = creditCode;
    }
    this.phone = sessionStorage.getItem("mobilePhoneAuth");
  },
  methods: {
    // 实名认证获取验证码接口
    async sendVcode() {
      if (!this.phone) {
        return Toast.fail({
          duration: 2000,
          message: "请填写手机号",
          forbidClick: true
        });
      }
      Toast.loading({
        duration: 0,
        message: "获取验证中...",
        forbidClick: true
      });
      const TIME_COUNT = 60;
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.showVerify = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.showVerify = true;
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000);
      }
      try {
        const res = await sendVcode({
          mobile: this.phone,
          idName: this.name,
          idCode: this.idCode,
          account: this.account
        });
        if (res) {
          Toast.clear();
          Toast.success({
            message: "验证码已发送",
            forbidClick: true
          });
          this.$refs.inputVerify.focus();
          this.vCodeKey = res.vcodeKey;
        }
      } catch (error) {
        if (error) {
          Toast.clear();
        }
      }
    },
    // 实名认证验证验证码接口
    async verifyVcode() {
      Toast.loading({
        duration: 0,
        message: "验证中...",
        forbidClick: true
      });
      try {
        await verifyVcode({
          vCode: this.verifyCode,
          idName: this.name,
          idCode: this.idCode,
          mobile: this.phone,
          account: this.account,
          signType: 2, //0:crm合同  1:电子合同 2 实名认证
          billingType: 1,
          vCodeKey: this.vCodeKey,
          creditCode: this.creditCode //信用代码
        });
        Toast.clear();
        // 成功后回到合同 / 付款页
        let sourcePage = this.$route.query?.sourcePage;
        let tokenAuth = sessionStorage.getItem("tokenAuth");
        if (sourcePage == "contractPage") {
          this.$router.push({
            name: "contractDetail",
            query: {
              contractId: this.$route.query?.contractId,
              token: tokenAuth,
              isLogin: "1"
            }
          });
        } else if (sourcePage == "paymentLandedPage") {
          // 跳转付款页面
          this.$router.push({
            name: "paymentLanded",
            query: {
              orderNo: this.$route.query?.orderNo,
              sourcePage: "paymentLandedPage",
              token: tokenAuth,
              isLogin: "1"
            }
          });
        }
      } catch (error) {
        if (error) {
          Toast.clear();
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.legalPersonAuthent {
  background: #f7f6f8;
  display: flex;
  flex-direction: column;
  height: 100%;

  main {
    flex: 1;
    .input-box {
      background: #ffffff;
      padding: 0 16px;
      display: flex;
      align-items: center;
      position: relative;

      .autofill {
        font-size: 14px;
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: #ff9900;
        letter-spacing: 0;
        text-align: right;
        position: absolute;
        right: 16px;
      }

      .autofill-add {
        font-size: 14px;
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: #b2b2b2;
        letter-spacing: 0;
        text-align: right;
        position: absolute;
        right: 16px;
      }
    }
    .tips {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #7f7f7f;
      line-height: 20px;
      padding: 8px 16px;
    }

    .van-radio-group {
      padding: 16px;
      display: flex;
      background-color: #fff;
    }
    .van-radio {
      font-size: 14px;
    }

    .van-radio__icon {
      font-size: 16px;
    }
  }

  footer {
    z-index: 9999;
    height: 56px;
    background: #ffffff;
    box-shadow: 0 0 0 0 #f0f3fa;

    .btn-box {
      height: 100%;
      padding: 0 16px;
      display: flex;
      justify-content: center;
      align-items: center;

      .btn {
        border-radius: 21px;
      }
    }
  }
}
</style>