<template>
  <div class="basic-page">
    <div class="info">
      <div class="item">
        <label for="">签约主体</label>
        <div class="content">{{ info.customerBankName }}</div>
      </div>
      <div class="item">
        <label for="">试用时长</label>
        <div class="content">{{ info.borrowLength }}个月</div>
      </div>
      <div class="item">
        <label for="">约定归还时间</label>
        <div class="content">{{ info.returnTime }}</div>
      </div>
      <div class="item">
        <label for="">合同金额</label>
        <div class="content">¥{{ info.deposit }}</div>
      </div>
      <div class="item">
        <label for="">创建人</label>
        <div class="content">{{ info.createBy }}</div>
      </div>
      <div class="item">
        <label for="">创建日期</label>
        <div class="content">{{ info.createTime }}</div>
      </div>
    </div>
    <div class="line"></div>

    <div class="subject">
      <p>签约主体</p>
      <div class="subject-content">
        <div class="sub-item">
          <label for="">甲方(签章)</label>
          <div class="sub-content">{{ info.signUser }}</div>
        </div>
        <div class="sub-item">
          <label for="">代表签字</label>
          <div class="sub-content"></div>
        </div>
        <div class="sub-item">
          <label for="">开户银行</label>
          <div class="sub-content"></div>
        </div>
        <div class="sub-item">
          <label for="">账号</label>
          <div class="sub-content"></div>
        </div>
        <div class="sub-item">
          <label for="">纳税人识别号</label>
          <div class="sub-content"></div>
        </div>
        <div class="sub-item">
          <label for="">电话</label>
          <div class="sub-content">{{ info.loanCellphone }}</div>
        </div>
        <div class="sub-item">
          <label for="">地址</label>
          <div class="sub-content">{{ info.loanAddress }}</div>
        </div>
      </div>
      <div class="subject-content">
        <div class="sub-item">
          <label for="">乙方(签章)</label>
          <div class="sub-content"></div>
        </div>
        <div class="sub-item">
          <label for="">代表签字</label>
          <div class="sub-content"></div>
        </div>
        <div class="sub-item">
          <label for="">开户银行</label>
          <div class="sub-content"></div>
        </div>
        <div class="sub-item">
          <label for="">账号</label>
          <div class="sub-content"></div>
        </div>
        <div class="sub-item">
          <label for="">纳税人识别号</label>
          <div class="sub-content"></div>
        </div>
        <div class="sub-item">
          <label for="">电话</label>
          <div class="sub-content"></div>
        </div>
        <div class="sub-item">
          <label for="">地址</label>
          <div class="sub-content"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {};
  }
};
</script>

<style lang="scss" scoped>
.basic-page {
  .info {
    background: #ffffff;
    padding: 0 13px;
    .item {
      display: flex;
      height: 40px;
      justify-content: space-between;
      align-items: center;

      label {
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 13px;
        color: #7f7f7f;
        line-height: 20px;
      }

      .content {
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 13px;
        color: #333333;
        text-align: right;
        line-height: 20px;
      }
    }
  }

  .line {
    height: 8px;
    background: #f5f5f5;
  }

  .subject {
    padding: 1px 13px;

    p {
      width: 64px;
      height: 22px;
      font-family: PingFangSC-SNaNpxibold;
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      letter-spacing: 0;
      line-height: 22px;
      padding-top: 16px;
      margin-bottom: 12px;
    }

    .subject-content {
      border: 0.5px solid #e5e5e5;
      border-radius: 4px;
      margin-bottom: 12px;
      padding: 12px;

      .sub-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          height: 20px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #7f7f7f;
          line-height: 20px;
        }

        .sub-content {
          height: 20px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #333333;
          text-align: right;
          line-height: 20px;
        }
      }
    }
  }
}
</style>