.experience-page {
  height: 100%;
  padding: 0 16px;

  .education {
    width: 100%;
    min-height: 77px;
    border-bottom: 1px solid #ebedf0;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);

    .education-title {
      display: flex;
      justify-content: space-between;
      padding-top: 15px;

      p {
        // width: 64px;
        height: 22px;
        font-family: PingFangSC-Medium;
        font-weight: 550;
        font-size: 16px;
        color: #333333;
      }
    }

    .annotation {
      height: 18px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 10px;
      color: #b2b2b2;
      line-height: 18px;
      margin-top: 8px;
    }

    // 内容
    ul {
      //   margin-top: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      li {
        min-height: 62px;
        border-bottom: 1px solid #ebedf0;
        -webkit-transform: scaleY(1);
        transform: scaleY(1);

        .educt-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 16px;
          .educt-name {
            width: 120px;
            height: 22px;
            font-family: PingFangSC-Medium;
            font-weight: 550;
            font-size: 14px;
            color: #333333;
            line-height: 22px;
            text-overflow :ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/
            white-space :nowrap; /*让文字不换行*/
            overflow : hidden; 
          }

          .educt-arrow {
            display: flex;

            .educt-year {
              // width: 91px;
              height: 20px;
              font-family: PingFangSC-Regular;
              font-weight: 400;
              font-size: 12px;
              color: #7f7f7f;
              line-height: 20px;
            }

            /deep/.van-icon {
              font-size: 12px;
              margin-top: 4px;
              margin-left: 11px;
            }
          }
        }

        .educt-info {
          display: flex;
          align-items: center;
          //   margin-top: 4px;
          padding: 4px 0 16px 0;

          span {
            // width: 72px;
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #7f7f7f;
            line-height: 20px;
            margin-right: 12px;
          }
        }
      }

      li:last-child {
        border: none;
      }
    }
  }

  .work {
    width: 100%;
    min-height: 77px;
    border-bottom: 1px solid #ebedf0;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    padding-top: 35px;

    .work-title {
      display: flex;
      justify-content: space-between;

      p {
        // width: 64px;
        height: 22px;
        font-family: PingFangSC-Medium;
        font-weight: 550;
        font-size: 16px;
        color: #333333;
      }
    }

    // 内容
    ul {
      margin-top: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      li {
        min-height: 62px;
        border-bottom: 1px solid #ebedf0;
        -webkit-transform: scaleY(1);
        transform: scaleY(1);

        .work-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 16px;
          .work-name {
            width: 64px;
            height: 22px;
            font-family: PingFangSC-Medium;
            font-weight: 550;
            font-size: 14px;
            color: #333333;
            line-height: 22px;
            text-overflow :ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/
            white-space :nowrap; /*让文字不换行*/
            overflow : hidden; 
          }

          .work-arrow {
            display: flex;

            .work-year {
              // width: 91px;
              height: 20px;
              font-family: PingFangSC-Regular;
              font-weight: 400;
              font-size: 12px;
              color: #7f7f7f;
              line-height: 20px;
            }

            /deep/.van-icon {
              font-size: 12px;
              margin-top: 4px;
              margin-left: 11px;
            }
          }
        }

        .work-info {
          //   margin-top: 4px;
          padding: 4px 0 16px 0;

          span {
            // width: 72px;
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #7f7f7f;
            line-height: 20px;
            margin-right: 12px;
          }

          .work-main {
            margin-top: 6px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #7f7f7f;
            line-height: 22px;
          }
        }
      }

      li:last-child {
        border: none;
      }
    }
  }

  // 联系人
  .linkMan {
    width: 100%;
    min-height: 77px;
    border-bottom: 1px solid #ebedf0;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    padding-top: 35px;

    .linkMan-title {
      display: flex;
      justify-content: space-between;

      p {
        // width: 64px;
        height: 22px;
        font-family: PingFangSC-Medium;
        font-weight: 550;
        font-size: 16px;
        color: #333333;
      }
    }

    // 内容
    ul {
      margin-top: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      li {
        min-height: 62px;
        border-bottom: 1px solid #ebedf0;
        -webkit-transform: scaleY(1);
        transform: scaleY(1);

        .linkMan-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 16px;
          .linkMan-name {
            width: 64px;
            height: 22px;
            font-family: PingFangSC-Medium;
            font-weight: 550;
            font-size: 14px;
            color: #333333;
            line-height: 22px;
          }

          .linkMan-arrow {
            display: flex;

            /deep/.van-icon {
              font-size: 12px;
              margin-top: 4px;
              margin-left: 11px;
            }
          }
        }

        .linkMan-info {
          //   margin-top: 4px;
          padding: 4px 0 16px 0;

          span {
            // width: 72px;
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #7f7f7f;
            line-height: 20px;
            margin-right: 12px;
          }

          .linkMan-main {
            margin-top: 6px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #7f7f7f;
            line-height: 22px;
          }

          .linkMan-user {
            margin-top: 12px;
            background: #f7f7f7;
            border-radius: 4px;
            padding: 8px;
          }
        }
      }

      li:last-child {
        border: none;
      }
    }
  }
}
