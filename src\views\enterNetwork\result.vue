<template>
  <div class="result-box">
    <img src="../../assets/<EMAIL>" alt="" class="tips" />
    <span class="text">激活成功</span>
    <van-button type="info" color="#FF9900" class="close-btn" @click="close"
      >关闭</van-button
    >
  </div>
</template>
<script>
import { Button } from "vant";
export default {
  components: {
    [Button.name]: Button
  },
  data() {
    return {
      token: "",
      groupId: "",
      deptId: ""
    };
  },
  created() {
    this.token = this.$route.query.token;
    this.groupId = this.$route.query.groupId;
    this.deptId = this.$route.query.deptId;
  },
  methods: {
    close() {
      // window.close();
      this.$router.go(-1);
    }
  }
};
</script>
<style lang="scss" scoped>
.result-box {
  background-color: #fff;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  .tips {
    width: 73px;
    height: 73px;
    margin: 56px 0 24px 0;
  }
  .text {
    font-family: PingFangSC-Medium;
    font-weight: 700;
    font-size: 18px;
    color: #1e232e;
    line-height: 26px;
    margin-bottom: 48px;
  }
  .close-btn {
    width: 132px;
    height: 48px;
    border-radius: 24px;
  }
}
</style>
