<template>
  <div class="person">
    <van-nav-bar
      left-arrow
      @click-left="onClickLeft"
      title="实名认证"
    ></van-nav-bar>

    <tabs :isActiveA="true" :message="message"></tabs>
    <main>
      <div class="input-box">
        <item-input
          :label="'姓名'"
          :placeholder="'请输入姓名'"
          v-model.trim="name"
          must
        ></item-input>
        <span class="autofill" @click="autofill">自动填写</span>
      </div>
      <div class="input-box">
        <item-input
          :label="'身份证号'"
          :placeholder="'请输入身份证号'"
          v-model.trim="idCode"
          must
        ></item-input>
      </div>
      <p class="tips">实名信息仅用于身份认证</p>
    </main>

    <footer>
      <div class="btn-box">
        <van-button
          :disabled="nextDisabled"
          @click="next"
          class="btn"
          type="default"
          color="#FF9900"
          block
          >下一步</van-button
        >
      </div>
    </footer>

    <uploadPopup
      :showPopup="showPopup"
      @update="update"
      @personInfo="personInfo"
    ></uploadPopup>
  </div>
</template>
  
<script>
import itemInput from "@/components/item-input.vue";
import tabs from "../components/tabs.vue";
import uploadPopup from "../components/uploadPopup.vue";
import { Button, Toast, NavBar } from "vant";
// api
import {
  createBestSignAccount,
  subitEntCertification
} from "@/api/realNameAuth";
export default {
  components: {
    itemInput,
    uploadPopup,
    tabs,
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Toast.name]: Toast
  },
  data() {
    return {
      name: "",
      idCode: "",
      attachmentIDPositiveUrl: "",
      attachmentIDTheOtherSideUrl: "",
      showPopup: false,
      message: {
        stepA: "信息填写",
        stepB: "实名认证"
      }
    };
  },
  computed: {
    nextDisabled() {
      return !this.name || !this.idCode;
    }
  },
  methods: {
    autofill() {
      this.showPopup = true;
    },
    update(val) {
      this.showPopup = val;
    },

    personInfo(val) {
      if (val) {
        this.name = val.name;
        this.idCode = val.idCode;
        this.attachmentIDPositiveUrl = val?.idCardFrontUrl;
        this.attachmentIDTheOtherSideUrl = val?.idCardBackUrl;
      }
    },

    // 下一步
    next() {
      this.createBestSignAccount();
    },

    // 创建/编辑用户接口（新增/编辑企业认证下一步时调用）
    async createBestSignAccount() {
      try {
        const res = await createBestSignAccount({
          name: this.name,
          idCode: this.idCode,
          idCardFrontUrl: this.attachmentIDPositiveUrl || null,
          idCardBackUrl: this.attachmentIDTheOtherSideUrl || null,
          createType: 3,
          billingType: 1,
          linkmanRelation: 3,
          mobile: sessionStorage.getItem("mobilePhoneAuth") || ""
        });
        if (res) {
          this.subitEntCertification(res);
        }
      } catch (err) {
        console.log(err);
      }
    },

    // 企业认证提交接口
    async subitEntCertification(account) {
      Toast.loading({
        duration: 0,
        message: "加载中...",
        forbidClick: true
      });
      let params = {
        contractId: this.$route.query?.contractId,
        idName: this.name,
        idCode: this.idCode,
        attachmentIDPositiveUrl: this.attachmentIDPositiveUrl || null,
        attachmentIDTheOtherSideUrl: this.attachmentIDTheOtherSideUrl || null,
        isLegalPerson: 0, //是否是法人 0否 1是
        cardType: 3, //1企业法人 2企业经办人 3个人
        customerBankId: this.$route.query?.customerBankId,
        billingType: 1,
        signType: 2,
        mobile: sessionStorage.getItem("mobilePhoneAuth") || "",
      };
      try {
        const res = await subitEntCertification(params);
        if (res) {
          Toast.clear();
          this.$router.push({
            name: "personAuthentNext",
            query: {
              name: this.name || "",
              idCode: this.idCode || "",
              account: account,
              contractId: this.$route.query?.contractId,
              orderNo: this.$route.query?.orderNo,
              sourcePage: this.$route.query?.sourcePage,
            }
          });
        }
      } catch (err) {
        if (err) {
          Toast.clear();
        }
      }
    },

    // 返回上一页
    onClickLeft() {
      this.$router.go(-1);
    }
  }
};
</script>
  
<style lang="scss" scoped>
.person {
  background: #f7f6f8;
  display: flex;
  flex-direction: column;
  height: 100%;

  main {
    flex: 1;
    .input-box {
      background: #ffffff;
      padding: 0 16px;
      display: flex;
      align-items: center;
      position: relative;
    }
    .tips {
      height: 20px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #7f7f7f;
      line-height: 20px;
      padding: 8px 16px;
    }
    .autofill {
      font-size: 14px;
      height: 20px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      color: #ff9900;
      letter-spacing: 0;
      text-align: right;
      position: absolute;
      right: 16px;
    }
  }

  footer {
    z-index: 9999;
    height: 56px;
    background: #ffffff;
    box-shadow: 0 0 0 0 #f0f3fa;

    .btn-box {
      height: 100%;
      padding: 0 16px;
      display: flex;
      justify-content: center;
      align-items: center;

      .btn {
        border-radius: 21px;
      }
    }
  }
}
</style>