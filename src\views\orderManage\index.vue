<template>
  <div class="order-manage">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="订单管理"
      left-arrow
      @click-left="onClickLeft"
      fixed
      placeholder
    />

    <!-- 搜索框 -->
    <div class="search-container">
      <van-search
        v-model="searchValue"
        placeholder="搜索产品名称搜索"
        @search="onSearch"
        @clear="onClear"
      />
    </div>

    <!-- 筛选器 -->
    <div class="filter-container">
      <van-dropdown-menu>
        <van-dropdown-item
          v-model="filterStatus"
          :options="statusOptions"
          @change="onFilterChange"
        />
        <van-dropdown-item
          v-model="filterProductType"
          :options="productTypeOptions"
          @change="onFilterChange"
        />
        <van-dropdown-item
          v-model="filterProductSubType"
          :options="productSubTypeOptions"
          @change="onFilterChange"
        />
      </van-dropdown-menu>
    </div>

    <!-- 订单列表 -->
    <div class="order-list">
      <van-pull-refresh
        v-model="refreshing"
        @refresh="onRefresh"
        success-text="刷新成功"
      >
        <div class="order-data-list">
          <!-- 空状态 -->
          <van-empty
            v-if="!loading && !refreshing && orderList.length === 0"
            description="暂无订单数据"
            image="search"
          />

          <div
            v-for="(order, index) in orderList"
            :key="`${order.id || index}`"
            class="order-item"
          >
            <div class="order-header">
              <div class="order-title">{{ order.title }}</div>
              <div class="order-status" :class="getStatusClass(order.status)">
                {{ order.statusText }}
              </div>
            </div>
            <div class="order-time">创建时间：{{ order.createTime }}</div>

            <div class="order-info">
              <div class="info-row">
                <div class="info-item">
                  <div class="info-label">产品类型</div>
                  <div class="info-value">{{ order.productType }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">产品小类</div>
                  <div class="info-value">{{ order.productSubType }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">订单类型</div>
                  <div class="info-value">{{ order.orderType }}</div>
                </div>
              </div>
            </div>

            <div class="order-footer">
              <div class="price-info">
                <div class="total-price">
                  应结金额：<span class="price">¥{{ order.totalAmount }}</span>
                </div>
                <div class="paid-price">收付金额：¥{{ order.paidAmount }}</div>
              </div>
              <div class="duration">时长：{{ order.duration }}</div>
            </div>
          </div>
        </div>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
import {
  NavBar,
  Search,
  DropdownMenu,
  DropdownItem,
  Empty,
  PullRefresh
} from "vant";

export default {
  name: "orderManage",
  components: {
    [NavBar.name]: NavBar,
    [Search.name]: Search,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    [Empty.name]: Empty,
    [PullRefresh.name]: PullRefresh
  },
  data() {
    return {
      searchValue: "",
      loading: false,
      refreshing: false,
      orderList: [],

      // 筛选器数据
      filterStatus: 0,
      filterProductType: 0,
      filterProductSubType: 0,

      // 筛选选项
      statusOptions: [
        { text: "订单状态", value: 0 },
        { text: "待支付", value: 1 },
        { text: "冻结中", value: 2 },
        { text: "待确认", value: 3 },
        { text: "已完成", value: 4 }
      ],
      productTypeOptions: [
        { text: "产品类型", value: 0 },
        { text: "软件", value: 1 },
        { text: "硬件", value: 2 }
      ],
      productSubTypeOptions: [
        { text: "产品小类", value: 0 },
        { text: "巡店", value: 1 },
        { text: "订购", value: 2 }
      ],

      // 模拟数据生成函数
      mockDataGenerator: null
    };
  },
  mounted() {
    this.initMockData();
    this.loadInitialData();
  },
  methods: {
    // 初始化模拟数据
    initMockData() {
      const baseData = [
        {
          id: 1,
          title: "手机巡店服务(2000家门店)",
          status: 1,
          statusText: "待支付",
          createTime: "2025-07-11 11:25:12",
          productType: "软件",
          productSubType: "巡店",
          orderType: "订购",
          totalAmount: "800.00",
          paidAmount: "120.00",
          duration: "12月"
        },
        {
          id: 2,
          title: "P2 2.8mm",
          status: 2,
          statusText: "冻结中",
          createTime: "2025-07-11 11:25:12",
          productType: "软件",
          productSubType: "巡店",
          orderType: "订购",
          totalAmount: "800.00",
          paidAmount: "120.00",
          duration: "12月"
        },
        {
          id: 3,
          title: "S1010-8P 8口百兆POE交换机",
          status: 3,
          statusText: "待确认",
          createTime: "2025-07-11 11:25:12",
          productType: "软件",
          productSubType: "巡店",
          orderType: "订购",
          totalAmount: "800.00",
          paidAmount: "120.00",
          duration: "12月"
        },
        {
          id: 4,
          title: "智能监控设备",
          status: 4,
          statusText: "已完成",
          createTime: "2025-07-10 09:30:25",
          productType: "硬件",
          productSubType: "订购",
          orderType: "订购",
          totalAmount: "1200.00",
          paidAmount: "1200.00",
          duration: "24月"
        },
        {
          id: 5,
          title: "云存储服务",
          status: 1,
          statusText: "待支付",
          createTime: "2025-07-09 14:15:30",
          productType: "软件",
          productSubType: "订购",
          orderType: "订购",
          totalAmount: "500.00",
          paidAmount: "0.00",
          duration: "6月"
        }
      ];

      // 生成更多模拟数据
      this.mockDataGenerator = [];
      for (let i = 0; i < 50; i++) {
        const baseIndex = i % baseData.length;
        const item = { ...baseData[baseIndex] };
        item.id = i + 1;
        item.title = `${item.title} (${i + 1})`;
        item.createTime = this.generateRandomTime();
        this.mockDataGenerator.push(item);
      }
      this.totalCount = this.mockDataGenerator.length;
    },

    // 生成随机时间
    generateRandomTime() {
      const start = new Date("2025-07-01");
      const end = new Date("2025-07-16");
      const randomTime = new Date(
        start.getTime() + Math.random() * (end.getTime() - start.getTime())
      );
      return randomTime
        .toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit"
        })
        .replace(/\//g, "-");
    },

    // 返回按钮点击
    onClickLeft() {
      this.$router.go(-1);
    },

    // 搜索
    onSearch(value) {
      this.searchValue = value;
      this.refreshList();
    },

    // 清空搜索
    onClear() {
      this.searchValue = "";
      this.refreshList();
    },

    // 筛选器变化
    onFilterChange() {
      this.refreshList();
    },

    // 下拉刷新
    onRefresh() {
      // 模拟刷新延迟
      setTimeout(() => {
        this.loadFilteredData();
        this.refreshing = false;
      }, 1000);
    },

    // 加载筛选后的数据
    loadFilteredData() {
      const filteredData = this.getFilteredData();
      this.orderList = filteredData;
    },

    // 初始化数据
    loadInitialData() {
      this.loadFilteredData();
    },

    // 刷新列表
    refreshList() {
      this.loadFilteredData();
    },

    // 获取过滤后的数据
    getFilteredData() {
      let filteredData = [...this.mockDataGenerator];

      // 搜索过滤
      if (this.searchValue) {
        filteredData = filteredData.filter(item =>
          item.title.toLowerCase().includes(this.searchValue.toLowerCase())
        );
      }

      // 状态过滤
      if (this.filterStatus > 0) {
        filteredData = filteredData.filter(
          item => item.status === this.filterStatus
        );
      }

      // 产品类型过滤
      if (this.filterProductType > 0) {
        const productTypeText = this.productTypeOptions.find(
          opt => opt.value === this.filterProductType
        )?.text;
        if (productTypeText) {
          filteredData = filteredData.filter(
            item => item.productType === productTypeText
          );
        }
      }

      // 产品小类过滤
      if (this.filterProductSubType > 0) {
        const productSubTypeText = this.productSubTypeOptions.find(
          opt => opt.value === this.filterProductSubType
        )?.text;
        if (productSubTypeText) {
          filteredData = filteredData.filter(
            item => item.productSubType === productSubTypeText
          );
        }
      }

      return filteredData;
    },

    // 根据筛选条件过滤数据（用于刷新）
    filterData() {
      this.loadFilteredData();
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        1: "status-pending", // 待支付
        2: "status-frozen", // 冻结中
        3: "status-confirm", // 待确认
        4: "status-completed" // 已完成
      };
      return statusMap[status] || "";
    }
  }
};
</script>

<style lang="scss" scoped>
.order-manage {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .search-container {
    padding: 12px 16px;
    background-color: #fff;

    .van-search {
      padding: 0;
      background-color: #f7f8fa;

      .van-search__content {
        background-color: #f7f8fa;
        border-radius: 20px;
      }
    }
  }

  .filter-container {
    background-color: #fff;
    border-bottom: 1px solid #ebedf0;

    .van-dropdown-menu {
      .van-dropdown-menu__bar {
        height: 44px;

        .van-dropdown-menu__item {
          font-size: 14px;
          color: #323233;
        }
      }
    }
  }

  .order-list {
    flex: 1;
    overflow: hidden;

    .van-pull-refresh {
      height: 100%;
      overflow-y: auto;
    }

    .order-data-list {
      padding: 12px 16px;
    }

    .van-empty {
      padding: 60px 0;
    }

    // 保持您的原有订单项样式
    .order-item {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .order-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;

        .order-title {
          width: 388rpx;
          height: 48rpx;
          font-family: PingFangSC-Medium;
          font-weight: 550;
          font-size: 32rpx;
          color: #000000;
          line-height: 48rpx;
        }

        .order-status {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 4px;
          white-space: nowrap;

          &.status-pending {
            color: #ff976a;
            background-color: #fff7f0;
          }

          &.status-frozen {
            color: #969799;
            background-color: #f2f3f5;
          }

          &.status-confirm {
            color: #1989fa;
            background-color: #f0f8ff;
          }

          &.status-completed {
            color: #07c160;
            background-color: #f0f9f0;
          }
        }
      }

      .order-time {
        font-size: 12px;
        color: #969799;
        margin-bottom: 16px;
      }

      .order-info {
        padding: 12px 16px;
        background: #f7f7f7;
        border-radius: 8px;
        margin-bottom: 12px;

        .info-row {
          display: flex;
          justify-content: space-between;

          .info-item {
            flex: 1;

            .info-label {
              font-size: 12px;
              color: #969799;
              margin-bottom: 4px;
            }

            .info-value {
              font-size: 14px;
              color: #323233;
            }
          }
        }
      }

      .order-footer {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        .price-info {
          .total-price {
            font-size: 14px;
            color: #323233;
            margin-bottom: 4px;

            .price {
              height: 44px;
              font-family: D-DIN-PRO-Medium;
              font-weight: 550;
              font-size: 18px;
              color: #000000;
              text-align: right;
            }
          }

          .paid-price {
            font-size: 12px;
            color: #969799;
          }
        }

        .duration {
          font-size: 12px;
          color: #969799;
        }
      }
    }
  }

  // 列表加载状态
  .van-list {
    .van-list__finished-text {
      color: #969799;
      font-size: 12px;
    }
  }
}

// 下拉菜单样式覆盖
.van-dropdown-item {
  .van-dropdown-item__option {
    &.van-dropdown-item__option--active {
      color: #1989fa;
    }
  }
}
</style>
