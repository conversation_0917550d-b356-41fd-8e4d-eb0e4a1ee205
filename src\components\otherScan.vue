<template>
  <div class="otherScan-page">
    <van-nav-bar title="付款"></van-nav-bar>
    <div class="img-box">
      <img src="../assets/img/img_wxzfb.png" alt="" />
      <p>请使用微信或支付宝扫描</p>
    </div>
  </div>
</template>

<script>
import { NavBar, Button, Divider } from "vant";
export default {
  components: {
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Divider.name]: Divider,
  },
  data() {
    return {};
  },
  watch: {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.otherScan-page {
  height: 100%;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;

  .img-box {
     height: 306px;
     display: flex;
     justify-content: center;
     align-items: center;
     flex-direction: column;
    img {
        width: 136px;
        height: 136px; 
    }
    
    p {
        margin-top: 36px;
        width: 143px;
        height: 18px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 13px;
        color: #7F7F7F;
    }
  }
}
</style>