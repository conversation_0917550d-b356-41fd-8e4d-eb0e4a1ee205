<template>
  <div class="logistics-search-page">
    <!-- header -->
    <header>
      <van-nav-bar title="物流详情" left-arrow @click-left="onClickLeft" />
      <div class="search">
        <van-field
          v-model="keyword"
          rows="1"
          placeholder="输入客户名称/合同号/物流单号查询物流进度"
          clearable
          left-icon="search"
          @keydown.enter="search"
        />
      </div>
    </header>

    <!-- main -->
    <main>
      <!-- 未输入时候内容显示 -->
      <div class="main-tips" v-if="result.length == 0">
        <img class="img" src="../../../assets/kb.png" />
        <p>上方搜索关键词，快速查询物流信息～</p>
      </div>

      <!-- 有数据显示 -->
      <div class="logist-content" v-if="result.length > 0">
        <div class="logist-content-title">{{ total }}个包裹已发出</div>
        <div
          class="logist-content-list"
          v-for="(item, index) in result"
          :key="index"
          @click="goDetail(item)"
        >
          <div class="logist-title">
            <label>{{ item.logisticsCompany }}</label>
            <span>主单号：{{ item.logisticsNumber | numSplit }}</span>
          </div>
          <div class="logist-item">
            <label>合同编号</label>
            <span>{{ item.contractNo || "--" }}</span>
          </div>
          <div class="logist-item">
            <label>客户名称</label>
            <span>{{ item.customerName || "--" }}</span>
          </div>
          <div class="logist-item">
            <label>收货人</label>
            <span>{{ item.linkMan || "--" }}</span>
          </div>
          <div class="logist-item">
            <label>收货地址</label>
            <span>{{ item.receiveAddress || "--" }}</span>
          </div>
        </div>

        <infinite-loading
          spinner="spiral"
          @infinite="infiniteHandler"
          :distance="10"
          class="infinite-loading-wrap"
        >
          <div slot="no-more" class="no-more">到底了~</div>
          <div slot="no-results" class="no-more">我是有底线的~</div>
        </infinite-loading>
      </div>
    </main>
  </div>
</template>
    
<script>
// api
import { searchLogisticsList } from "../../../api/logistics";
// components
import { NavBar, Field, Loading, Icon } from "vant";

import InfiniteLoading from "vue-infinite-loading";
export default {
  components: {
    [NavBar.name]: NavBar,
    [Field.name]: Field,
    [Loading.name]: Loading,
    [Icon.name]: Icon,
    InfiniteLoading
  },
  data() {
    return {
      keyword: "",
      pageNumber: 1,
      result: [],
      total: 0,
      busy: true
    };
  },
  computed: {},

  created() {
    // 默认进来搜索当前手机号的物流信息
    this.search();
  },
  methods: {
    // 搜索
    async search() {
      this.result = [];
      try {
        const res = await searchLogisticsList({
          keyword: this.keyword,
          pageNumber: 1,
          pageSize: 10
        });
        if (res) {
          this.result.push(...res.list); //追加数据
          this.total = res?.total;
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 无限滚动
    async infiniteHandler($state) {
      const res = await searchLogisticsList({
        keyword: this.keyword,
        pageNumber: this.pageNumber,
        pageSize: 10
      });
      if (res.list.length > 0) {
        this.pageNumber += 1; // 下一页
        // 过滤掉重复的元素
        const newList = res.list.filter(
          (item) =>
            !this.result.some((resultItem) => resultItem.outId === item.outId)
        );
        this.result = this.result.concat(newList);
        $state.loaded();
      } else {
        $state.complete();
      }
    },

    /**
     * 查看详情
     */
    goDetail(obj) {
      const query = {
        id: obj.outId,
        keyword: this.keyword
      };
      if (obj.logisticsCompany && obj.logisticsCompany.indexOf("顺丰") !== -1) {
        query.customerName = obj.linkPhone.substring(obj.linkPhone.length - 4);
      }

      if (obj.logisticsCompany && obj.logisticsCompany.indexOf("跨越") !== -1) {
        query.customerName = obj.linkPhone.substring(obj.linkPhone.length - 4);
      }
      this.$router.push({ name: "logisticsDetail", query });
    },

    // 返回上一页
    onClickLeft() {
      this.$router.go(-1);
    }
  },
  filters: {
    numSplit(val) {
      return val.split(",")[0];
    }
  }
};
</script>
    
<style lang="scss" scoped>
/deep/ .van-nav-bar .van-icon {
  color: #333;
  font-size: 18px;
}
.logistics-search-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  header {
    height: 100px;
    .search {
      padding: 10px 16px;
      background-color: #fff;
      display: flex;
      align-items: center;

      /deep/ .van-cell {
        height: 36px;
        background-color: #f0f0f0;
        padding: 7px 11px;
        border-radius: 6px;
      }
    }
  }

  main {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;

    .main-tips {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-top: 300px;
      .img {
        width: 214px;
        height: 148px;
      }

      p {
        width: 100%;
        text-align: center;
        font-size: 13px;
        color: #b2b2b2;
      }
    }
    .logist-content {
      .logist-content-title {
        height: 30px;
        display: flex;
        align-items: center;
        background: rgba(255, 153, 0, 0.1);
        text-indent: 10px;
        color: #ff9900;
        font-size: 14px;
      }

      .logist-content-list {
        margin: 12px;
        background-color: #fff;
        border-radius: 8px;
        font-size: 14px;
        box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.04);

        .logist-title {
          display: flex;
          justify-content: space-between;
          padding: 12px;
          background-color: #f6faff;

          label {
            color: #8acc47;
          }

          span {
            color: rgba(0, 0, 0, 0.5);
          }
        }

        .logist-item {
          display: flex;
          padding: 10px;

          label {
            width: 88px;
            color: rgba(0, 0, 0, 0.3);
          }

          span {
            display: inline-block;
            flex: 1;
            color: #000000;
          }
        }
      }

      .no-more {
        font-size: 14px;
        color: #ccc;
      }
    }
  }
}
</style>