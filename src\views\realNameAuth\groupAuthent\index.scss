.groupAuthent {
  background: #f7f6f8;
  display: flex;
  flex-direction: column;
  height: 100%;
  main {
    flex: 1;
    overflow-y: auto;
    .input-box {
      background: #ffffff;
      padding: 0 16px;
      display: flex;
      align-items: center;
      position: relative;

      .autofill {
        font-size: 14px;
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: #ff9900;
        letter-spacing: 0;
        text-align: right;
        position: absolute;
        right: 16px;
      }

      .select-input {
        position: absolute;
        background-color: #fff;
        top: 55px;
        left: 0;
        right: 0;
        max-height: 188px;
        z-index: 99;
        font-size: 12px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
        display: flex;
        flex-direction: column;

        .list {
          flex: 1;
          overflow-y: auto;
          overflow-x: hidden;
          padding: 0 16px;

          .defaut {
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }

          .defaut-no {
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .activeIcon {
            width: 14px;
            height: 14px;
          }
        }

        .more {
          height: 32px;
          text-align: right;
          line-height: 32px;
          padding: 0 16px;
          color: #02A7F0;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
        }
      }
    }
    .tips {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #7f7f7f;
      line-height: 20px;
      padding: 8px 16px;
    }

    // 企业证件
    .groupCertificate {
      background: #ffffff;
      padding: 16px;
      .groupCertificate-title {
        width: 64px;
        height: 22px;
        font-family: PingFangSC-Medium;
        font-weight: 550;
        font-size: 16px;
        color: #000000;
      }
      .group-tips {
        width: 343px;
        height: 66px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #7f7f7f;
        line-height: 22px;
        margin: 8px 0;
      }
      .businessLicense {
        padding-top: 15px;

        .businessLicense-title {
          height: 22px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          line-height: 22px;

          .must {
            color: #ed422f;
            margin-left: 4px;
            font-size: 12px;
          }
        }

        .businessLicense-upload {
          width: 343px;
          // height: 162px;
          background: #f7f7f7;
          border-radius: 8px;
          margin-top: 20px;
        }
      }
    }

    // 法人个人证件
    .legalPerson {
      background-color: #ffffff;
      padding: 24px 16px 0 16px;

      .legalPerson-title {
        width: 104px;
        height: 22px;
        font-family: PingFangSC-Medium;
        font-weight: 550;
        font-size: 16px;
        color: #000000;
      }

      .tips {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #7f7f7f;
        line-height: 20px;
        padding: 8px 0;
      }

      .van-radio {
        height: 40px;
      }
    }
  }

  footer {
    z-index: 9999;
    height: 56px;
    background: #ffffff;
    box-shadow: 0 0 0 0 #f0f3fa;

    .btn-box {
      height: 100%;
      padding: 0 16px;
      display: flex;
      justify-content: center;
      align-items: center;

      .btn {
        border-radius: 21px;
      }
    }
  }
}
