<template>
  <van-popup
    v-model="visible"
    position="bottom"
    :style="{ height: '100%' }"
    :close-on-click-overlay="false"
  >
    <div class="add-manager-form">
      <!-- 头部 -->
      <div class="form-header">
        <van-icon name="arrow-left" @click="handleClose" />
        <span class="form-title">新增店长</span>
        <div class="placeholder"></div>
      </div>

      <!-- 表单内容 -->
      <div class="form-content">
        <van-form @submit="handleSubmit">
          <!-- 用户名 -->
          <van-field
            v-model="formData.username"
            name="username"
            label="用户名"
            placeholder="请输入用户名"
            :rules="[{ required: true, message: '请输入用户名' }]"
          />

          <!-- 显示名 -->
          <van-field
            v-model="formData.displayName"
            name="displayName"
            label="显示名"
            placeholder="请输入显示名"
            :rules="[{ required: true, message: '请输入显示名' }]"
          />

          <!-- 手机号码 -->
          <van-field
            v-model="formData.phone"
            name="phone"
            label="手机号码"
            placeholder="请输入手机号码"
            type="tel"
            :rules="[
              { required: true, message: '请输入手机号码' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
            ]"
          />
        </van-form>
      </div>

      <!-- 保存按钮 - 固定在底部 -->
      <div class="form-footer">
        <van-button
          type="warning"
          block
          :loading="loading"
          @click="handleSubmit"
          class="save-button"
        >
          保存
        </van-button>
      </div>
    </div>
  </van-popup>
</template>

<script>
import { Field, Form, Button, Popup, Icon, Toast } from "vant";

export default {
  name: "AddManagerForm",
  components: {
    [Field.name]: Field,
    [Form.name]: Form,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Icon.name]: Icon
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: this.value,
      loading: false,
      formData: {
        username: "",
        displayName: "",
        phone: ""
      }
    };
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit("input", val);
      if (!val) {
        this.resetForm();
      }
    }
  },
  methods: {
    // 关闭弹窗
    handleClose() {
      this.visible = false;
    },

    // 提交表单
    async handleSubmit() {
      try {
        this.loading = true;

        // 表单验证
        if (!this.validateForm()) {
          return;
        }

        // 构造提交数据
        const submitData = {
          username: this.formData.username,
          displayName: this.formData.displayName,
          phone: this.formData.phone
        };

        // 触发保存事件，由父组件处理实际的保存逻辑
        this.$emit("save", {
          data: submitData,
          onSuccess: () => {
            // 保存成功后关闭弹窗并重置loading状态
            this.loading = false;
            this.visible = false;
          },
          onError: (errorMsg) => {
            // 保存失败时只重置loading状态，不关闭弹窗
            this.loading = false;
            Toast(errorMsg || "保存失败，请重试");
          }
        });

      } catch (error) {
        // 本地异常处理（如表单验证失败等）
        this.loading = false;
        Toast("操作失败，请重试");
      }
    },

    // 表单验证
    validateForm() {
      if (!this.formData.username.trim()) {
        Toast("请输入用户名");
        return false;
      }

      if (!this.formData.displayName.trim()) {
        Toast("请输入显示名");
        return false;
      }

      if (!this.formData.phone.trim()) {
        Toast("请输入手机号码");
        return false;
      }

      // 手机号格式验证
      const phoneReg = /^1[3-9]\d{9}$/;
      if (!phoneReg.test(this.formData.phone)) {
        Toast("请输入正确的手机号码");
        return false;
      }

      return true;
    },

    // 重置表单
    resetForm() {
      this.formData = {
        username: "",
        displayName: "",
        phone: ""
      };
    }
  }
};
</script>

<style scoped>
.add-manager-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f7f7f7;
}

.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #eee;
}

.form-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.placeholder {
  width: 24px;
}

.form-content {
  flex: 1;
  padding: 16px 0;
  background: white;
  margin-top: 8px;
  overflow-y: auto;
}

.form-footer {
  padding: 20px;
  background: white;
  border-top: 1px solid #eee;
  /* 确保按钮固定在底部 */
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* 保存按钮样式 - 橙色圆角按钮 */
.save-button {
  background: #ff9900 !important;
  border: none !important;
  border-radius: 6px !important;
  height: 44px !important;
  color: white !important;
}

.save-button:active {
  background: #e68900 !important;
}

/* 表单字段样式调整 */
.add-manager-form :deep(.van-field__label) {
  width: 80px;
  color: #333;
  font-weight: 500;
}

.add-manager-form :deep(.van-field__control) {
  color: #333;
}

.add-manager-form :deep(.van-field__control::placeholder) {
  color: #c8c8c8;
}
</style>
