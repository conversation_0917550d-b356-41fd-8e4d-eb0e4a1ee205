<template>
  <van-popup v-model="show" position="bottom" round :style="{ height: '30%' }">
    <div class="popup_time">
      <div class="title">
        <p></p>
        <p>发货状态</p>
        <img src="@/assets/<EMAIL>" @click="show = false" />
      </div>

      <main>
        <div
          :class="activeIndex == index ? 'active_list' : 'no_list'"
          v-for="(item, index) in list"
          :key="index"
          @click="changeStatus(index)"
        >
          {{ item.key }}
        </div>
      </main>
    </div>
  </van-popup>
</template>
  
  <script>
import { Popup } from "vant";
export default {
  components: {
    [Popup.name]: Popup
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      list: [
        { id: 2, key: "全部" },
        { id: 1, key: "待发货" },
        { id: 3, key: "已发货" }
      ],
      activeIndex: 0
    };
  },
  watch: {
    value(val) {
      this.show = val;
    },
    show(val) {
      this.$emit("input", val);
    }
  },
  methods: {
    changeStatus(index) {
      this.activeIndex = index;
      setTimeout(() => {
        this.show = false;
      }, 300);
      this.$emit("changeStatus", this.list[index].id);
    }
  }
};
</script>
  
  <style lang="scss" scoped>
.popup_time {
  height: 100%;
  padding: 0 16px;
  display: flex;
  flex-direction: column;
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 48px;

    p {
      font-family: PingFangSC-Medium;
      font-weight: 550;
      font-size: 16px;
      color: #000000;
      text-align: center;
    }
    img {
      width: 20px;
      height: 20px;
    }
  }

  main {
    flex: 1;
    display: flex;
    margin-top: 16px;

    .no_list {
      width: 109px;
      height: 32px;
      background: #f7f7f7;
      border: 1px solid #f7f7f7;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
    }

    .active_list {
      width: 109px;
      height: 32px;
      background: #ff99000f;
      border: 1px solid #ff9900;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ff9900;
      margin-right: 8px;
    }
  }
}
</style>