<template>
  <van-popup v-model="show" round position="bottom" :style="{ height: '88%' }">
    <div class="popup-box">
      <div class="title">
        <p>证件</p>
        <img
          class="title-icon"
          src="../../../assets/ico_delete.png"
          alt=""
          @click="onClose"
        />
      </div>
      <div class="popup-tips">
        请上传清晰的身份证照片，系统将自动识别证件信息。照片仅限jpeg、jpg、png格式且大小不超过15M
      </div>
      <!-- 上传组件 -->
      <div class="upload-box">
        <upload-file
          :idCard="true"
          :max="1"
          :size="15 * 1024"
          :cardFront="true"
          @on-upload-success="val => uploadFrontSuccess(val)"
        ></upload-file>
      </div>
      <div class="upload-box">
        <upload-file
          :idCard="true"
          :max="1"
          :cardFront="false"
          :size="15 * 1024"
          @on-upload-success="val => uploadBackSuccess(val)"
        ></upload-file>
      </div>
    </div>
  </van-popup>
</template>

<script>
import { Popup, Toast } from "vant";
import UploadFile from "../../../components/upload-file/upload-file.vue";
// api
import { getOcrInfo } from "@/api/realNameAuth";
export default {
  components: {
    UploadFile,
    [Popup.name]: Popup,
    [Toast.name]: Toast
  },
  props: {
    showPopup: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      personInfo: {
        name: "", //姓名
        idCode: "", //身份证号
        idCardFrontUrl: "", //身份证正面
        idCardBackUrl: "" //身份证反面
      }
    };
  },
  watch: {
    showPopup(val) {
      this.show = val;
    },
    show(val) {
      this.$emit("update", val);
    },

    personInfo: {
      // 数据变化时执行的逻辑代码
      handler(newName) {
        console.log(newName);
        if (newName.idCardFrontUrl && newName.idCardBackUrl) {
          this.show = false;
        }
        this.$emit("personInfo", this.personInfo);
      },
      // 开启深度监听
      deep: true
    }
  },
  methods: {
    // 获取ocr信息
    async getOcrInfo(fileurl) {
      Toast.loading({
        duration: 0,
        message: "获取中...",
        forbidClick: true
      });
      try {
        const res = await getOcrInfo({
          type: "idCard",
          url: fileurl
        });
        if (res) {
          Toast.clear();
          Toast.success({
            message: "获取成功",
            forbidClick: true
          });
          let obj = JSON.parse(res).data.face.data;
          const idCardRegex = /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}(?:\d|X|x)$/;
          if (!idCardRegex.test(obj.idNumber)) {
            Toast.fail({
              duration: 2000,
              message: "身份证号识别有误，请重新识别或手动添加！"
            });
          }
          this.personInfo.name = obj.name;
          this.personInfo.idCode = obj.idNumber;
        }
      } catch (err) {
        if (err) {
          Toast.clear();
        }
      }
    },
    uploadFrontSuccess(val) {
      if (val?.fileurl) {
        this.personInfo.idCardFrontUrl = val.fileurl;
        this.getOcrInfo(val.fileurl);
      }
    },
    uploadBackSuccess(val) {
      if (val?.fileurl) {
        this.personInfo.idCardBackUrl = val.fileurl;
      }
    },

    onClose() {
      this.show = false;
    }
  }
};
</script>

<style lang="scss" scoped>
// popup
.popup-box {
  padding: 0 16px;
  .title {
    height: 70px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    p {
      height: 22px;
      font-family: PingFangSC-Medium;
      font-weight: 550;
      font-size: 16px;
      color: #000000;
      letter-spacing: 0;
      text-align: center;
    }

    .title-icon {
      width: 16px;
      height: 16px;
      position: absolute;
      right: 0;
    }
  }

  .popup-tips {
    width: 343px;
    height: 44px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 12px;
    color: #7f7f7f;
    line-height: 22px;
  }
  .upload-box {
    margin-top: 14px;
  }
}
</style>
