<template>
  <div class="tryPage">
    <navbar title="试用合同" :routerLink="true" @goBack="goBack" />
    <main>
      <div
        class="tryList"
        v-for="(item, index) in result"
        :key="index"
        @click="toDetail(item)"
      >
        <div class="try-title">
          <div class="try-title-top">
            <p class="try-name">{{ item.customerName }}</p>
            <p
              class="try-type"
              :style="{ color: cluingStateColor[item.status - 1] }"
            >
              {{
                contractStatus.find((e) => e.value == item.status)
                  ? contractStatus.find((e) => e.value == item.status).dname
                  : ""
              }}
            </p>
          </div>
          <div class="try-no">试用合同编号：{{ item.contractNo }}</div>
        </div>

        <div class="try-content">
          <div class="item">
            <label for="">签约主体</label>
            <p>{{ item.billingName }}</p>
          </div>
          <div class="item">
            <label for="">销售负责人</label>
            <p>{{ item.userName }}</p>
          </div>
          <div class="item">
            <label for="">合同类型</label>
            <p>
              {{
                contractLoanType.find((e) => e.value == item.type)
                  ? contractLoanType.find((e) => e.value == item.type).dname
                  : ""
              }}
            </p>
          </div>
          <div class="item">
            <label for="">合同金额</label>
            <p>¥{{ item.totalMoney }}</p>
          </div>
          <div class="item">
            <label for="">约定归还时间</label>
            <p>{{ item.returnTime }}</p>
          </div>
        </div>
      </div>

      <infinite-loading
        spinner="spiral"
        @infinite="infiniteHandler"
        :distance="10"
        class="infinite-loading-wrap"
      >
        <div slot="no-more" class="no-more">到底了~</div>
        <div slot="no-results" class="no-more">我是有底线的~</div>
      </infinite-loading>
    </main>
  </div>
</template>

<script>
// api
import { getContractList } from "@/api/tryContract";
import { getAllDict } from "@/api/delivery";
// components
import navbar from "@/components/navbar.vue";
import InfiniteLoading from "vue-infinite-loading";

import { findDeeply } from "../../common/utils";
export default {
  components: {
    navbar,
    InfiniteLoading
  },
  data() {
    return {
      page: {
        no: 1,
        limit: 10
      },
      result: [],
      contractStatus: [],
      contractLoanType: [],
      cluingStateColor: [
        "red",
        "blue",
        "green",
        "red",
        "pink",
        "magenta",
        "green",
        "orange"
      ]
    };
  },
  created() {
    this.getAllDict();
  },
  mounted() {
    this.getContractList();
  },
  methods: {
    toDetail(item) {
      console.log(item);
      this.$router.push({
        name: "tryDetail",
        query: {
          id: item.id
        }
      });
    },

    // 获取合同列表
    async getContractList() {
      try {
        const res = await getContractList({
          choosedType: 0,
          ...this.page
        });
        this.result = res.records || [];
      } catch (error) {
        console.log(error);
      }
    },

    // 无限滚动
    async infiniteHandler($state) {
      const res = await getContractList({
        choosedType: 0,
        ...this.page
      });
      if (res.records.length > 0) {
        this.page.no += 1; // 下一页
        // 过滤掉重复的元素
        const newList = res.records.filter(
          (item) => !this.result.some((resultItem) => resultItem.id === item.id)
        );
        this.result = this.result.concat(newList);
        $state.loaded();
      } else {
        $state.complete();
      }
    },

    /**
     * 全部字典项
     */
    async getAllDict() {
      const res = await getAllDict();
      sessionStorage.setItem("dictTree", JSON.stringify(res));
      if (res) {
        this.getOption();
      }
    },

    /**
     * 字典项
     */
    getOption() {
      let dictTree = JSON.parse(sessionStorage.getItem("dictTree"));
      const contractStatus = findDeeply(
        dictTree,
        (item) => item.type === "contract_status"
      );
      const contractLoanType = findDeeply(
        dictTree,
        (item) => item.type === "contract_loan_type"
      );
      this.contractStatus = [...contractStatus.children] || [];
      this.contractLoanType = [...contractLoanType.children] || [];
    },
    // 返回app
    goBack() {
      this.backToApp();
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>