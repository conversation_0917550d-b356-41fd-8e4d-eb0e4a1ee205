<template>
  <div class="bank-page">
    <div class="title must">银行卡图片</div>
    <div class="bank-img">
      <div class="img-box">
        <img src="../../../assets/hrm/ico_card.png" alt="" />
      </div>
      <div class="add-img">
        <!-- 上传 -->
        <upload-file
          :max="1"
          v-model="attachmentData"
          @on-upload-success="(val) => uploadSuccess(val)"
        ></upload-file>
        <div class="text" v-show="attachmentData.length <= 0">点击上传银行卡图片</div>
      </div>
    </div>
    <!-- 银行卡号 -->
    <div>
      <van-field
        class="must"
        readonly
        clickable
        name="picker"
        :value="form.bankName"
        label="银行名称"
        placeholder="点击选择银行"
        @click="clickBank"
      />
      <van-popup v-model="showBank" position="bottom">
        <van-picker
          show-toolbar
          :columns="bankList"
          :default-index="bankIndex"
          @confirm="onBank"
          @cancel="showBank = false"
        >
          <template #option="option">
            <div>{{ option.dname }}</div>
          </template>
        </van-picker>
      </van-popup>

      <van-field
        class="no-must"
        label="支行名称"
        v-model="form.bankBranch"
        placeholder="请输入"
      />

      <van-field
        class="must"
        label="银行卡号"
        v-model="form.bankAccount"
        placeholder="请输入"
      />
    </div>
  </div>
</template>

<script>
import {
  NavBar,
  Button,
  Divider,
  CellGroup,
  Field,
  Icon,
  Dialog,
  Picker,
  Popup,
  DatetimePicker,
  Toast,
} from "vant";
import UploadFile from "../../../components/upload-file/upload-file.vue";

// api
import {
  getAllNewDict,
  getOcrInfo,
  saveStaffBankAccount,
  updateStaffBankAccount,
  getStaffBankAccountList,
  getStaffAccountDetail,
} from "../../../api/hrm";

export default {
  components: {
    UploadFile,
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Divider.name]: Divider,
    [CellGroup.name]: CellGroup,
    [Field.name]: Field,
    [Icon.name]: Icon,
    [Picker.name]: Picker,
    [Popup.name]: Popup,
    [Dialog.Component.name]: Dialog.Component,
    [DatetimePicker.name]: DatetimePicker,
  },
  data() {
    return {
      bankList: [], //字典项
      showBank: false, //显示选中银行框
      form: {
        id: "",
        bankName: "", //银行字典项value
        bankBranch: "", //支行名称
        bankAccount: "", //银行卡号
      },
      attachmentData: [], //暂存图片
      attachmentList: [], //传值附件资料
      bankIndex:"", //
    };
  },
  mounted() {
    this.getAllNewDict();
    this.getBankList();
  },
  watch:{
    showBank(val) {
      if(!val) {
        this.bankIndex = "";
      }
    }
  },
  methods: {
    uploadSuccess(val) {
      this.form.bankBranch = "";

      this.attachmentData = [val];
      this.getOcrInfo(val.fileurl);
    },

    clickBank(val) {
      this.bankIndex = this.bankList.findIndex((item) => {
        return item.dname == val.target.value;
      })
      this.showBank = true;
    },

    // 点击选择银行
    onBank(val) {
      this.form.bankName = val.dname;
      this.showBank = false;
    },

    // 获取银行卡列表
    async getBankList() {
      try {
        const res = await getStaffBankAccountList({
          staffId: this.$route.query.id,
        });
        // console.log(res, "银行卡列表");
        if (res && res.length != 0) {
          this.getStaffAccountDetail(res[0].id);
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 获取银行信息
    async getStaffAccountDetail(id) {
      try {
        const res = await getStaffAccountDetail(id);
        // console.log(res, "银行信息");
        if (res) {
          this.form = res;
          // 用于展示的数据 attachmentData
          this.attachmentData = res.attachmentList && res.attachmentList;
          this.attachmentData && this.attachmentData.forEach((ele) => {
            ele.fileSuffix = ele.suffix;
            ele.fileType = ele.filetype;
            ele.name = ele.filename;
            ele.fileUrl = ele.fileurl;
            ele.progress = 100;
          });
          this.fileurl = res.attachmentData && res.attachmentData[0].fileurl;

        }
      } catch (error) {
        console.log(error);
      }
    },

    // // OCR获取文字信息
    async getOcrInfo(fileurl) {
      const obj = {
        type: "bankCard",
        url: fileurl,
      };
      try {
        const res = await getOcrInfo(obj);
        let result = JSON.parse(res);
        this.form.bankAccount = result.data.cardNumber;
        this.form.bankName = result.data.bankName;
        // console.log( this.form.bankName , '银行名称字典项');
      } catch (error) {
        console.log(error);
      }
    },

    submit() {
      if (!this.form.id) {
        this.saveStaffBankAccount();
      } else {
        this.updateStaffBankAccount();
      }
    },

    // 保存
    async saveStaffBankAccount() {
      let tempArr = [];
      let tempObj = {};
      this.attachmentData.map((item) => {
        tempObj = {
          id: "",
          filename: item.name,
          fileurl: item.fileurl,
          filetype: item.fileType,
          suffix: item.fileSuffix,
          size: item.size,
          createTime: "",
          createBy: "",
        };
        tempArr.push(tempObj);
        return tempObj;
      });
      this.attachmentList = tempArr;

      if (this.attachmentList.length <= 0 || !this.form.bankAccount || !this.form.bankName) {
        return Toast.fail({
          duration: 2000,
          message: "请填写完整！",
        });
      }

      let obj = {
        ...this.form,
        staffId: this.$route.query.id,
        attachmentList: this.attachmentList,
      };
      try {
        const res = await saveStaffBankAccount(obj);
        console.log(res);
        this.$router.push({
          name: "positionInfor",
          query: {
            id: this.$route.query.id,
          },
        });
      } catch (error) {
        console.log(error);
      }
    },

    // 更新
    async updateStaffBankAccount() {
      let tempArr = [];
      let tempObj = {};
      this.attachmentData.map((item) => {
        tempObj = {
          id: "",
          filename: item.name,
          fileurl: item.fileurl,
          filetype: item.fileType,
          suffix: item.fileSuffix,
          size: item.size,
          createTime: "",
          createBy: "",
        };
        tempArr.push(tempObj);
        return tempObj;
      });
      this.attachmentList = tempArr;

      if (this.attachmentList.length <= 0 || !this.form.bankAccount || !this.form.bankName) {
        return Toast.fail({
          duration: 2000,
          message: "请填写完整！",
        });
      }
      let obj = {
        ...this.form,
        staffId: this.$route.query.id,
        attachmentList: this.attachmentList,
      };
      try {
        const res = await updateStaffBankAccount(obj);
        console.log(res);
        this.$router.push({
          name: "positionInfor",
          query: {
            id: this.$route.query.id,
          },
        });
      } catch (error) {
        console.log(error);
      }
    },

    // 获取银行字典项
    async getAllNewDict() {
      let res = await getAllNewDict();
      this.bankList = res.find((item) => item.type === "customer_bank")
        ? res.find((item) => item.type === "customer_bank").children
        : [];
    },
  },
};
</script>
<style lang="scss" scoped>
.bank-page {
  height: 100%;
  .title {
    // width: 70px;
    height: 22px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 22px;
    padding: 16px;
  }

  .bank-img {
    // width: 343px;
    height: 204px;
    background-color: #f7f7f7;
    border-radius: 8px;
    margin: 0 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    margin-bottom: 15px;

    .img-box {
      background: #fff;
      border-radius: 8px;
      position: relative;
      img {
        width: 289px;
        height: 172px;
        opacity: 1;
        z-index: 66;
      }
    }
    .add-img {
      position: absolute;
      width: 85%;
      height: 86%;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      .round {
        width: 42px;
        height: 42px;
        background: #ff9900;
        border-radius: 50%;
        color: #fff;
        font-size: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        margin-bottom: 8px;
      }
      .text {
        width: 108px;
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        text-align: center;
        line-height: 20px;
        position: absolute;
        margin-top: 40px;
      }
    }
  }
  .must {
    &::before {
      content: "*";
      display: inline-block;
      margin-right: 4px;
      line-height: 1;
      font-family: SimSun;
      font-size: 14px;
      color: #ed4014;
    }
  }

  .no-must {
    padding-left: 27px;
  }
}
</style>