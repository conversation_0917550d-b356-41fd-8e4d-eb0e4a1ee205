<template>
  <div
    class="box-content"
    @click="onChoose"
    v-if="showUpload && fileList.length < max"
  >
    <img src="../../assets/hrm/attement.png" alt="" />
  </div>
</template>

<script>
import { Icon } from "vant";
export default {
  components: {
    [Icon.name]: Icon
  },
  props: {
    fileList: {
      type: Array,
      default() {
        return [];
      }
    },
    max: {
      type: Number
    },
    showUpload: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      visibles: false,
      url: "",
      videoUrl: ""
    };
  },
  methods: {
    onChoose() {
      if (this.disabled) return;
      this.$emit("on-choose");
    },
    onHandleDel(item) {
      this.$emit("on-del", item);
    },
    /**查看大图 */
    showBigImg(e) {
      this.url = e.fileurl || "";
      this.visible = true;
    },
    showVideo(e) {
      this.videoUrl = e.fileurl || "";
      this.visibles = true;
    }
  }
};
</script>

<style scoped lang="scss">
.box-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 20px;
    height: 20px;
  }
}
</style>
