<template>
  <div class="app">
    <router-view></router-view>
  </div>
</template>

<script>
import <PERSON><PERSON> from "js-cookie";
export default {
  name: "index",
  data() {
    return {};
  },
  mounted() {
    const userAgent = navigator.userAgent;
    const tokenMatch = userAgent.match(/ovopark\/[^/]+\/([^/]+)\//);
    if (tokenMatch) {
      const token = tokenMatch[1];
      try {
        Cookie.set("token", token);
      } catch (error) {
        console.error("存储 Token 到 cookie 时出错:", error);
      }
    }
  }
};
</script>

<style>
html {
  width: 100%;
  height: 100%;
}
body {
  width: 100%;
  height: 100%;
}
.app {
  width: 100%;
  height: 100%;
  background-color: #f7f7f7;
}
</style>
