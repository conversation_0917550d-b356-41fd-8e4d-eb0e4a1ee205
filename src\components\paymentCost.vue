<template>
  <div class="payCost-page">
    <van-nav-bar title="付款失败"></van-nav-bar>
    <!-- body -->
    <div class="page-body">
      <div class="cost-box">
        <div class="cost-icon">
          <img src="../assets/img/icon-cost.png" alt="" />
        </div>
        <p class="text-cost">付款失败</p>
        <p class="text-cost-content">失败原因：用户取消支付</p>
      </div>
    </div>
    <!-- footer -->
    <div class="footer">
        <van-button  >取消</van-button>
        <van-button  color="#FF9900">再次付款</van-button>
    </div>
  </div>
</template>

<script>
import { NavBar, Button, Divider } from "vant";
export default {
  components: {
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Divider.name]: Divider,
  },
  data() {
    return {};
  },
  mounted() {},
  watch: {},
  methods: {},
};
</script>

<style scoped lang="scss">
.payCost-page {
  height: 100%;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;

  .page-body {
    display: flex;
    justify-content: center;
    padding: 0 16px;
    flex-direction: column;
    height: 325px;
    .cost-box {
      width: 232px;
      height: 183px;
      display: flex;
      margin: 0 auto;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .cost-icon {
        width: 48px;
        height: 48px;

        img {
          width: 100%;
          height: 100%;
        }
      }
      .text-cost {
        height: 29px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 18px;
        color: #FF1111;
        letter-spacing: 0;
        margin-top: 16px;
      }
      .text-cost-content {
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #B2B2B2;
        letter-spacing: 0;
        margin-top: 8px;
      }
    }

  }
  .footer {
    padding: 0 16px;
    display: flex;
    justify-content: space-between;
  }
  /deep/.van-button {
    width: 164px;
    height: 44px;
    border-radius: 5px;
  }
}
</style>