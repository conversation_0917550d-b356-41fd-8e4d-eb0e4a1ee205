<template>
  <div class="recharge-page">
    <!-- 状态栏 -->
    <header>
      <div class="status-bar">
        <img src="../../assets/ico_back_r.png" alt="" @click="goBack" />
      </div>
      <div class="recharge_account">
        <p>充值账户</p>
        <div class="account_message">
          <div class="amount">
            {{ selectedAccount.cardNumber }}-{{ selectedAccount.money }}
            <h2 class="fade-line"></h2>
          </div>
          <img
            class="icon"
            src="../../assets/change.png"
            alt=""
            @click="showPopup = true"
          />
        </div>
      </div>
    </header>

    <main>
      <div class="input_box">
        <p>充值金额</p>
        <div class="r_input_box">
          <input
            class="r_input"
            type="text"
            inputmode="decimal"
            placeholder="输入金额"
            v-model="selectedAmount"
            @click="handleManualInput"
            readonly
          />
          <span>元</span>
        </div>
      </div>
      <!-- 金额列表 -->
      <div class="item_box">
        <div
          v-for="(item, index) in presetAmounts"
          :key="index"
          class="item"
          :class="{ active: index === selectIndex }"
          @click="changeAmount(index)"
        >
          {{ item }}
          <span>元</span>
        </div>
      </div>

      <!-- 支付方式 -->
      <div class="payment">
        <div class="title">支付方式</div>
        <div class="wx">
          <div>
            <img src="../../assets/ico_wechat.png" alt="" />
            <span>微信支付</span>
          </div>
          <img
            src="../../assets/ico_selected.png"
            style="width: 16px;height: 16px;"
            alt=""
          />
        </div>

        <div class="footer">
          <van-button
            class="btn"
            block
            :disabled="!selectedAmount"
            @click="handleSubmit"
            >确认充值</van-button
          >
        </div>
      </div>
    </main>

    <!-- 弹出选择窗 -->
    <van-popup
      v-model="showPopup"
      round
      position="bottom"
      :style="{ height: '30%' }"
    >
      <div class="popChange">
        <div class="header">
          <div></div>
          <p>账户选择</p>
          <img
            src="../../assets/ico_close_r.png"
            alt=""
            @click="showPopup = false"
          />
        </div>

        <div
          class="item"
          :class="item.id === selectedAccount.id ? 'active' : ''"
          v-for="item in customerAccountList"
          :key="item.id"
          @click="selectAccount(item)"
        >
          {{ item.cardNumber }}
          <div class="item_footer" v-if="item.id === selectedAccount.id">
            <img
              src="../../assets/ico_choice_r.png"
              style="width: 16px;height: 16px;"
              alt=""
            />
          </div>
        </div>
      </div>
    </van-popup>

    <van-number-keyboard
      :show="showAmount"
      theme="custom"
      :extra-key="['.']"
      close-button-text="充值"
      @blur="showAmount = false"
      @close="closeAmount"
      @input="onInput"
      @delete="deleteAmount"
    />
  </div>
</template>

<script>
import { payH5, getCustomerAccountList } from "../../api/serviceSubDetail";
import { Button, Popup, Toast, NumberKeyboard } from "vant";
export default {
  components: {
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Toast.name]: Toast,
    [NumberKeyboard.name]: NumberKeyboard
  },
  data() {
    return {
      selectedAmount: "",
      presetAmounts: [300, 500, 1000, 2000, 5000, 10000],
      selectedPayment: "wechat",
      selectIndex: -1,
      showPopup: false,
      customerAccountList: [],
      selectedAccount: {},
      inputTimer: null,
      showAmount: false
    };
  },
  watch: {
    selectedAmount: {
      handler(newValue) {
        if (!newValue) {
          this.selectIndex = -1;
          return;
        }

        const numValue = Number(newValue);
        if (isNaN(numValue)) {
          this.selectIndex = -1;
          return;
        }

        this.selectIndex = this.presetAmounts.findIndex(
          amount => amount === numValue
        );
      },
      immediate: true
    }
  },
  created() {
    this.getCustomerAccountList();
  },

  methods: {
    goBack() {
      this.backToApp();
    },

    focusAmount() {
      this.showAmount = true;
    },

    handleManualInput() {
      // 点击 input 时调起数字键盘
      this.showAmount = true;
    },

    changeAmount(index) {
      if (this.selectIndex === index) return;
      this.selectIndex = index;
      this.selectedAmount = String(this.presetAmounts[index]);
    },

    async getCustomerAccountList() {
      try {
        const res = await getCustomerAccountList();
        if (res && res.length > 0) {
          this.customerAccountList = res.map(account => ({
            ...account,
            orderName: "账号充值" + "_" + account.nextOrderNo,
            orderNo: account.nextOrderNo
          }));
          this.selectAccount(this.customerAccountList[0]);
        }
      } catch (error) {
        Toast(error.result);
      }
    },

    selectAccount(account) {
      this.selectedAccount = {
        ...account,
        orderName: "账号充值" + "_" + account.nextOrderNo,
        orderNo: account.nextOrderNo
      };
      this.showPopup = false;
    },

    async handleSubmit() {
      if (!this.selectedAccount.id) {
        Toast("请选择充值账户");
        return;
      }

      if (!this.selectedAmount) {
        Toast("请输入充值金额");
        return;
      }

      try {
        Toast.loading({
          message: "提交中...",
          forbidClick: true
        });

        const isAndroid = /android/i.test(navigator.userAgent);
        const isIOS = /iphone|ipad|ipod/i.test(navigator.userAgent);

        const params = {
          orderName: this.selectedAccount.orderName,
          orderNo: this.selectedAccount.orderNo,
          client: isAndroid ? "Android" : isIOS ? "iOS" : null,
          minute: 5,
          price: Number(this.selectedAmount) * 100,
          isAccount: 1,
          pageType: "77-CI1-BI1" //页面从哪来的，回调去那个详情页面查询：（格式） 页面类型-合同ID-账单ID
        };

        const res = await payH5(params);

        Toast.clear();
        if (res) {
          window.location.href = res;
        }
      } catch (error) {
        Toast.fail(error.result);
      }
    },

    deleteAmount() {
      if (this.selectedAmount.length == 0) return;
      this.selectedAmount = this.selectedAmount.substring(
        0,
        this.selectedAmount.length - 1
      );
    },

    onInput(op) {
      // 如果已经超过最大长度限制
      if (this.selectedAmount.length >= 9) {
        return;
      }

      // 处理小数点
      if (op === ".") {
        // 如果已经有小数点,忽略
        if (this.selectedAmount.includes(".")) {
          return;
        }
        // 如果是空值,补0
        if (!this.selectedAmount) {
          this.selectedAmount = "0.";
          return;
        }
        this.selectedAmount += ".";
        return;
      }

      // 构建新的金额字符串
      let newAmount = this.selectedAmount + op;

      // 使用正则验证金额格式
      const amountRegex = /^\d{0,9}(\.\d{0,2})?$/;
      if (!amountRegex.test(newAmount)) {
        return;
      }

      // 处理以0开头的情况
      if (
        newAmount.startsWith("0") &&
        !newAmount.startsWith("0.") &&
        newAmount.length > 1
      ) {
        return;
      }

      this.selectedAmount = newAmount;
    },

    closeAmount() {
      this.showAmount = false;
      this.handleSubmit();
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";

/deep/.van-key--blue {
  color: #fff;
  background-color: #ff9900;
}
</style>
