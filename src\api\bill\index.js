import urls from "./config";
import { get, postJson, post } from "../request";

/*
 * 查询发票管理列表
 */
export function getCrmBillingPage(params = {}) {
  return get(urls.getCrmBillingPage, params);
}

/**
 *
 * 抬头列表
 *
 */
export function getBankList(params = {}) {
  return get(urls.getBankList, params);
}

export function readSettlementLog(params = {}) {
  return get(urls.readSettlementLog, params);
}

/**
 *
 * 申请开票合同查询
 *
 */
export function getContract(params = {}) {
  return get(urls.getContract, params);
}

/**
 *
 * 新增抬头
 *
 */
export function saveBank(params = {}) {
  return postJson(urls.saveBank, params);
}

export function updateBank(params = {}) {
  return postJson(urls.updateBank, params);
}

/**
 *
 * 保存开票
 *
 */
export function saveBilling(params = {}) {
  return postJson(urls.saveBilling, params);
}

/**
 * 合同详情
 */
export function getContractDetail(params = {}) {
  return get(urls.getContractDetail + "/" + params);
}

export function getCustomerBankByContractNo(params = {}) {
  return get(urls.getCustomerBankByContractNo, params, 20000);
}

// 找他人付
export function payByOther(params = {}) {
  return post(urls.payByOther, params);
}

/**
 * 获取税务省市区地址
 */
export function getTaxAreaInfo(params = {}) {
  return get(urls.getTaxAreaInfo, params);
}

// 保存服务发生地
export function saveServicePlace(params = {}) {
  return postJson(urls.saveServicePlace, params);
}

// 查询服务发生地详情
export function getServicePlace(params = {}) {
  return get(urls.getServicePlace, params);
}

// 删除服务发生地
export function deleteServicePlace(params = {}) {
  return get(urls.deleteServicePlace, params);
}
