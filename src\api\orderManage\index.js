import urls from "./config";
import { get, postJson } from "../request";

// 收货地址列表
export function getMasterOrderAddressList(params) {
  return get(urls.getMasterOrderAddressList, params);
}

// 新增/编辑门店地址和收货地址接口
export function insertOrUpdateMasterOrderAddress(params) {
  return postJson(urls.insertOrUpdateMasterOrderAddress, params);
}

// 订单提交
export function saveMasterOrder(params) {
  return postJson(urls.saveMasterOrder, params);
}

// 获取省市区地址
export function getAllAreaInfo(params) {
  return get(urls.getAllAreaInfo, params);
}

// 根据企业查询企业下所有门店信息
export function getDeptByGroupId(params) {
  return get(urls.getDeptByGroupId, params);
}

export function searchUsersByRole(params) {
  return get(urls.searchUsersByRole, params);
}

export function detail(params) {
  return get(urls.detail, params);
}

export function queryProductTypesToTree(params) {
  return get(urls.queryProductTypesToTree, params);
}