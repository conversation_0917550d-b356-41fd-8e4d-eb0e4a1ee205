<template>
  <div class="realNameAuth">
    <div class="title">快捷登录注册</div>
    <main>
      <div class="input-box">
        <input
          v-model="from.phone"
          type="number"
          oninput="if(value.length>11)value=value.slice(0,11)"
          class="input"
          placeholder="请输入手机号"
        />
        <img
          v-if="showDelete"
          @click="clearPhone"
          class="delete"
          src="https://ovopark.oss-cn-hangzhou.aliyuncs.com/2023/12/15/ico_close.png"
          alt=""
        />
      </div>
      <div class="input-box">
        <input
          class="input"
          id="inputCode"
          v-model="from.verifyCode"
          oninput="if(value.length>6)value=value.slice(0,6)"
          type="number"
          placeholder="请输入验证码"
        />
        <span class="authCode" v-if="showVerify" @click="clickVerify"
          >获取验证码</span
        >
        <span class="authCode-and" v-else>重新获取({{ count }}s)</span>
      </div>
      <van-button type="default" :disabled="disabledStatus" @click="login"
        >登录</van-button
      >
      <div class="agree">
        <div>
          <van-radio-group v-model="radio">
            <van-radio checked-color="#FF9900" name="1"></van-radio
          ></van-radio-group>
        </div>
        <div class="agree-txt">
          我已阅读并同意 <span class="agree-color">《隐私策略》、</span
          ><span class="agree-color">《服务协议》</span> 和<span
            class="agree-color"
            >《数字证书使用协议》</span
          >
        </div>
      </div>
    </main>
  </div>
</template>
  
  <script>
// api
import { getLogisticsCode, checkLogisticsCode } from "../../../api/logistics";
// components
import { Button, Toast } from "vant";
import { Radio, RadioGroup, Dialog } from "vant";
import Cookie from "js-cookie";
export default {
  components: {
    [Button.name]: Button,
    [Radio.name]: Radio,
    [RadioGroup.name]: RadioGroup,
    [Dialog.Component.name]: Dialog.Component,
    [Toast.name]: Toast
  },
  data() {
    return {
      from: {
        phone: "",
        verifyCode: ""
      },
      radio: "",
      showDelete: false,
      show: false,
      showVerify: true,
      timer: null,
      count: ""
    };
  },
  computed: {
    disabledStatus() {
      return !(this.radio && this.from.phone && this.from.verifyCode);
    }
  },
  watch: {
    "from.phone"(val) {
      val ? (this.showDelete = true) : (this.showDelete = false);
    }
  },
  created() {},
  methods: {
    // 清空手机号
    clearPhone() {
      this.from.phone = "";
    },

    // 验证手机号格式
    isValidPhone(phoneNumber) {
      const reg = /^1[3-9]\d{9}$/;
      return reg.test(phoneNumber);
    },

    // 获取验证码
    clickVerify() {
      if (
        !(this.from.phone && this.from.phone.length == 11) ||
        !this.isValidPhone(this.from.phone)
      ) {
        return Toast.fail({
          duration: 2000,
          message: "请输入正确的手机号"
        });
      }
      // 获取验证码接口调用
      this.getLogisticsCode();
      const TIME_COUNT = 60;
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.showVerify = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.showVerify = true;
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000);
      }
    },

    // 获取验证码
    async getLogisticsCode() {
      try {
        await getLogisticsCode({
          phone: this.from.phone
        });
        let element = document.getElementById("inputCode"); // 获取到指定标签
        element.focus(); // 重新聚焦输入框
      } catch (error) {
        console.log(error);
      }
    },

    // 验证验证码
    async login() {
      if (!this.from.phone || !this.from.verifyCode) {
        return;
      }
      try {
        const res = await checkLogisticsCode({
          ...this.from
        });
        if (res.isVerify) {
          Cookie.set("token", res.token);
          Toast.success({
            duration: 2000,
            message: "登录成功"
          });

          this.$router.push({
            name: "logisticsSearch"
          });
        }
      } catch (error) {
        console.log(error);
      }
    }
  }
};
</script>
  
<style lang="scss" scoped>
@import "./index.scss";
</style>