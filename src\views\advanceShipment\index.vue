<template>
  <div class="ship-page">
    <!-- header -->
    <header>
      <img src="../../assets/img/ico_back-t.png" alt="" @click="onClickLeft" />
      <div class="title">提前发货</div>
      <img src="../../assets/img/ico_filter.png" alt="" @click="show = true" />
    </header>

    <!-- main -->
    <div class="main-box" v-if="result.length > 0">
      <main>
        <div
          class="list"
          v-for="(item, index) in result"
          :key="index"
          @click="viewDetail(item)"
        >
          <div class="list-top">
            <div class="list-top-no">
              <span class="contract">{{ item.orderNo }}</span>
              <span
                :class="{
                  status: item.status == 1,
                  statusTwo: item.status == 2,
                  statusThree: item.status == 3,
                  statusFour: item.status == 4
                }"
                >{{ item.status | invoiceStatus(item.status) }}</span
              >
            </div>
            <div class="list-top-content">合同编号：{{ item.contractNo }}</div>
          </div>
          <div class="list-bottom">
            <div class="item">
              <label for="">客户名称</label>
              <div class="content">{{ item.customerName }}</div>
            </div>
            <div class="item">
              <label for="">发货欠款总额</label>
              <div class="content">{{ item.sendArrearsAmount }}</div>
            </div>
            <div class="item">
              <label for="">预计回款时间</label>
              <div class="content">{{ item.expectedPayTime }}</div>
            </div>
            <div class="item">
              <label for="">创建人</label>
              <div class="content">{{ item.createName }}</div>
            </div>
            <div class="item">
              <label for="">创建时间</label>
              <div class="content">{{ item.createTime }}</div>
            </div>
          </div>
        </div>
      </main>

      <infinite-loading
        spinner="spiral"
        @infinite="infiniteHandler"
        :distance="10"
        class="infinite-loading-wrap"
      >
        <div slot="no-more" class="no-more">到底了~</div>
        <div slot="no-results" class="no-more">我是有底线的~</div>
      </infinite-loading>
    </div>
    <van-empty v-else description="暂无数据" />

    <!-- popup -->
    <popupDreaw v-model="show" @sure="sure" />
    <popupDetail
      v-model="showDetail"
      :id="id"
      @update="getEarlyOrderList"
      :row="row"
    />
  </div>
</template>

<script>
import popupDreaw from "./components/popupDreaw.vue";
import popupDetail from "./components/popupDetail.vue";
import InfiniteLoading from "vue-infinite-loading";
import { Empty } from "vant";
// api
import { getEarlyOrderList } from "../../api/advanceShip";
export default {
  components: {
    popupDreaw,
    popupDetail,
    InfiniteLoading,
    [Empty.name]: Empty
  },
  data() {
    return {
      show: false,
      showDetail: false,
      result: [],
      page: {
        no: 1,
        limit: 10
      },
      orderNo: "",
      customerName: "",
      contractNo: "",
      auditStatus: "",
      startTime: "",
      endTime: "",
      id: "",
      row: {}
    };
  },

  watch: {},

  mounted() {
    this.getEarlyOrderList();
  },

  methods: {
    sure(val) {
      this.orderNo = val.orderNo;
      this.customerName = val.customerName;
      this.contractNo = val.contractNo;
      this.auditStatus = val.auditStatus;
      this.startTime = val.startTime;
      this.endTime = val.endTime;
      this.page.no = 1;
      this.getEarlyOrderList();
    },

    // 提前发货单列表
    async getEarlyOrderList() {
      let params = {
        ...this.page,
        orderNo: this.orderNo,
        customerName: this.customerName,
        contractNo: this.contractNo,
        auditStatus: this.auditStatus,
        startTime: this.startTime,
        endTime: this.endTime
      };
      try {
        const res = await getEarlyOrderList(params);
        this.result = res?.records;
      } catch (error) {
        console.log(error);
      }
    },

    // 查看详情
    viewDetail(item) {
      this.id = item.id;
      this.row = item;
      this.showDetail = true;
    },

    // 无限滚动
    async infiniteHandler($state) {
      const res = await getEarlyOrderList({
        ...this.page,
        orderNo: this.orderNo,
        customerName: this.customerName,
        contractNo: this.contractNo,
        auditStatus: this.auditStatus
      });
      if (res.records.length > 0) {
        this.page.no += 1; // 下一页
        // 过滤掉重复的元素
        const newList = res.records.filter(
          (item) => !this.result.some((resultItem) => resultItem.id === item.id)
        );
        this.result = this.result.concat(newList);
        $state.loaded();
      } else {
        $state.complete();
      }
    },

    onClickLeft() {
      this.backToApp();
    }
  },
  filters: {
    invoiceStatus(val) {
      const statusMap = {
        1: "审批中",
        2: "已通过",
        3: "驳回",
        4: "草稿"
      };
      return statusMap[val] || "";
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>