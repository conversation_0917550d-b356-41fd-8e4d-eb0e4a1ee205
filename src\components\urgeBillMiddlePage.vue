<!-- 合同审批的中间跳转页面 -->
<template>
  <div class="audit-middle-page">
    <div class="is-mobile" v-show="!isWeb && isWeixin">
      <div class="arror">
        <img src="../assets/arror.png" alt="" />
      </div>
      <div class="step">
        <h3>1.点击右上角</h3>
        <div class="img">
          <img src="../assets/dot.png" alt="" />
        </div>
      </div>
      <div class="step">
        <h3>2.选择在{{ isIOS ? "“Safari” " : "“浏览器” " }}中打开</h3>
      </div>
      <div class="step1">
        <img
          v-show="isAndroid"
          class="os-brower"
          src="../assets/anz.png"
          alt=""
        />
        <img v-show="isIOS" class="os-brower" src="../assets/ios.png" alt="" />
      </div>
    </div>
    <div class="bg" v-show="!isWeb && !isWeixin">
      <img src="../assets/bg.png" alt="" />
      <van-button class="btn1" @click="wakeUp()" type="primary" block
        >打开万店掌App</van-button
      >
      <van-button
        class="btn2"
        @click="download()"
        ref="btn2"
        type="danger"
        block
        >立即下载万店掌App</van-button
      >
    </div>
  </div>
</template>

<script>
import { Button } from "vant";

export default {
  components: {
    [Button.name]: Button
  },

  mounted() {
    var userAgent = navigator.userAgent;
    var isWeixin = userAgent.toLowerCase().indexOf("micromessenger") !== -1; // 微信内
    var isAndroid = /(Android)/i.test(userAgent); //android终端
    var isIOS = /(iPhone|iPad|iPod|iOS|Mac|Mac)/i.test(userAgent); //ios终端

    this.isAndroid = isAndroid;
    this.isIOS = isIOS;
    this.isWeixin = isWeixin;

    if (isWeixin) {
      this.isWeb = false;
    } else {
      if (!isAndroid && !isIOS) {
        this.isWeb = true;
      }
      //android端
      if (isAndroid || isIOS) {
        this.isWeb = false;
        let theEvent = document.createEvent("Events");
        theEvent.initEvent("click", true, true);
        document.getElementsByClassName("btn1")[0].dispatchEvent(theEvent);
      }
    }
  },

  data() {
    return {
      isWeb: true,
      isWeixin: false,
      isAndroid: false,
      isIOS: false
    };
  },
  methods: {
    //唤醒
    wakeUp() {
      if (window.ovopark.browser.android) {
        window.ovopark.action("openAppPage", {
          module: "CRM_EXPENSE_CENTER"
        });
      } else {
        window.location.href =
          "ioswdzforappstore://push/crm?type=ivan_view&page=ExpenseCenterTopViewController";
      }
      // window.location.href = this.isAndroid ? "jaq://com.kedacom.ovopark?type=crm_cost_center" : "ioswdzforappstore://push/crm?type=ivan_view&page=ExpenseCenterTopViewController";
    },

    //下载
    download() {
      window.location.href = this.isAndroid
        ? "http://f.ovopark.com/version/APP/WDZ/ovoparkApp-ovopark-release.apk"
        : "https://apps.apple.com/cn/app/%E4%B8%87%E5%BA%97%E6%8E%8C-%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E6%96%B0%E9%9B%B6%E5%94%AE%E8%BF%90%E8%90%A5%E5%B9%B3%E5%8F%B0/id1116662738";
    }
  }
};
</script>

<style scoped lang="scss">
.audit-middle-page {
  height: 100%;
  background: #fff;
  .is-mobile {
    padding-top: 160px;
    .arror {
      position: absolute;
      top: 10px;
      right: 10px;
      img {
        height: 175px;
        width: 90px;
      }
    }
    .step {
      position: relative;
      padding-left: 60px;
      height: 60px;
      line-height: 60px;
      .img {
        position: relative;
        top: -68px;
        left: 111px;
      }
    }

    .step1 {
      text-align: center;
      .os-brower {
        height: 230px;
        width: 170px;
      }
    }
    .content {
      margin-top: 20px;
      padding: 0 40px;
      line-height: 25px;
      text-align: center;
      color: #ccc;
    }
  }
  .bg {
    height: 95%;
    img {
      width: 100%;
    }
    .btn1 {
      margin-bottom: 10px;
      background: #f90;
      border: 1px solid #f90;
    }
    .btn2 {
      margin-bottom: 10px;
    }
  }
}
</style>
