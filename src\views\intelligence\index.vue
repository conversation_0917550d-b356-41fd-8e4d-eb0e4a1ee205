<template>
  <div class="intelligence-container">
    <van-nav-bar
      title="谍报中心"
      left-arrow
      @click-left="onClickLeft"
    ></van-nav-bar>
    <div class="body">
      <div class="chat-history" v-if="chatHistory.length">
        <div
          class="chat-message"
          :class="{ robot: item.role == 'robot' }"
          v-for="item in chatHistory"
          :key="item.sesstionId"
        >
          <img src="@/assets/<EMAIL>" alt="" class="avator" />
          <div class="chat-content">
            {{ item.content }}
          </div>
        </div>
      </div>
      <div class="chat-no-history" v-else>
        <div class="robot-welcome">
          <img src="@/assets/<EMAIL>" alt="" class="avator" />
          <div class="content">
            欢迎来到谍报中心，您可以选择下方标准问题，或者自定义问题跟我交流
          </div>
        </div>
        <div
          class="chat-message"
          :class="{ robot: item.role == 'robot' }"
          v-for="item in chatList"
          :key="item.sesstionId"
        >
          <img src="@/assets/<EMAIL>" alt="" class="avator" />
          <img
            src="https://ovopark.oss-cn-hangzhou.aliyuncs.com/2023/10/23/dymicPng.png"
            alt=""
            class="dot"
            v-if="item.type === 'waiting'"
          />
          <div class="chat-content" v-if="item.content">
            {{ item.content }}
          </div>
        </div>
      </div>
    </div>
    <div class="bottom">
      <div class="btn-wrap">
        <van-button
          color="#F7F7F7"
          round
          size="small"
          @click="wakePopup(item.type)"
          v-for="item in btnList"
          :key="item.type"
          >{{ item.name }}</van-button
        >
      </div>
      <div class="chat-input">
        <van-field
          v-model="chatContent"
          placeholder="有问题尽管问我～"
          type="textarea"
          rows="1"
          autosize
        />
      </div>
      <img
        src="https://ovopark.oss-cn-hangzhou.aliyuncs.com/2024/01/08/active_send.png"
        alt=""
        v-if="chatContent"
        @click="onConfirm"
      />
      <img
        src="https://ovopark.oss-cn-hangzhou.aliyuncs.com/2024/01/08/send.png"
        alt=""
        v-else
      />
    </div>
    <van-popup v-model="showModal" position="bottom" round
      ><div class="popup-content">
        <van-sticky>
          <div class="btn-wrap">
            <van-button
              round
              size="small"
              v-for="item in btnList"
              :key="item.type"
              @click="type = item.type"
              :class="{ active: item.type === type }"
              >{{ item.name }}</van-button
            >
          </div>
        </van-sticky>
        <div class="content">
          <ul class="list">
            <li class="item">品牌开放加盟是否开放加盟，加盟条件是什么</li>
            <li class="item">品牌名称与标志</li>
            <li class="item">品牌创立时间与地点</li>
            <li class="item">品牌所属行业与产品类别</li>
          </ul>
        </div>
      </div></van-popup
    >
  </div>
</template>

<script>
import { NavBar, Button, Toast, Field, Popup, Sticky } from "vant";
import { sendDialogue } from "api/intelligence";

export default {
  components: {
    [NavBar.name]: NavBar,
    [Button.name]: Button,
    [Field.name]: Field,
    [Popup.name]: Popup,
    [Sticky.name]: Sticky,
    [Toast.name]: Toast
  },
  data() {
    return {
      chatList: [],
      chatHistory: [],
      chatContent: "",
      sesstionIds: [],
      sessionId: "",
      showModal: false,
      type: 1,
      btnList: [
        {
          type: 1,
          name: "工商信息"
        },
        {
          type: 2,
          name: "品牌咨询"
        },
        {
          type: 3,
          name: "招聘信息"
        }
      ]
    };
  },
  methods: {
    onClickLeft() {
      this.backToApp();
    },
    // 生成随机字符串
    generateRandomString(length) {
      const characters =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
      let result = "";
      for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        result += characters.charAt(randomIndex);
      }
      return result;
    },
    showTypingEffect(index, answer) {
      const chatList = this.chatList;
      let text = "";
      let currentIndex = 0;
      const timer = setInterval(() => {
        text = answer?.substring(0, currentIndex + 1);
        chatList[index].content = text;
        chatList[index].status = "printing";
        currentIndex++;
        if (currentIndex >= answer?.length) {
          chatList[index].status = "finished";
          this.chatList = chatList;
          clearInterval(timer);
        }
      }, 30);
    },
    async onConfirm() {
      const chatList = this.chatList;
      const randomKey = this.generateRandomString(8) + "_" + Date.now();
      if (!this.chatContent.trim()) {
        return Toast.fail({
          duration: 2000,
          message: "请输入内容"
        });
      }
      const isPrinting = chatList?.some(
        it => it.status === "printing" || it.type === "waiting"
      );
      if (isPrinting) {
        Toast.fail({
          duration: 2000,
          message: "请等待上一条消息发送完毕"
        });
        this.chatContent = "";
        return;
      }
      chatList.push({
        role: "user",
        content: this.chatContent
      });
      chatList.push({
        role: "robot",
        content: "",
        type: "waiting"
      });
      this.chatList = chatList;
      const params = {
        content: this.chatContent,
        sessionId: randomKey
      };
      this.sesstionIds.push(randomKey);
      try {
        const res = await sendDialogue(params);
        const sessionId = res?.conversationId;
        const role = res?.role;
        const answer = res?.answer;
        this.sessionId = sessionId;
        this.chatList.push({
          content: answer,
          role
        });
        this.chatContent = "";
        const index = this.chatList?.findIndex(it => it.type === "waiting");
        if (index > -1) {
          this.chatList.splice(index, 1);
        }
        this.showTypingEffect(this.chatList.length - 1, answer);
      } catch (error) {
        const chatList = this.chatList;
        const index = chatList?.findIndex(it => it.type === "waiting");
        if (index > -1) {
          chatList.splice(index, 1);
        }
        Toast.fail({
          duration: 2000,
          message: error.result || error.data
        });
      }
    },
    wakePopup(type) {
      this.type = type;
      this.showModal = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.intelligence-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  height: 100vh;
  .body {
    padding: 16px;
    flex: 1;
    max-height: 600px;
    overflow-y: auto;
    .chat-message {
      margin-bottom: 20px;
      display: flex;
      flex-direction: row-reverse;
      .avator {
        width: 40px;
        height: 40px;
        margin-left: 8px;
      }
      .chat-content {
        background: #ff9900;
        border-radius: 12px 12px 0 12px;
        padding: 8px;
        font-size: 14px;
        color: #fff;
        line-height: 22px;
      }
      &.robot {
        flex-direction: row;
        .avator {
          margin-right: 8px;
          margin-left: 0;
        }
        .chat-content {
          background: #f4f5f7;
          border-radius: 12px 12px 12px 0;
          color: #1e232e;
        }
        .dot {
          width: 42px;
          height: 40px;
        }
      }
    }
    .robot-welcome {
      margin-bottom: 20px;
      display: flex;
      .avator {
        width: 40px;
        height: 40px;
        margin-right: 8px;
      }
      .content {
        padding: 8px;
        font-size: 14px;
        background: #f4f5f7;
        border-radius: 12px 12px 12px 0;
        color: #1e232e;
        line-height: 22px;
      }
    }
  }
  .bottom {
    padding: 12px 16px;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    background: #fff;
    .chat-input {
      width: 100%;
      border-radius: 6px;
      background: #f7f7f7;
      /deep/ .van-cell {
        width: 90%;
        background: #f7f7f7;
        border-radius: 6px;
      }
    }
    img {
      width: 24px;
      height: 24px;
      position: absolute;
      top: 20px;
      right: 30px;
    }
    .btn-wrap {
      margin-bottom: 12px;
      .van-button {
        margin-right: 8px;
        .van-button__text {
          font-size: 12px;
          color: #333333;
        }
      }
    }
  }
  .popup-content {
    .btn-wrap {
      padding: 16px;
      .van-button {
        background: #f7f7f7;
        color: #333333;
        font-size: 12px;
        margin-right: 8px;
        &.active {
          background: rgba(255, 153, 0, 0.1);
          color: #ff9900;
        }
      }
    }
    .content {
      padding: 16px;
      .list {
        max-height: 300px;
        overflow-y: auto;
        .item {
          display: flex;
          justify-content: space-between;
          background: #ffffff;
          border: 1px solid #e5e5e5;
          border-radius: 6px;
          padding: 12px;
          margin-bottom: 8px;
          font-size: 14px;
          color: #333333;
        }
      }
    }
  }
}
</style>
