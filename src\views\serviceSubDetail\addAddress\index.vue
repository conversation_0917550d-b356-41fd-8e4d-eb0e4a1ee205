<template>
  <div class="edit-page">
    <navbar
      :title="edit ? '编辑地址' : '新增地址'"
      :routerLink="true"
      @goBack="goBack"
    />
    <div class="edit-main">
      <van-form @submit="onSubmit">
        <main>
          <van-cell-group>
            <van-field
              v-model="recipient"
              label="姓名"
              placeholder="收货人姓名"
              required
              :show-error="false"
              :rules="[{ required: true, message: '请输入收货姓名' }]"
            />
            <van-field
              v-model="mobile"
              label="电话"
              placeholder="收货人手机号"
              maxlength="11"
              type="number"
              required
              :rules="[{ required: true, message: '请输入收货人手机号' }]"
            />
            <van-field
              required
              v-model="fieldValue"
              label="地区"
              placeholder="选择省/市/区"
              readonly
              is-link
              :rules="[{ required: true, message: '请选择省/市/区' }]"
              @click="show = true"
            />

            <van-field
              v-model="address"
              required
              type="textarea"
              label="详细地址"
              placeholder="街道门牌、楼层房间号等信息"
              :rules="[{ required: true, message: '请输入详细地址' }]"
            />
          </van-cell-group>

          <div class="setting">
            <p>设置默认地址</p>
            <van-switch
              v-model="checked"
              size="20px"
              active-color="#FF9900"
              inactive-color="#E5E5E5"
            />
          </div>
        </main>
        <footer>
          <van-button
            class="btn"
            native-type="submit"
            :loading="submitLoading"
            :disabled="submitLoading"
            >保存</van-button
          >
        </footer>
      </van-form>
    </div>
    <van-popup v-model="show" round position="bottom">
      <van-cascader
        v-model="location"
        title="请选择所在地区"
        :options="options"
        active-color="#FF9900"
        @close="show = false"
        @finish="onFinish"
      />
    </van-popup>
  </div>
</template>

<script>
// api
import {
  getAllDistrictInfo,
  saveOrUpdateAddress
} from "../../../api/serviceSubDetail";
// components
import navbar from "../../../components/navbar.vue";
import {
  CellGroup,
  Field,
  Switch,
  Cascader,
  Popup,
  Form,
  Button,
  Notify
} from "vant";
export default {
  components: {
    navbar,
    [CellGroup.name]: CellGroup,
    [Field.name]: Field,
    [Switch.name]: Switch,
    [Cascader.name]: Cascader,
    [Popup.name]: Popup,
    [Form.name]: Form,
    [Button.name]: Button,
    [Notify.name]: Notify
  },
  data() {
    return {
      id: "",
      checked: false,
      show: false,
      fieldValue: "",
      submitLoading: false,
      options: [],
      edit: false,
      recipient: "", // 收货人
      mobile: "", // 手机号
      location: "", // 地区
      address: "", // 详细地址
      isDefault: 0 //  是否默认 0 非默认 1 默认
    };
  },
  mounted() {
    this.edit = this.$route.params.edit; //如果是编辑，则需要将编辑信息带入 true 为编辑 false 为新增
    if (this.edit) {
      const { id, recipient, location, mobile, district, address, isDefault } =
        this.$route.params;
      this.id = id;
      this.recipient = recipient;
      this.location = location;
      this.mobile = mobile;
      this.fieldValue = district.replace(/ /g, "/");
      this.address = address;
      this.checked = isDefault == 1;
    }
    this.getAllDistrictInfo();
  },
  methods: {
    // 新增或编辑地址
    async onSubmit() {
      this.submitLoading = true;
      try {
        await saveOrUpdateAddress({
          id: this.id || null,
          recipient: this.recipient,
          mobile: this.mobile,
          location: this.location,
          district: this.fieldValue,
          address: this.address,
          isDefault: this.checked ? 1 : 0
        });
        setTimeout(() => {
          this.submitLoading = false;
          if (this.id) {
            Notify({ type: "success", message: "编辑成功" });
          } else {
            Notify({ type: "success", message: "新增成功" });
          }
          this.$router.push({
            name: "addressManage",
            query: {
              id: this.$route.query.id
            }
          });
        }, 1500);
      } catch (error) {
        if (error) {
          this.submitLoading = false;
        }
      }
    },

    // 全部选项选择完毕后，会触发 finish 事件
    onFinish({ selectedOptions }) {
      this.show = false;
      this.fieldValue = selectedOptions.map((option) => option.text).join("/");
      this.location = selectedOptions[selectedOptions.length - 1].code;
    },

    // 获取省市区地址
    async getAllDistrictInfo() {
      try {
        const res = await getAllDistrictInfo();
        this.options = this.getArea(res);
      } catch (error) {
        console.log(error);
      }
    },

    // 处理返回的地址信息
    getArea(arr) {
      return arr.map((item) => {
        const newItem = { ...item, text: item.shortName, value: item.code };
        if (item?.children?.length > 0) {
          newItem.children = this.getArea(item.children);
        } else {
          delete newItem.children;
        }
        return newItem;
      });
    },

    goBack() {
      // 新增或编辑返回地址管理页
      this.$router.push({
        name: "addressManage",
        query: {
          id: this.$route.query.id
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.edit-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f7f7f7;

  .edit-main {
    flex: 1;
    background: #f7f7f7;
    display: flex;

    .van-form {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    main {
      flex: 1;
      margin-top: 8px;

      .setting {
        height: 56px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 16px;
        background: #fff;

        p {
          height: 22px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #000000;
          line-height: 22px;
        }
      }
    }

    footer {
      height: 56px;
      background-color: #fff;
      border-top: 1px solid #e5e5e5;
      display: flex;
      align-items: center;
      justify-content: center;

      button {
        background: none;
        color: inherit;
        border: none;
        padding: 0;
        font: inherit;
        cursor: pointer;
        outline: inherit;
      }

      .btn {
        color: #fff;
        width: 343px;
        height: 40px;
        background: #ff9900;
        border-radius: 8px;
      }
    }
  }
}

::v-deep .van-field__label {
  font-weight: 400;
  font-size: 14px;
  color: #000000;
}
</style>