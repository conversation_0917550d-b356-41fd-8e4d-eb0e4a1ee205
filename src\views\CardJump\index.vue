<template>
  <div class="crad-page">
    <div class="logo">
      <img src="../../assets/img/cardlogo.png" alt="" />
    </div>
    <div class="title">万友引力</div>
    <div class="content">将打开微信关联「万友引力」小程序</div>
    <div class="content">获取小程序名片数据</div>
    <div class="btn-ok">
      <van-button @click="goCard" type="primary" class="btn">前往</van-button>
    </div>
    <div class="btn-cancal">
      <van-button  class="btn" @click="cancalClick">取消</van-button>
    </div>
  </div>
</template>

<script>
// api
import { generateUrlLink } from "../../api/card";

import { Button } from "vant";
export default {
  components: {
    [Button.name]: Button,
  },
  data() {
    return {
      cardId:"",
      sourceId:"",
      masterId:"",
    };
  },
  mounted() {
    console.log(this.$route.query.param);
    this.cardId = this.$route.query.param;
    this.sourceId = this.$route.query.sourceParam;
    this.masterId = this.$route.query.masterId;
  },
  methods:{
    async generateUrlLink() {
      let obj = {
        cardId:this.cardId || "",
        sourceId:this.sourceId || "",
        masterId:this.masterId || "",
      }
      try {
        const res = await generateUrlLink(obj);
        if(res) {
          window.location.href = res;
        }
      }catch(error) {
        console.log(error);
      }
    },

    goCard() {
      this.generateUrlLink();
    },

    cancalClick() {
      this.backToApp();
    }
  }
};
</script>

<style lang="scss" scoped>
.crad-page {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  background-color: #ffffff;
  .logo {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 119px;

    img {
      width: 44px;
      height: 44px;
      margin-top: 75px;
    }
  }
  .title {
    margin: 0 auto;
    height: 22px;
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    margin-top: 12px;
    text-align: center;
    margin-bottom: 20px;
  }
  .content {
    margin: 0 auto;
    height: 22px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #7f7f7f;
    line-height: 22px;
    text-align: center;
    margin-bottom: 8px;
  }

  .btn-ok {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 24px;
    margin-top: 84px;
    .btn {
      width: 200px;
      height: 44px;
      background: #56bf6a;
      border-radius: 6px;
    }
  }

  .btn-cancal {
    display: flex;
    justify-content: center;
    align-items: center;

    .btn {
      width: 200px;
      height: 44px;
      background: #f0f0f0;
      border-radius: 6px;
    }
  }
}
</style>