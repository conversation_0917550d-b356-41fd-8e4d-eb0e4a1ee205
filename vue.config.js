const path = require("path");
const isOnline = process.env.NODE_ENV === "production" ? true : false;

module.exports = {
  publicPath: isOnline ? "/notice/" : "./",

  configureWebpack: {
    resolve: {
      alias: {
        "~": path.resolve(__dirname, "./node_modules/"),
        "@": path.resolve(__dirname, "./src"),
        api: path.resolve("./src/api")
      }
    }
  },

  chainWebpack: config => {
    config.plugins.delete("prefetch");
  },
  devServer: {
    open: true,
    proxy: {
      "/api/crm/*": {
        changeOrigin: true,
        target: isOnline
          ? "http://tianti.ovopark.com/"
          : "http://tianti.wandianzhang.com/"
      },
      //
      "/ovopark-organize/*": {
        changeOrigin: true,
        target: isOnline
          ? "http://172.16.3.190:9499/"
          : "http://test.wistore.net/"
      },
      "/api/member/*": {
        changeOrigin: true,
        target: isOnline
          ? "http://tianti.wandianzhang.com/"
          : "http://tianti.wandianzhang.com/"
      },
      "/api/common/*": {
        changeOrigin: true,
        target: isOnline
          ? "https://116.62.15.85:9112/"
          : "http://tianti.wandianzhang.com/"
      },
      "/ovopark-privilege/*": {
        changeOrigin: true,
        target: isOnline
          ? "https://www.ovopark.com/"
          : "http://test.wistore.net/"
      },
      "/api/dc/*": {
        changeOrigin: true,
        target: isOnline
          ? "https://obs.ovopark.com/"
          : "https://obs.wandianzhang.com/"
      }
    }
  }
};
