<template>
  <div class="grid">
    <div v-for="(item, i) in fileList" :key="i" class="grid-box">
      <div class="box-content-container">
        <div class="box-content">
          <video
            :src="item.fileUrl"
            v-if="item.fileType === 1 || item.fileUrl && /.mp4|.flv|.avi|.swf|.MP4/.test(item.fileUrl)"
            class="upload-img"
            @click="showVideo(item)"
          ></video>
          <img
            :src="item.fileUrl"
            @click="showBigImg(item)"
            class="upload-img"
            v-else
          />
          <div class="file-mask" v-show="item.progress !== 100">
            <span class="progress-text">{{ item.progress || 0 }}%</span>
          </div>
          <div class="del-box" @click="onHandleDel(item)">
            <!-- <span class="del-icon-box"></span> -->
            <img class="del-icon-box" src="../../assets/hrm/ico_edit.png" alt="">
          </div>
        </div>
      </div>
    </div>

    <!-- 选择区域 -->
    <div
      class="grid-box upload-area"
      @click="onChoose"
      v-if="showUpload && fileList.length < max"
    >
      <div class="box-content-container">
        <div class="box-content">
          <div :class="['add-box', disabled ? 'mask' : '']">
            +
            <!-- <img :src="require('./add.png')" class="add-icon" /> -->
          </div>
        </div>
      </div>
    </div>

    <!-- <Modal title="预览" v-model="visible" :closable="false">
      <img :src="url" v-if="visible" style="width: 100%; max-height: 600px;" />
      <div slot="footer">
        <Button @click="visible = false">关闭</Button>
      </div>
    </Modal> -->

    <!-- <Modal title="预览" v-model="visibles" :closable="false">
      <video :src="videoUrl" v-if="visibles" style="width: 100%; max-height: 600px;" autoplay="true" controls />
      <div slot="footer">
        <Button @click="visibles = false">关闭</Button>
      </div>
    </Modal> -->
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    fileList: {
      type: Array,
      default() {
        return [];
      },
    },
    max: {
      type: Number,
    },
    showUpload: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      visibles: false,
      url: "",
      videoUrl:"",
    };
  },
  methods: {
    onChoose() {
      if (this.disabled) return;
      this.$emit("on-choose");
    },
    onHandleDel(item) {
      this.$emit("on-del", item);
    },
    /**查看大图 */
    showBigImg(e) {
      this.url = e.fileurl || "";
      this.visible = true;
    },
    showVideo(e) {
      this.videoUrl = e.fileurl || "";
      this.visibles = true;
    }
  },
};
</script>

<style scoped lang="scss">
.grid {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
  // margin-right: -10px;

  .upload-box {
    display: flex;
    width: 140px;
    height: 140px;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    border: 1px solid #e1e1e1;
    color: #2d8cf0;
  }

  .upload-img {
    width: 100%;
    height: 100%;
  }

  .add-box {
    // display: flex;
    // height: 100%;
    // justify-content: center;
    // align-items: center;
    // border: 1px solid #e1e1e1;
    // border-radius: 6px;
    width: 11.2vw;
    height: 11.2vw;
    background: #ff9900;
    border-radius: 50%;
    color: #fff;
    font-size: 8.53333vw;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    margin-bottom: 2.13333vw;
  }

  .add-icon {
    width: 50%;
    height: 50%;
  }

  .grid-box {
    width: 100%;
    height: 100%;

    .box-content-container {

      width: 100%;
      height: 100%;


      .box-content {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 9px;

        img {
          border-radius: 9px;
        }
      }

      .file-mask {
        display: flex;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.3);
      }

      .progress-text {
        font-size: 20px;
        color: #fff;
      }
    }
  }

  .del-box {
    position: absolute;
    top: 0;
    right: 0;
    width: 24px;
    height: 24px;
    background: #E5E5E5;
    border-radius: 0 8px 0 8px;
    display: flex;
    justify-content: center;
    align-items: center;

    .del-icon-box {
      width: 16px;
      height: 16px;
      // display: flex;
      // width: 16px;
      // height: 16px;
      // align-items: center;
      // justify-content: center;
      // border-radius: 50%;
      // background: #ff1111;
    }

    // .del-icon-box:before {
    //   content: "";
    //   display: inline-block;
    //   width: 60%;
    //   height: 2px;
    //   background: #fff;
    // }
  }
}
.mask {
  background-color: #a0a1a5;
  cursor: not-allowed;
}
</style>
