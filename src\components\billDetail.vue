<template>
  <div class="detail">
    <!-- <van-nav-bar v-show="showHome" title="账单详情" left-arrow @click-left="onleftClick"></van-nav-bar> -->
    <van-nav-bar
      left-arrow
      @click-left="onClickLeft"
      v-show="showHome"
      title="账单详情"
    ></van-nav-bar>
    <div class="body">
      <div class="basic-info">
        <p class="bill-status">{{ billStatus }}</p>
        <p class="body-title">
          {{ billDetail.billName }}
        </p>
        <p class="bill-no">
          <span class="label">账单编号：</span>
          <span class="content">{{ billDetail.billNo }}</span>
        </p>
        <p class="line"></p>
        <p class="pay-money">
          <span class="label">应结金额</span>
          <span class="seperator">¥</span>
          <span class="content">{{ billDetail.amount }}</span>
        </p>
        <div class="customer-box">
          <p class="w-1">
            <span class="label">客户名称</span>
            <span class="content">{{ billDetail.customerName }}</span>
          </p>
          <p class="w-1">
            <span class="label">计费周期</span>
            <span class="content">{{ billDetail.billingCycle }}</span>
          </p>
          <p class="w-1">
            <span class="label">对账方式</span>
            <span class="content">{{ billDetail.billingType }}</span>
          </p>
          <p class="w-1">
            <span class="label">账单生成时间</span>
            <span class="content">{{ billDetail.createTime }}</span>
          </p>
          <p class="w-1">
            <span class="label">账单确认时间</span>
            <span class="content">{{ billDetail.confirmTime }}</span>
          </p>
          <p class="w-1">
            <span class="label">调整金额</span>
            <span class="content">¥{{ billDetail.modifyAmount || "--" }} </span>
          </p>
          <p class="w-1">
            <span class="label">地址</span>
            <span class="content">{{ billDetail.customerAddress }}</span>
          </p>
        </div>
      </div>

      <div class="fee-detail">
        <div class="title">费用明细</div>
        <div
          class="cost-detail"
          v-for="(item, index) in billDetail.detailList"
          :key="index"
        >
          <div class="product-wrap">
            <p class="p-name">{{ item.productName }}</p>
            <p class="pay-money">
              <span class="label">应结金额</span>
              <span class="seperator">¥</span>
              <span class="content">{{ item.amount || "--" }} </span>
            </p>
          </div>
          <div class="shop-box">
            <p class="s-name">
              <span class="label">门店名称：</span>
              <span class="content">{{ item.deptName }}</span>
            </p>

            <p class="price">
              <span class="label">单价(点/年)：</span>
              <span class="content">{{ item.price }}</span>
            </p>
          </div>

          <p class="w-1">
            <span class="label">入网时间：</span>
            <span class="content">{{ item.firstUsedTime }}</span>
          </p>
          <p class="w-1">
            <span class="label">服务到期时间：</span>
            <span class="content">{{ item.expireTime }}</span>
          </p>
          <p class="w-1">
            <span class="label">计费开始时间：</span>
            <span class="content">{{ item.billTime }}</span>
          </p>
          <p class="w-1">
            <span class="label">计费截止时间：</span>
            <span class="content">{{ item.billExpireTime }}</span>
          </p>
        </div>
      </div>
    </div>
    <!-- footer -->
    <div class="footer">
      <div class="footer-box">
        <!-- <div
          class="sum"
          :class="{
            lineHight:
              billDetail.status === 2 &&
              billDetail.isPay === false &&
              time !== 0
          }"
        >
          合计：
        </div> -->
        <div
          class="amount"
          :class="{
            lineHight:
              billDetail.status === 2 &&
              billDetail.isPay === false &&
              time !== 0
          }"
        >
          ¥{{ billDetail.amount || "0" }}
        </div>
        <div
          class="left-time"
          v-if="
            billDetail.status === 2 && billDetail.isPay === false && time !== 0
          "
        >
          剩余<van-count-down
            @finish="onFinish"
            class="count-down"
            :time="time"
            format="HH 时 mm 分 ss 秒"
          />自动关闭
        </div>
      </div>
      <!-- <div
        class="left-time"
        v-if="
          billDetail.status === 2 && billDetail.isPay === false && time !== 0
        "
      >
        剩余<van-count-down
          @finish="onFinish"
          class="count-down"
          :time="time"
          format="HH 时 mm 分 ss 秒"
        />自动关闭
      </div> -->

      <div class="btn-wraps">
        <!-- <div
          v-if="
            billDetail.status === 1 &&
              billDetail.isPay === false &&
              type !== 'other'
          "
        >
          <van-button @click="otherPay" round size="small" class="otherPay"
            >找他人付</van-button
          >
        </div> -->
        <div
          v-if="billDetail.status === 2 && billDetail.isPay === false"
          class="pay"
        >
          <van-button
            color="#FF9900"
            @click="onClick"
            size="small"
            class="hasPay"
            >付款</van-button
          >
        </div>
        <div v-if="billDetail.isPay === true">
          <van-button disabled color="#FF9900" size="small" class="hasPay"
            >已付款</van-button
          >
        </div>
        <div v-if="billDetail.status === 1 && billDetail.isPay === false">
          <van-button
            color="#FF9900"
            @click="onClick"
            size="small"
            class="confirm"
            >账单确认并付款</van-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  NavBar,
  Field,
  Loading,
  Icon,
  GoodsAction,
  GoodsActionButton,
  Button,
  Dialog,
  CountDown,
  Toast
} from "vant";
import { h5BillDetail, getAllDict, confirmBill } from "../api/delivery";
import { readSettlementLog, payByOther } from "../api/bill";
import { findDeeply, getUserAgentInfo } from "../common/utils";
import moment from "moment";
import { statusList } from "../common/dictionary";

export default {
  components: {
    [NavBar.name]: NavBar,
    [Field.name]: Field,
    [Loading.name]: Loading,
    [Icon.name]: Icon,
    [GoodsActionButton.name]: GoodsActionButton,
    [GoodsAction.name]: GoodsAction,
    [Button.name]: Button,
    [Dialog.name]: Dialog,
    [CountDown.name]: CountDown,
    [Toast.name]: Toast
  },
  data() {
    return {
      billDetail: {
        detailList: []
      }, //详情信息
      billCycleList: [], //计费周期
      billTypeList: [], //对账方式
      billStatus: "",
      showHome: true,
      copyMsg: "收到来自万店掌的续费账单，请您点击查 看并完成缴费",
      type: "",
      time: 0,
      contractDetail: {}
    };
  },

  watch: {
    /**设置账单状态 */
    billDetail(val) {
      this.billStatus = statusList[val.status];
    }
  },

  /**加载字典项 */
  created() {
    this.getAllDict();
    this.readSettlementLog();
    this.type = this.$route.query.type;
  },

  mounted() {
    this.h5BillDetail();
    if (getUserAgentInfo().match(/MicroMessenger/i) == "micromessenger") {
      this.showHome = false;
    }
  },

  methods: {
    //查看埋点
    async readSettlementLog() {
      const res = await readSettlementLog();
      console.log(res);
    },

    //倒计时结束
    onFinish() {
      this.h5BillDetail();
    },

    //付款
    onClick() {
      if (this.contractDetail?.contractNo) {
        // 已存在合同，则跳转到合同详情页
        this.$router.push({
          name: "contractDetail",
          query: {
            contractId: this.contractDetail.id,
            billId: this.$route.query.billId,
            token: this.$route.query.token,
            isLogin: "1"
          }
        });
      } else {
        // 第一次付款，跳转到支付页面，合同还未生成
        this.$router.push({
          name: "payCost",
          query: {
            billId: this.$route.query.billId,
            token: this.$route.query.token,
            type: 2,
            specialType: "88" //随便定义的值，用来判断是否是从费用中心续费页面跳转过来的
          }
        });
      }
    },

    // 找他人付
    async otherPay() {
      const params = {
        billDetailURL: `${window.location.href}&type=other`
      };
      try {
        const res = await payByOther(params);
        if (res) {
          const copyText = `${this.copyMsg}\n${res}`;
          Dialog.confirm({
            message: copyText,
            confirmButtonText: "复制链接",
            beforeClose: (action, done) => {
              if (action === "confirm") {
                this.$copyText(copyText).then(
                  () => {
                    Toast.success("复制成功，快去转发吧");
                  },
                  () => {
                    Toast.fail("复制失败");
                  }
                );
                done(false);
              } else {
                done();
              }
            }
          });
        }
      } catch (err) {
        Toast.fail({
          duration: 2000,
          message: err.result
        });
      }
    },

    //账单确认并付款
    confirmBill() {
      let obj = {
        id: Number(this.$route.query.billId),
        isSystem: true, //是否是系统确认
        isOtherPay: this.type === "other" ? "1" : "0"
      };
      confirmBill(obj).then(res => {
        if (res) {
          this.onClick();
        }
      });
    },
    // 回退到app页面
    onClickLeft() {
      this.backToApp();
    },

    //获取账单详情
    async h5BillDetail() {
      try {
        let obj = {
          billId: this.$route.query.billId || "",
          isDetail: 1,
          isContract: 1
        };
        const res = await h5BillDetail(obj);
        this.billDetail = res;
        this.contractDetail = res.contractPayDetail || {};
        this.time = res?.cancelTime > 0 ? res.cancelTime : 0;
        this.billDetail.billingCycle = this.billCycleList
          ? this.billCycleList.find(item => item.value == res.billingCycle)
              ?.dname
          : "";
        this.billDetail.billingType = this.billTypeList
          ? this.billTypeList.find(
              item => item.value == this.billDetail.billingType
            )?.dname
          : "";
        this.billDetail.createTime = this.handleDate(
          this.billDetail.createTime,
          1
        );
        this.billDetail.confirmTime = this.handleDate(
          this.billDetail.confirmTime,
          1
        );
        res &&
          res.detailList.forEach(item => {
            item.firstUsedTime = this.handleDate(item.firstUsedTime, 0);
            item.expireTime = this.handleDate(item.expireTime, 0);
            item.billTime = this.handleDate(item.billTime, 0);
            item.billExpireTime = this.handleDate(item.billExpireTime, 0);
          });
      } catch (error) {
        console.log(error);
      }
    },

    /**处理时间格式 */
    handleDate(oldDate, type) {
      let DateType = ["YYYY-MM-DD", "YYYY-MM-DD HH:mm:ss"];
      let newDate = moment(oldDate).format(DateType[type]);
      return newDate;
    },

    /**
     * 全部字典项
     */
    async getAllDict() {
      const res = await getAllDict();
      sessionStorage.setItem("dictTree", JSON.stringify(res));
      if (res) {
        this.getOption();
      }
    },

    /**
     * 字典项
     */
    getOption() {
      let dictTree = JSON.parse(sessionStorage.getItem("dictTree"));
      const billCycleList = findDeeply(
        dictTree,
        item => item.type === "bill_cycle"
      );
      const billTypeList = findDeeply(
        dictTree,
        item => item.type === "bill_type"
      );
      this.billCycleList = [...billCycleList.children] || [];
      this.billTypeList = [...billTypeList.children] || [];
    },
    confirm() {
      console.log("点击了复制并转发按钮...");
    }
  }
};
</script>

<style lang="scss" scoped>
.detail {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
/deep/ .van-button {
  min-width: 78px;
}
/deep/.van-nav-bar__content {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  height: 44px;
}
/deep/.van-nav-bar__title {
  width: 111px;
  height: 24px;
  font-family: PingFangSC-Semibold;
  font-weight: bold;
  font-size: 17px;
  color: #333333;
  text-align: center;
}
/deep/ .van-nav-bar .van-icon {
  color: #333;
  font-size: 18px;
}
.body {
  box-sizing: border-box;
  padding: 0 16px;
  overflow-x: hidden;
  overflow-y: auto;
  flex: 1;
  background-color: rgb(247, 247, 247);
  .basic-info {
    background-color: #fff;
    padding: 12px 16px;
    margin: 12px 0;
    border-radius: 6px;
    .bill-status {
      font-size: 14px;
      color: #208bee;
      line-height: 22px;
      margin-bottom: 8px;
      font-weight: bold;
    }
    .body-title {
      font-size: 18px;
      color: #000000;
      font-weight: bold;
    }
    .bill-no {
      font-weight: 400;
      font-size: 14px;
      color: #7f7f7f;
      line-height: 22px;
      margin-top: 4px;
      margin-bottom: 12px;
    }
    .line {
      height: 1px;
      background-color: #e5e5e5;
    }
    .pay-money {
      height: 56px;
      line-height: 56px;
      text-align: right;
      .seperator {
        font-size: 14px;
        color: #ff9900;
        line-height: 22px;
      }
      .label {
        font-size: 14px;
        color: #333333;
        line-height: 22px;
        margin-right: 8px;
      }
      .content {
        font-size: 24px;
        color: #ff9900;
        line-height: 32px;
        font-family: D-DIN-PRO-Medium;
      }
    }
    .customer-box {
      background-image: linear-gradient(180deg, #fafafa 0%, #ffffff 100%);
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      padding: 12px;
      .w-1 {
        display: flex;
        justify-content: space-between;
        line-height: 20px;
        margin-bottom: 8px;
        .label {
          font-size: 12px;
          color: #7f7f7f;
        }
        .content {
          font-size: 12px;
          color: #333333;
          text-align: right;
          max-width: 200px;
        }
        &:last-child {
          margin-bottom: 0;
          .content {
            width: 188px;
          }
        }
      }
    }
  }
  .fee-detail {
    background-color: #fff;
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 20px;
    .title {
      font-size: 16px;
      color: #000000;
      line-height: 24px;
      font-weight: bold;
    }
    .cost-detail {
      padding: 12px 0;
      border-bottom: 1px solid #e5e5e5;
      .product-wrap {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        .p-name {
          font-size: 14px;
          color: #333333;
          line-height: 22px;
          font-weight: 600;
        }
        .pay-money {
          line-height: 22px;
          .label {
            font-size: 12px;
            color: #333333;
            line-height: 20px;
            margin-right: 8px;
          }
          .seperator {
            font-size: 12px;
            color: #ff9900;
            line-height: 20px;
          }
          .content {
            font-size: 18px;
            color: #ff9900;
            line-height: 22px;
          }
        }
      }
      .shop-box {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        .s-name {
          font-size: 12px;
          color: #333333;
          line-height: 20px;
        }
        .price {
          font-size: 12px;
          color: #7f7f7f;
          line-height: 20px;
        }
      }
      .w-1 {
        font-size: 12px;
        color: #7f7f7f;
        line-height: 20px;
        margin-bottom: 4px;
        display: flex;
        justify-content: space-between;
      }
    }
  }
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 52px;
  background: #ffffff;
  box-sizing: border-box;
  padding: 6px 16px;

  .btn-wraps {
    display: flex;
    .otherPay {
      width: 102px;
      height: 38px;
      background: rgba(255, 153, 0, 0.06);
      border-radius: 6px;
      color: #ff9900;
    }
    .pay,
    .confirm,
    .hasPay {
      width: 102px;
      height: 38px;
      background: #ff9900;
      border-radius: 6px;
    }
  }

  .footer-box {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    // .sum {
    //   width: 55px;
    //   height: 22px;
    //   font-family: PingFangSC-Regular;
    //   font-weight: Regular;
    //   font-size: 15px;
    //   color: #333333;
    //   letter-spacing: 0;
    // }
    // .amount {
    //   width: 54px;
    //   height: 25px;
    //   font-family: PingFangSC-Medium;
    //   font-weight: bold;
    //   font-size: 18px;
    //   color: #ff9900;
    //   letter-spacing: 0;
    // }
    .amount {
      font-size: 24px;
      color: #333333;
      font-weight: bold;
    }
    .left-time {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #333333;
      .count-down {
        font-size: 12px;
        line-height: 16px;
      }
    }
  }
  // /deep/.van-button--normal {
  //   padding: 0 4vw;
  //   font-size: 3.73333vw;
  //   width: 100%;
  //   height: 100%;
  // }
}
// .left-time {
//   position: absolute;
//   bottom: 1px;
//   left: 4.5vw;
//   color: #999999;
//   font-size: 11px;
//   .count-down {
//     color: #999999;
//     display: inline-block;
//     font-size: 11px;
//   }
// }
</style>
