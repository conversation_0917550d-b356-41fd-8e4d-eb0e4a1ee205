<template>
  <van-popup
    v-model="show"
    position="right"
    :style="{ height: '100%', width: '100%' }"
  >
    <div class="applyDelivery">
      <div class="header">
        <img src="../../../assets/img/ico_back-t.png" @click="onClickLeft" />
        <div class="title">延期服务详情页</div>
        <div></div>
      </div>

      <div class="main">
        <div class="title">
          <div class="title-no">
            <p>{{ detailData.delayNo }}</p>
          </div>

          <div class="title-name">
            <div class="title-name-one">
              <p>负责人</p>
              <p>{{ detailData.manager }}</p>
            </div>
            <div class="title-name-two">
              <p>客户名称</p>
              <p>{{ detailData.customerName }}</p>
            </div>
          </div>
        </div>

        <div class="basic">
          <div class="basic-title">基础信息</div>

          <div class="item">
            <label for="">服务选择</label>
            <div class="content">{{ detailData.softwareName }}</div>
          </div>

          <div class="item">
            <label for=""> 延期时长</label>
            <div class="content">
              {{ detailData.delayDay }}
            </div>
          </div>

          <div class="item">
            <label for="">创建人</label>
            <div class="content">{{ detailData.createBy }}</div>
          </div>

          <div class="item">
            <label for="">创建时间</label>
            <div class="content">{{ detailData.createTime }}</div>
          </div>
        </div>

        <div class="message">
          <div class="message-title">延期原因</div>
          <div class="message-content">{{ detailData.delayReason }}</div>
        </div>

        <!-- 延期服务清单 -->
        <div class="service_list">
          <div class="service_list_title">延期服务清单</div>
          <div
            class="service_content"
            v-for="(item, index) in detailData.serviceDelayList"
            :key="index"
          >
            <div class="item">
              <label for="">门店名称</label>
              <div class="content">{{ item.deptName }}</div>
            </div>
            <div class="item">
              <label for="">设备类型</label>
              <div class="content">
                {{
                  softData.find(e => e.value == item.hardwareModel)
                    ? softData.find(e => e.value == item.hardwareModel).dname
                    : ""
                }}
              </div>
            </div>
            <div class="item">
              <label for="">数量</label>
              <div class="content">{{ item.point }}</div>
            </div>
            <div class="item">
              <label for="">入网</label>
              <div class="content">{{ item.firstUsedTime }}</div>
            </div>
            <div class="item">
              <label for="">到期</label>
              <div class="content">{{ item.expireTime }}</div>
            </div>
            <div class="item">
              <label for="">延期日期</label>
              <div class="content">{{ item.delayTime }}</div>
            </div>

            <div class="item">
              <label for="">软件产品</label>
              <div class="content">{{ item.softwareName }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="footer" v-if="showBtn">
        <div class="btn">
          <van-button
            class="resect"
            v-if="rejectShow"
            @click="approveDelayForm(5)"
            >驳回</van-button
          >
          <van-button
            class="sure"
            color="#FF9900"
            v-if="auditShow"
            @click="approveDelayForm(3)"
            >审核</van-button
          >
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script>
import { Button, DatetimePicker, Field, Popup, Toast } from "vant";
import { getDelayFormDetail, approveDelayForm } from "@/api/postponeService";
import { getAllDict } from "@/api/delivery";
import { findDeeply } from "@/common/utils";
export default {
  components: {
    [Button.name]: Button,
    [DatetimePicker.name]: DatetimePicker,
    [Field.name]: Field,
    [Popup.name]: Popup,
    [Toast.name]: Toast
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    id: {
      type: [String, Number],
      default: ""
    },
    rejectShow: {
      type: Boolean,
      default: false
    },
    auditShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      detailData: {},
      showBtn: false,
      softData: []
    };
  },
  watch: {
    value(val) {
      if (val) {
        this.show = val;
        this.id && this.getDelayFormDetail(this.id);
      }
    },
    show(val) {
      if (!val) {
        this.$emit("input", val);
        setTimeout(() => {
          this.showBtn = false;
        }, 1000);
      }
    }
  },
  created() {
    this.getAllDict();
  },
  mounted() {},
  methods: {
    // 根据合同id获取合同详情
    async getDelayFormDetail(id) {
      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        duration: 0
      });
      try {
        const res = await getDelayFormDetail({ id });
        this.detailData = res;
        this.showBtn = res.state == 2;
        Toast.clear();
      } catch (error) {
        console.log(error);
      }
    },

    // 审批 状态（3:通过 5:驳回）
    async approveDelayForm(val) {
      Toast.loading({
        message: "审批中...",
        forbidClick: true,
        duration: 0
      });
      try {
        await approveDelayForm({
          id: this.id,
          state: val
        });
        Toast.success("审批成功");
        Toast.clear();
        this.show = false;
      } catch (error) {
        if (error) {
          Toast.fail("审批失败");
        }
      }
    },

    onClickLeft() {
      this.show = false;
    },

    /**
     * 全部字典项
     */
    async getAllDict() {
      const res = await getAllDict();
      if (res) {
        this.getOption(res);
      }
    },

    /**
     * 字典项
     */
    getOption(res) {
      const softData = findDeeply(
        res,
        item => item.type === "soft_service_code"
      );

      this.softData = [...softData.children] || [];
    }
  }
};
</script>

<style lang="scss" scoped>
.applyDelivery {
  display: flex;
  flex-direction: column;
  height: 100%;
  .header {
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    flex-shrink: 0;

    img {
      width: 20px;
      height: 20px;
    }

    .title {
      height: 24px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #000000;
      text-align: center;
      line-height: 24px;
    }
  }

  .main {
    flex: 1;
    background: #f7f7f7;
    padding: 16px;
    overflow: auto;
    .title {
      background: #fff;
      border-radius: 6px;
      margin-bottom: 12px;
      padding: 12px 16px;

      .title-no {
        border-bottom: 0.5px solid #e5e5e5;
        padding-bottom: 12px;
        :first-child {
          height: 26px;
          font-family: PingFangSC-Medium;
          font-weight: 550;
          font-size: 18px;
          color: #000000;
          line-height: 26px;
          margin-bottom: 4px;
        }
        :nth-child(2) {
          height: 22px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #7f7f7f;
          line-height: 22px;
        }
      }

      .title-name {
        border: 0.5px solid #e5e5e5;
        border-radius: 4px;
        margin-top: 12px;
        padding: 12px;

        .title-name-one,
        .title-name-two {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          :first-child {
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #7f7f7f;
            line-height: 20px;
          }

          :nth-child(2) {
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #333333;
            text-align: right;
            line-height: 20px;
          }
        }

        .title-name-two {
          margin-bottom: 0;
        }
      }
    }

    .basic {
      background: #fff;
      border-radius: 6px;
      margin-bottom: 12px;
      font-size: 13px;
      padding: 12px 16px;

      .basic-title {
        height: 48px;
        line-height: 48px;
        font-size: 16px;
        font-weight: 550;
      }

      .item {
        min-height: 40px;
        display: flex;
        align-items: center;

        label {
          font-size: 13px;
          width: 80px;
          color: #7f7f7f;
        }

        .content {
          flex: 1;
        }
      }

      label {
        color: #7f7f7f;
      }
    }

    .message {
      background: #fff;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 12px;

      .message-title {
        height: 48px;
        line-height: 48px;
        font-size: 16px;
        font-weight: 550;

        // 再标题后面加入必填标识符
        &:after {
          content: "*";
          color: #ff0000;
          padding-left: 3px;
        }
      }

      .message-content {
        word-wrap: break-word;
        word-break: break-all;
      }

      .message-box {
        border: 1px solid #e5e5e5;
        border-radius: 4px;
        margin-bottom: 12px;

        .van-cell {
          padding: 10px;
        }
      }

      .item {
        min-height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 13px;
      }

      label {
        color: #7f7f7f;
      }
    }

    .service_list {
      background: #fff;
      border-radius: 6px;
      padding: 16px;

      .service_list_title {
        height: 48px;
        line-height: 48px;
        font-size: 16px;
        font-weight: 550;
      }

      .service_content {
        border-bottom: 1px solid #e5e5e5;

        &:last-child {
          border-bottom: none;
        }

        .item {
          height: 40px;
          display: flex;
          align-items: center;

          label {
            font-size: 13px;
            width: 80px;
            color: #7f7f7f;
          }

          .content {
            flex: 1;
            font-size: 13px;
          }
        }
      }
    }
  }

  .footer {
    .btn {
      padding: 0 16px;
      height: 52px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .resect {
        width: 116px;
        height: 38px;
        background: #ffffff;
        border: 0.5px solid #e5e5e5;
        border-radius: 8px;
        font-size: 14px;
      }

      .sure {
        width: 215px;
        height: 38px;
        background: #ff9900;
        border-radius: 8px;
        font-size: 14px;
      }
    }
  }
}

.must {
  &:after {
    content: "*";
    color: #ff0000;
    padding-left: 3px;
  }
}
</style>
