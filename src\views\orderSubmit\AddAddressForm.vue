<template>
  <van-popup
    v-model="visible"
    position="bottom"
    :style="{ height: '100%' }"
    :close-on-click-overlay="false"
  >
    <div class="add-address-form">
      <!-- 头部 -->
      <div class="form-header">
        <van-icon name="arrow-left" @click="handleClose" />
        <span class="form-title">新增收货地址</span>
        <div class="placeholder"></div>
      </div>

      <!-- 表单内容 -->
      <div class="form-content">
        <van-form @submit="handleSubmit">
          <!-- 收货人 -->
          <van-field
            v-model="formData.name"
            name="name"
            label="收货人"
            placeholder="请输入收货人姓名"
            :rules="[{ required: true, message: '请输入收货人姓名' }]"
          />

          <!-- 联系方式 -->
          <van-field
            v-model="formData.mobile"
            name="mobile"
            label="联系方式"
            placeholder="请输入手机号码"
            type="tel"
            :rules="[
              { required: true, message: '请输入手机号码' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
            ]"
          />

          <!-- 地区选择 -->
          <van-field
            v-model="areaText"
            name="area"
            label="地区"
            placeholder="请选择省市区"
            readonly
            is-link
            @click="showAreaPicker = true"
            :rules="[{ required: true, message: '请选择地区' }]"
          />

          <!-- 详细地址 -->
          <van-field
            v-model="formData.address"
            name="address"
            label="详细地址"
            type="textarea"
            placeholder="请输入详细地址信息"
            rows="3"
            autosize
            :rules="[{ required: true, message: '请输入详细地址' }]"
          />
        </van-form>
      </div>

      <!-- 保存按钮 - 固定在底部 -->
      <div class="form-footer">
        <van-button
          type="warning"
          block
          :loading="loading"
          @click="handleSubmit"
          class="save-button"
        >
          保存
        </van-button>
      </div>

      <!-- 地区选择器 -->
      <van-popup v-model="showAreaPicker" position="bottom">
        <van-area
          :area-list="areaList"
          @confirm="onAreaConfirm"
          @cancel="showAreaPicker = false"
        />
      </van-popup>
    </div>
  </van-popup>
</template>

<script>
import { Field, Form, Button, Popup, Icon, Area, Toast } from "vant";
// 引入获取省市区地址的API
import { getAllAreaInfo } from "../../api/orderManage";

export default {
  name: "AddAddressForm",
  components: {
    [Field.name]: Field,
    [Form.name]: Form,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Icon.name]: Icon,
    [Area.name]: Area
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: this.value,
      loading: false,
      showAreaPicker: false,
      formData: {
        name: "",
        mobile: "",
        province: "",
        city: "",
        area: "",
        address: "",
        provinceCode: "",
        cityCode: "",
        areaCode: ""
      },
      areaList: {
        // 省市区数据，从API获取后转换为vant Area组件需要的格式
        province_list: {},
        city_list: {},
        county_list: {}
      }
    };
  },
  computed: {
    areaText() {
      const { province, city, area } = this.formData;
      if (province && city && area) {
        return `${province} ${city} ${area}`;
      }
      return "";
    }
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit("input", val);
      if (!val) {
        this.resetForm();
      } else {
        // 弹窗打开时，如果地址数据为空则加载
        if (Object.keys(this.areaList.province_list).length === 0) {
          this.loadAreaData();
        }
      }
    }
  },
  mounted() {
    // 组件挂载时预加载地址数据
    this.loadAreaData();
  },
  methods: {
    // 关闭弹窗
    handleClose() {
      this.visible = false;
    },

    // 加载省市区地址数据
    async loadAreaData() {
      try {
        const res = await getAllAreaInfo();
        // 将API返回的数据转换为vant Area组件需要的格式
        this.convertAreaData(res);
      } catch (error) {
        Toast("加载地址数据失败，请重试");
      }
    },

    // 转换地址数据格式
    convertAreaData(data) {
      const provinceList = {};
      const cityList = {};
      const countyList = {};

      // 遍历省份数据
      data.forEach(province => {
        // 添加省份
        provinceList[province.code] = province.shortName;

        // 遍历城市数据
        if (province.children && province.children.length > 0) {
          province.children.forEach(city => {
            // 添加城市
            cityList[city.code] = city.shortName;

            // 遍历区县数据
            if (city.children && city.children.length > 0) {
              city.children.forEach(county => {
                // 添加区县
                countyList[county.code] = county.shortName;
              });
            }
          });
        }
      });

      // 更新组件数据
      this.areaList = {
        province_list: provinceList,
        city_list: cityList,
        county_list: countyList
      };
    },

    // 地区选择确认
    onAreaConfirm(values) {
      this.formData.province = values[0].name;
      this.formData.city = values[1].name;
      this.formData.area = values[2].name;
      this.formData.provinceCode = values[0].code;
      this.formData.cityCode = values[1].code;
      this.formData.areaCode = values[2].code;
      this.showAreaPicker = false;
    },

    // 提交表单
    async handleSubmit() {
      try {
        this.loading = true;

        // 表单验证
        if (!this.validateForm()) {
          return;
        }

        // 构造提交数据，按照API要求的格式
        const submitData = {
          type: 2, // 地址类型：2收货地址
          groupId: 1084, // 客户ID
          name: this.formData.name, // 收件人姓名
          mobile: this.formData.mobile, // 手机号
          province: this.formData.province, // 省
          city: this.formData.city, // 市
          area: this.formData.area, // 区
          cantonCode: this.formData.areaCode, // 行政区编码
          address: this.formData.address // 详细地址
        };

        // 触发保存事件，由父组件处理实际的保存逻辑
        // 传递一个回调函数给父组件，让父组件控制成功/失败后的行为
        this.$emit("save", {
          data: submitData,
          onSuccess: () => {
            // 保存成功后关闭弹窗并重置loading状态
            this.loading = false;
            this.visible = false;
          },
          onError: (errorMsg) => {
            // 保存失败时只重置loading状态，不关闭弹窗
            this.loading = false;
            Toast(errorMsg || "保存失败，请重试");
          }
        });

      } catch (error) {
        // 本地异常处理（如表单验证失败等）
        this.loading = false;
        Toast("操作失败，请重试");
      }
    },

    // 表单验证
    validateForm() {
      if (!this.formData.name.trim()) {
        Toast("请输入收货人姓名");
        return false;
      }

      if (!this.formData.mobile.trim()) {
        Toast("请输入手机号码");
        return false;
      }

      // 手机号格式验证
      const mobileReg = /^1[3-9]\d{9}$/;
      if (!mobileReg.test(this.formData.mobile)) {
        Toast("请输入正确的手机号码");
        return false;
      }

      if (
        !this.formData.province ||
        !this.formData.city ||
        !this.formData.area
      ) {
        Toast("请选择省市区");
        return false;
      }

      if (!this.formData.address.trim()) {
        Toast("请输入详细地址");
        return false;
      }

      return true;
    },


    // 重置表单
    resetForm() {
      this.formData = {
        name: "",
        mobile: "",
        province: "",
        city: "",
        area: "",
        address: "",
        provinceCode: "",
        cityCode: "",
        areaCode: ""
      };
    }
  }
};
</script>

<style scoped>
.add-address-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f7f7f7;
}

.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #eee;
}

.form-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.placeholder {
  width: 24px;
}

.form-content {
  flex: 1;
  padding: 16px 0;
  background: white;
  margin-top: 8px;
  overflow-y: auto;
}

.form-footer {
  padding: 20px;
  background: white;
  border-top: 1px solid #eee;
  /* 确保按钮固定在底部 */
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* 保存按钮样式 - 橙色圆角按钮 */
.save-button {
  background: #ff9900 !important;
  border: none !important;
  border-radius: 6px !important;
  height: 44px !important;
  color: white !important;
}

.save-button:active {
  background: #e68900 !important;
}

/* 表单字段样式调整 */
.add-address-form /deep/ .van-field__label {
  width: 80px;
  color: #333;
  font-weight: 500;
}

.add-address-form /deep/ .van-field__control {
  color: #333;
}

.add-address-form /deep/ .van-field__control::placeholder {
  color: #c8c8c8;
}
</style>
