import axios from "axios";
import qs from "qs";
import { response<PERSON><PERSON><PERSON> } from "./response";
import <PERSON><PERSON> from "js-cookie";

axios.defaults.timeout = 10000;

/**
 * 请求头参数规则 token + client + version + language + timezone
 */
function getAuthenticator() {
  return Cookie.get()["token"] || "";
}

function handleToken() {
  let tokenAuth = sessionStorage.getItem("tokenAuth");
  if (tokenAuth) {
    let headers = {
      token: getAuthenticator()
    };
    return headers;
  } else {
    let tokenLength = getAuthenticator().length;
    let headers = {
      authenticator: getAuthenticator(),
      "Ovo-Authorization": `${getAuthenticator()} web 1.0 SIMPLIFIED_CHINESE GMT+8:00`
    };
    if (tokenLength > 40) {
      delete headers["Ovo-Authorization"];
    } else {
      delete headers["authenticator"];
    }
    return headers;
  }
}

function removeUndefinedAndNull(data) {
  const newData = {};
  for (let key in data) {
    if (typeof data[key] === "string") {
      if (data[key] !== "undefined" && data[key] !== "null") {
        newData[key] = data[key];
      }
    } else if (data[key] !== null && data[key] !== undefined) {
      newData[key] = data[key];
    }
  }
  return newData;
}

// 小程序header
function handleCardToken() {
  let headers = {
    token: getAuthenticator()
  };
  return headers;
}

// 物流header
function handleLogistToken() {
  let headers = {
    token: getAuthenticator()
  };
  return headers;
}

// 小程序get请求
export const getCard = async function(url, params = {}) {
  const cleanData = removeUndefinedAndNull(params);
  let res = await axios.get(url, {
    headers: handleCardToken(),
    params: cleanData
  });
  return responseHandler(res);
};

/**
 * 请求头为token的get请求
 */
export const getLogist = async function(url, params = {}) {
  let res = await axios.get(url, {
    headers: handleLogistToken(),
    params
  });
  return responseHandler(res);
};

/**
 * 请求头为token的postJson请求
 */
export const postJsonLogist = async function(url, params = {}) {
  let res = await axios.post(url, params, {
    headers: handleLogistToken()
  });
  return responseHandler(res);
};

/**
 * 普通get请求
 */
export const get = async function(url, params = {}) {
  let res = await axios.get(url, {
    headers: handleToken(),
    params
  });
  return responseHandler(res);
};

/**
 * 普通post请求
 */
export const post = async function(url, params = {}) {
  let res = await axios.post(url, qs.stringify(params), {
    headers: handleToken()
  });
  return responseHandler(res);
};

/**
 * 普通post json请求
 */
export const postJson = async function(url, params = {}) {
  let res = await axios.post(url, params, {
    headers: handleToken()
  });
  return responseHandler(res);
};

//删除deleteReq请求
export const deleteReq = async function(url, params = {}) {
  let res = await axios.delete(url, {
    params,
    headers: handleToken()
  });
  return responseHandler(res);
};

//删除deleteReq json请求
export const deleteReqJson = async function(url, params = {}) {
  let res = await axios.delete(url, {
    data: params,
    headers: handleToken()
  });
  return responseHandler(res);
};

//更新putReq请求
export const putReq = async function(url, params = {}) {
  let res = await axios.put(url, params, {
    headers: handleToken()
  });
  return responseHandler(res);
};

// 不带token的post请求
export const postNoToken = async function(url, params = {}) {
  let res = await axios.post(url, params, {
    headers: {
      "Content-Type": "application/json"
    }
  });
  return responseHandler(res);
};
