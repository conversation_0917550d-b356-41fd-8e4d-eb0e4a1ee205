import Vue from "vue";
import VueRouter from "vue-router";

Vue.use(VueRouter);
// 解决编程式路由往同一地址跳转时会报错的情况
const originalPush = VueRouter.prototype.push;
const originalReplace = VueRouter.prototype.replace;

// push
VueRouter.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject)
    return originalPush.call(this, location, onResolve, onReject);
  return originalPush.call(this, location).catch(err => err);
};

//replace
VueRouter.prototype.replace = function push(location, onResolve, onReject) {
  if (onResolve || onReject)
    return originalReplace.call(this, location, onResolve, onReject);
  return originalReplace.call(this, location).catch(err => err);
};

const baseUrl = process.env.NODE_ENV === "production" ? "/notice/" : "/notice/";

const routes = [
  {
    path: "/",
    name: "index",
    component: resolve => require(["../components/delivery.vue"], resolve),
    meta: {
      title: "物流追踪"
    }
  },
  {
    path: "/details",
    name: "details",
    component: resolve => require(["../components/details.vue"], resolve),
    meta: {
      title: "物流详情"
    }
  },
  {
    path: "/wxLogin",
    name: "wxLogin",
    component: resolve => require(["../views/wxLogin/index.vue"], resolve)
  },
  {
    path: "/contractDetail",
    name: "contractDetail",
    component: resolve =>
      require(["../components/contractDetail.vue"], resolve),
    meta: {
      title: "合同/订单详情"
    }
  },
  {
    path: "/billDetail",
    name: "billDetail",
    component: resolve => require(["../components/billDetail.vue"], resolve),
    meta: {
      title: "账单详情"
    }
  },
  {
    path: "/pay",
    name: "payCost",
    component: resolve => require(["../components/payCost.vue"], resolve),
    meta: {
      title: "支付中心"
    }
  },
  {
    path: "/paySuccess",
    name: "paySuccess",
    component: resolve => require(["../components/paySuccess.vue"], resolve),
    meta: {
      title: "支付详情"
    }
  },
  {
    path: "/audit",
    name: "audit",
    component: resolve =>
      require(["../components/auditMiddlePage.vue"], resolve),
    meta: {
      title: "万店掌"
    }
  },
  {
    path: "/urgebill",
    name: "urgebill",
    component: resolve =>
      require(["../components/urgeBillMiddlePage.vue"], resolve),
    meta: {
      title: "万店掌"
    }
  },
  {
    path: "/paymentLanded",
    name: "paymentLanded",
    component: resolve => require(["../components/paymentLanded.vue"], resolve),
    meta: {
      title: "付款"
    }
  },
  {
    path: "/paymentSuccess",
    name: "paymentSuccess",
    component: resolve =>
      require(["../components/paymentSuccess.vue"], resolve),
    meta: {
      title: "付款成功"
    }
  },
  {
    path: "/paymentCost",
    name: "paymentCost",
    component: resolve => require(["../components/paymentCost.vue"], resolve)
    // meta: {
    //   title: '付款失败'
    // }
  },
  {
    path: "/otherScan",
    name: "otherScan",
    component: resolve => require(["../components/otherScan.vue"], resolve)
    // meta: {
    //   title: "其他"
    // }
  },
  {
    path: "/login",
    name: "login",
    component: resolve =>
      require(["../views/hrmPage/login/index.vue"], resolve),
    meta: {
      title: "登录"
    }
  },
  {
    path: "/hrmPage",
    name: "hrmPage",
    redirect: "/hrmPage/baseInfo",
    component: resolve => require(["../views/hrmPage/index.vue"], resolve),
    meta: {
      title: "hrm"
    },
    children: [
      {
        path: "baseInfo",
        name: "baseInfo",
        component: resolve =>
          require(["../views/hrmPage/baseInfo/index.vue"], resolve),
        meta: {
          title: "基础信息"
        }
      },
      {
        path: "mainExperience",
        name: "mainExperience",
        component: resolve =>
          require(["../views/hrmPage/mainExperience/index.vue"], resolve),
        meta: {
          title: "主要经历"
        }
      },
      {
        path: "bankAccount",
        name: "bankAccount",
        component: resolve =>
          require(["../views/hrmPage/bankAccount/index.vue"], resolve),
        meta: {
          title: "银行账号"
        }
      },
      {
        path: "positionInfor",
        name: "positionInfor",
        component: resolve =>
          require(["../views/hrmPage/positionInfor/index.vue"], resolve),
        meta: {
          title: "入职资料"
        }
      }
    ]
  },
  {
    path: "/successPage",
    name: "successPage",
    component: resolve =>
      require(["../views/hrmPage/successPage/index.vue"], resolve),
    meta: {
      title: "提交成功"
    }
  },
  {
    path: "/submitted",
    name: "submitted",
    component: resolve =>
      require(["../views/hrmPage/submitted/index.vue"], resolve),
    meta: {
      title: "已提交"
    }
  },
  {
    path: "/CardJump",
    name: "CardJump",
    component: resolve => require(["../views/CardJump/index.vue"], resolve),
    meta: {
      title: "名片夹"
    }
  },
  {
    path: "/cardVideoPlay",
    name: "cardVideoPlay",
    component: resolve =>
      require(["../views/cardVideoPlay/index.vue"], resolve),
    meta: {
      title: "视频播放页"
    }
  },
  // 发票管理
  {
    path: "/invoiceManage",
    name: "invoiceManage",
    component: resolve =>
      require(["../views/invoiceManage/index.vue"], resolve),
    meta: {
      title: "发票管理"
    }
  },
  // 申请开票
  {
    path: "/applyBill",
    name: "applyBill",
    component: resolve =>
      require(["../views/invoiceManage/applyBill.vue"], resolve),
    meta: {
      title: "申请开票"
    }
  },
  // 添加抬头
  {
    path: "/addBill",
    name: "addBill",
    component: resolve =>
      require(["../views/invoiceManage/addBill.vue"], resolve),
    meta: {
      title: "申请开票"
    }
  },
  // 终端登录
  {
    path: "/enterNetworkLogin",
    name: "enterNetworkLogin",
    component: resolve => require(["../views/enterNetwork/login.vue"], resolve),
    meta: {
      title: "登录"
    }
  },
  // 终端注册
  {
    path: "/enterNetworkRegister",
    name: "enterNetworkRegister",
    component: resolve =>
      require(["../views/enterNetwork/register.vue"], resolve),
    meta: {
      title: "注册"
    }
  },
  {
    path: "/scan",
    name: "scan",
    component: resolve =>
      require(["../views/enterNetwork/scanCopy.vue"], resolve),
    meta: {
      title: "扫码"
    }
  },
  // 结果页
  {
    path: "/result",
    name: "result",
    component: resolve => require(["../views/enterNetwork/result.vue"], resolve)
  },
  {
    path: "/realNameAuth",
    name: "realNameAuth",
    component: resolve => require(["../views/realNameAuth/index.vue"], resolve)
  },
  {
    path: "/personAuthent",
    name: "personAuthent",
    component: resolve =>
      require(["../views/realNameAuth/personAuthent/index.vue"], resolve),
    meta: {
      title: "个人认证"
    }
  },
  {
    path: "/personAuthentNext",
    name: "personAuthentNext",
    component: resolve =>
      require(["../views/realNameAuth/personAuthentNext/index.vue"], resolve),
    meta: {
      title: "个人认证"
    }
  },
  {
    path: "/groupAuthent",
    name: "groupAuthent",
    component: resolve =>
      require(["../views/realNameAuth/groupAuthent/index.vue"], resolve),
    meta: {
      title: "企业认证"
    }
  },
  // 法人认证
  {
    path: "/legalPersonAuthent",
    name: "legalPersonAuthent",
    component: resolve =>
      require(["../views/realNameAuth/legalPersonAuthent/index.vue"], resolve),
    meta: {
      title: "法人认证"
    }
  },
  // 授权书认证
  {
    path: "/noLegalPersonAuthent",
    name: "noLegalPersonAuthent",
    component: resolve =>
      require([
        "../views/realNameAuth/noLegalPersonAuthent/index.vue"
      ], resolve),
    meta: {
      title: "授权书认证"
    }
  },
  // 物流追踪登录页
  {
    path: "/logistics",
    name: "logisticsLogin",
    component: resolve =>
      require(["../views/logistics/logisticsLogin/index.vue"], resolve),
    meta: {
      title: "登录"
    }
  },

  // 物流追踪查询页
  {
    path: "/logisticsSearch",
    name: "logisticsSearch",
    component: resolve =>
      require(["../views/logistics/logisticsSearch/index.vue"], resolve),
    meta: {
      title: "查询"
    }
  },

  // 物流追踪详情页
  {
    path: "/logisticsDetail",
    name: "logisticsDetail",
    component: resolve =>
      require(["../views/logistics/logisticsDetail/index.vue"], resolve),
    meta: {
      title: "详情"
    }
  },
  // 巡店续费
  {
    path: "/renewal",
    name: "renewal",
    component: resolve => require(["../components/renewal.vue"], resolve),
    meta: {
      title: "巡店续费"
    }
  },
  // 组织架构
  {
    path: "/organization",
    name: "organization",
    component: resolve => require(["../components/organazition.vue"], resolve),
    meta: {
      title: "组织架构"
    }
  },
  // 服务订阅详情
  {
    path: "/serviceSubDetail",
    name: "serviceSubDetail",
    component: resolve =>
      require(["../views/serviceSubDetail/index.vue"], resolve),
    meta: {
      title: "服务订阅详情"
    }
  },
  {
    path: "/addressManage",
    name: "addressManage",
    component: resolve =>
      require(["../views/serviceSubDetail/addressManage/index.vue"], resolve),
    meta: {
      title: "地址管理"
    }
  },
  {
    path: "/addAddress",
    name: "addAddress",
    component: resolve =>
      require(["../views/serviceSubDetail/addAddress/index.vue"], resolve),
    meta: {
      title: "地址信息"
    }
  },
  {
    path: "/serviceInterpage",
    name: "serviceInterpage",
    component: resolve =>
      require([
        "../views/serviceSubDetail/serviceInterpage/index.vue"
      ], resolve),
    meta: {
      title: ""
    }
  },
  {
    path: "/serviceFail",
    name: "serviceFail",
    component: resolve =>
      require(["../views/serviceSubDetail/serviceFail/index.vue"], resolve),
    meta: {
      title: ""
    }
  },
  {
    path: "/refund",
    name: "refund",
    component: resolve => require(["../views/refund/refund.vue"], resolve),
    meta: {
      title: "收款记录"
    }
  },

  // 试用合同
  {
    path: "/tryContract",
    name: "tryContract",
    component: resolve => require(["../views/tryContract/index.vue"], resolve),
    meta: {
      title: "试用合同"
    }
  },
  {
    path: "/tryDetail",
    name: "tryDetail",
    component: resolve =>
      require(["../views/tryContract/tryDetail/index.vue"], resolve),
    meta: {
      title: "试用合同/订单详情"
    }
  },
  {
    path: "/bankStatement",
    name: "bankStatement",
    component: resolve =>
      require(["../views/bankStatement/index.vue"], resolve),
    meta: {
      title: "银行流水"
    }
  },
  {
    path: "/relatedWater",
    name: "relatedWater",
    component: resolve => require(["../views/relatedWater/index.vue"], resolve),
    meta: {
      title: "关联流水"
    }
  },
  {
    path: "/applyDelivery",
    name: "applyDelivery",
    component: resolve =>
      require(["../views/advanceShipment/applyDelivery/index.vue"], resolve),
    meta: {
      title: "提前发货申请"
    }
  },
  // 提前发货列表页
  {
    path: "/advanceShipment",
    name: "advanceShipment",
    component: resolve =>
      require(["../views/advanceShipment/index.vue"], resolve),
    meta: {
      title: "提前发货列表"
    }
  },

  // 这个物流跟踪页取代sxf写的那套，目前保留sxf写的那套给web端使用
  {
    path: "/logisticsTrack",
    name: "logisticsList",
    component: resolve =>
      require(["../views/logisticsTrack/logisticsList"], resolve),
    meta: {
      title: "物流跟踪"
    }
  },
  {
    path: "/customerPropertyList",
    name: "customerPropertyList",
    component: resolve => require(["../views/customerProperty"], resolve),
    meta: {
      title: "客户属性分析"
    }
  },
  {
    path: "/customerPropertyDetail",
    name: "customerPropertyDetail",
    component: resolve =>
      require(["../views/customerProperty/components/detail"], resolve),
    meta: {
      title: "客户详情"
    }
  },
  {
    path: "/postponeService",
    name: "postponeService",
    component: resolve =>
      require(["../views/postponeService/index.vue"], resolve),
    meta: {
      title: "延期服务"
    }
  },
  {
    path: "/accountReceivable",
    name: "accountReceivable",
    component: resolve =>
      require(["../views/accountReceivable/index.vue"], resolve),
    redirect: "/accountReceivable/accountFirst",
    meta: {
      title: "应收账款"
    },
    children: [
      {
        path: "/accountReceivable/accountFirst",
        name: "accountFirst",
        component: resolve =>
          require([
            "../views/accountReceivable/accountFirst/index.vue"
          ], resolve),
        meta: {
          index: 1
        }
      },
      {
        path: "/accountReceivable/accountSecond",
        name: "accountSecond",
        component: resolve =>
          require([
            "../views/accountReceivable/accountSecond/index.vue"
          ], resolve),
        meta: {
          index: 2
        }
      },
      {
        path: "/accountReceivable/accountThird",
        name: "accountThird",
        component: resolve =>
          require([
            "../views/accountReceivable/accountThird/index.vue"
          ], resolve),
        meta: {
          index: 3
        }
      },
      {
        path: "/accountReceivable/accountFourth",
        name: "accountFourth",
        component: resolve =>
          require([
            "../views/accountReceivable/accountFourth/index.vue"
          ], resolve),
        meta: {
          index: 4
        }
      }
    ]
  },
  {
    path: "/intelligence",
    name: "intelligence",
    component: resolve => require(["../views/intelligence/index.vue"], resolve),
    meta: {
      title: "谍报中心"
    }
  },
  // 储值
  {
    path: "/recharge",
    name: "recharge",
    component: resolve => require(["../views/recharge/index.vue"], resolve)
  },
  {
    path: "/businessOpportunity",
    name: "businessOpportunity",
    component: resolve =>
      require(["../views/businessOpportunity/index.vue"], resolve),
    meta: {
      title: "谍报列表"
    }
  },
  {
    path: "/businessDetail",
    name: "businessDetail",
    component: resolve =>
      require(["../views/businessOpportunity/detail.vue"], resolve),
    meta: {
      title: "谍报详情"
    }
  },
  {
    path: "/authCodeMan",
    name: "authCodeMan",
    component: resolve => require(["../views/authCodeMan/index.vue"], resolve),
    meta: {
      title: "授权码管理"
    }
  },
  {
    path: "/contractRepayment",
    name: "contractRepayment",
    component: resolve =>
      require(["../views/contractRepayment/index.vue"], resolve),
    meta: {
      title: "合同/回款额"
    }
  },
  {
    path: "/contractRepaymentTeam",
    name: "contractRepaymentTeam",
    component: resolve =>
      require(["../views/contractRepaymentTeam/index.vue"], resolve),
    meta: {
      title: "合同/回款额"
    }
  },
  {
    path: "/contributionRepaymentDetail",
    name: "contributionRepaymentDetail",
    component: resolve =>
      require(["../views/contributionRepaymentDetail/index.vue"], resolve),
    meta: {
      title: "贡献回款详情"
    }
  },
  // 订单管理
  {
    path: "/orderManage",
    name: "orderManage",
    component: resolve => require(["../views/orderManage/index.vue"], resolve),
    meta: {
      title: "订单管理"
    }
  },
  // 订单提交
  {
    path: "/orderSubmit",
    name: "orderSubmit",
    component: resolve => require(["../views/orderSubmit/index.vue"], resolve),
    meta: {
      title: "订单提交"
    }
  },
  // 订单确认
  {
    path: "/orderConfirm",
    name: "orderConfirm",
    component: resolve => require(["../views/orderConfirm/index.vue"], resolve),
    meta: {
      title: "订单确认"
    }
  },
];

// 路由配置
const RouterConfig = {
  mode: "history",
  base: baseUrl,
  routes: routes
};

const router = new VueRouter(RouterConfig);

export default router;
