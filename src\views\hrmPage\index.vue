<template>
  <div class="hrmPage">
    <!-- header -->
    <header>
      <ul v-for="(item, index) in stepContent" :key="index">
        <div class="img-box">
          <li :class="item.active ? 'active-li' : 'w-li'">
            <div class="bg-color">
              <img :src="item.active ? item.activeImg : item.img" alt="" />
            </div>
          </li>
          <p :class="item.active ? 'active-text' : 'text'">{{ item.text }}</p>
        </div>
        <div v-if="index !== stepContent.length - 1" class="line"></div>
      </ul>
    </header>

    <!-- main -->
    <div class="main">
      <router-view ref="hrm"></router-view>
    </div>

    <!-- footer -->
    <footer>
      <van-button type="primary" class="last" @click="last">上一步</van-button>
      <van-button
        v-show="showTempSave"
        type="primary"
        style="margin: 0 5px"
        class="last"
        @click="tempSave"
        >暂存</van-button
      >
      <van-button type="primary" class="next" @click="next">{{
        nextTxt
      }}</van-button>
    </footer>
  </div>
</template>

<script>
import { Popup, NavBar, Loading, Button, Icon, Overlay } from "vant";
export default {
  components: {
    [NavBar.name]: NavBar,
    [Overlay.name]: Overlay,
    [Popup.name]: Popup,
    [Loading.name]: Loading,
    [Button.name]: Button,
    [Icon.name]: Icon,
  },
  data() {
    return {
      stepContent: [
        {
          text: "基础信息",
          name: "baseInfo",
          img: require("../../assets/hrm/info.png"),
          active: false, //是否激活
          activeImg: require("../../assets/hrm/activeInfo.png"),
        },
        {
          text: "主要经历",
          name: "mainExperience",
          img: require("../../assets/hrm/mainExper.png"),
          active: false, //是否激活
          activeImg: require("../../assets/hrm/activeMain.png"),
        },
        {
          text: "银行账号",
          name: "bankAccount",
          img: require("../../assets/hrm/bank.png"),
          active: false, //是否激活
          activeImg: require("../../assets/hrm/activeBank.png"),
        },
        {
          text: "附件资料",
          name: "positionInfor",
          img: require("../../assets/hrm/attement.png"),
          active: false, //是否激活
          activeImg: require("../../assets/hrm/activeAttement.png"),
        },
      ],
      routerName: "", //路由name
      nextTxt: "下一步",
      showTempSave: false,
    };
  },
  watch: {
    $route: {
      handler(val) {
        this.routerName = val.name;
        this.stepContent.forEach((item) => {
          if (item.name == val.name) {
            item.active = true;
          } else {
            item.active = false;
          }
        });

        if (this.routerName == "positionInfor") {
          this.showTempSave = true;
          this.nextTxt = "提交";
        } else {
          this.showTempSave = false;
          this.nextTxt = "下一步";
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 根据路由判断下一步跳哪个页面
    next() {
      switch (this.routerName) {
        case "baseInfo":
          this.$refs.hrm.updateStaff();
          break;
        case "mainExperience":
          this.$refs.hrm.saveEduct();
          break;
        case "bankAccount":
          this.$refs.hrm.submit();
          break;
        case "positionInfor":
          this.$refs.hrm.submit();
          break;
      }
    },

    // 上一步
    last() {
      switch (this.routerName) {
        case "baseInfo":
          break;
        case "mainExperience":
          this.$router.push({
            name: "baseInfo",
            query: {
              id: this.$route.query.id,
            },
          });
          break;
        case "bankAccount":
          this.$router.push({
            name: "mainExperience",
            query: {
              id: this.$route.query.id,
            },
          });
          break;
        case "positionInfor":
          this.$router.push({
            name: "bankAccount",
            query: {
              id: this.$route.query.id,
            },
          });
          break;
      }
    },

    // // 暂存
    tempSave() {
      this.$refs.hrm.saveOrUpdateAttachment();
    },
  },
};
</script>

<style lang="scss" scoped>
.hrmPage {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  header {
    height: 89px;
    background: #ffffff;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 8px;

    ul {
      margin: 0;
      padding: 0;
      position: relative;

      .img-box {
        display: flex;
        flex-direction: column;
        align-items: center;

        .w-li {
          list-style: none;
          margin: 0;
          padding: 0;
          display: inline-block;
          // position: relative;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #f0f0f0;
          box-shadow: 0 0 20px 0 rgba(169, 173, 181, 0.2);
          display: flex;
          justify-content: center;
          align-items: center;

          .bg-color {
            width: 18px;
            height: 18px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
        // 激活状态
        .active-li {
          list-style: none;
          margin: 0;
          padding: 0;
          display: inline-block;
          // position: relative;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #ff9900;
          box-shadow: 0 0 20px 0 rgba(169, 173, 181, 0.2);
          display: flex;
          justify-content: center;
          align-items: center;

          .bg-color {
            width: 18px;
            height: 18px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }

        .text {
          margin-top: 8px;
          // width: 48px;
          height: 17px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #7f7f7f;
          text-align: right;
        }

        // 文字激活状态
        .active-text {
          margin-top: 8px;
          // width: 48px;
          height: 17px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #f2b951;
          text-align: right;
        }
      }

      .line {
        position: absolute;
        width: 56px;
        height: 2px;
        background: #e5e5e5;
        top: 27%;
        margin-bottom: 31px;
        left: 11.2vw;
      }
    }
  }

  .main {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #fff;
  }

  footer {
    height: 56px;
    background: #ffffff;
    box-shadow: 0 0 0 0 #f0f3fa;
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-top: 1px solid #f0f3fa;

    .last {
      background-color: #f0f0f0;
      border: 1px solid #f0f0f0;
      width: 138px;
      height: 40px;
      color: #7f7f7f;
      font-weight: 500;
      font-size: 14px;
      text-align: center;
      border-radius: 21px;
    }
    .next {
      width: 197px;
      height: 40px;
      background: #ff9900;
      border-radius: 21px;
      border: 1px solid #ff9900;
    }
  }
}
</style>