<template>
  <div class="login-page">
    <!-- header -->
    <header>
      <img src="../../../assets/hrm/up.png" alt="" />
    </header>
    <!-- main -->
    <main>
      <!-- title -->
      <div class="title">
        <p class="title-solt">欢迎加入</p>
        <p class="title-solt">万店掌大家庭</p>
        <p class="title-txt">成为世界一流的的零售科技服务公司</p>
      </div>
      <!-- input -->
      <div class="w-input">
        <van-field
          clear-trigger="input-color"
          label=""
          readonly
          v-model="encryptPhone"
          placeholder="请输入手机号码"
        />
        <van-field
          center
          clearable
          label=""
          v-model="verifyCode"
          placeholder="请输入验证码"
        >
          <template #button>
            <van-button v-show="show" size="small" type="primary" @click="send"
              >发送验证码</van-button
            >
            <van-button v-show="!show" size="small" type="primary" disabled
              >{{ count }}s</van-button
            >
          </template>
        </van-field>
      </div>
      <!-- submit -->
      <div>
        <van-button type="primary" class="submit" @click="submit"
          >提交</van-button
        >
        <p class="notice">注意：手机号不对请联系万店掌HR</p>
      </div>
    </main>
    <!-- fooyer -->
    <footer>
      <div class="img-box">
        <img src="../../../assets/hrm/down.png" alt="" />
      </div>
    </footer>
  </div>
</template>

<script>
import { Button, Field, CellGroup, Toast } from "vant";
import { getSubmitState, sendEntrySms, verifyEntrySms } from "../../../api/hrm";
export default {
  components: {
    [Button.name]: Button,
    [Field.name]: Field,
    [CellGroup.name]: CellGroup
  },
  data() {
    return {
      show: true,
      count: "",
      timer: null,
      phone: "", //手机号
      encryptPhone: "", //加密手机号
      verifyCode: "" //验证码
    };
  },
  mounted() {
    this.getSubmitState();
  },
  methods: {
    // 判断是否提交
    async getSubmitState() {
      try {
        const res = await getSubmitState({ id: this.$route.query.id });
        if (res.isSubmit) {
          this.$router.push({ name: "submitted" });
        } else {
          this.phone = res.phone;
          let tel = JSON.parse(JSON.stringify(res.phone));
          tel = "" + tel;
          this.encryptPhone = tel.substr(0, 3) + "****" + tel.substr(7); //用来显示的加密手机号
          console.log(this.encryptPhone);
          console.log(this.phone);
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 获取验证码
    async sendEntrySms() {
      try {
        const res = await sendEntrySms({ phone: this.phone });
        console.log(res);
      } catch (error) {
        console.log(error);
      }
    },

    // 验证入职单短信验证码
    async verifyEntrySms() {
      if (!this.phone || !this.verifyCode) {
        return Toast.fail({
          duration: 2000,
          message: "请输入验证码！"
        });
      }
      let obj = {
        phone: this.phone,
        verifyCode: this.verifyCode
      };
      try {
        const res = await verifyEntrySms(obj);
        if (res) {
          this.$router.push({
            name: "baseInfo",
            query: {
              id: this.$route.query.id
            }
          });
        } else {
          return Toast.fail({
            duration: 2000,
            message: "验证码不正确,请重新输入！"
          });
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 获取手机验证码
    send() {
      this.sendEntrySms();
      const TIME_COUNT = 60;
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.show = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.show = true;
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000);
      }
    },

    // 提交手机验证码
    submit() {
      this.verifyEntrySms();
      // this.$router.push({
      //       name: 'baseInfo',
      //       query: {
      //         id:this.$route.query.id,
      //       },
      //     });
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
