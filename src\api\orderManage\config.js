export default {
  getMasterOrderAddressList:
    "/api/crm/masterOrder/v1/getMasterOrderAddressList", //收货地址列表
  insertOrUpdateMasterOrderAddress:
    "/api/crm/masterOrder/v1/insertOrUpdateMasterOrderAddress", //新增/编辑门店地址和收货地址接口
  saveMasterOrder: "/api/crm/masterOrder/v1/saveMasterOrder", // 订单提交
  getAllAreaInfo: "/api/crm/system/v1/getAllDistrictInfo", //获取所有地区情况
  getDeptByGroupId: "/api/crm/customerDept/v1/getDeptByGroupId", //根据企业查询企业下所有门店信息
  searchUsersByRole: "/ovopark-privilege/user/searchUsersByRole", //查询门店店长列表
  detail: "/api/crm/product/v1/detail", //查询产品详情
  queryProductTypesToTree:"/api/crm/product/v1/queryProductTypesToTree", //查询小类
};
