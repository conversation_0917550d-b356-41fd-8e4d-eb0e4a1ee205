<template>
  <van-popup
    v-model="show"
    position="right"
    :style="{ height: '100%', width: '100%' }"
  >
    <div class="logistics-search-page">
      <!-- header -->
      <header>
        <van-nav-bar title="物流详情" left-arrow @click-left="backUp" />

        <div class="logist-content-title" v-if="result.length > 0">
          {{ total }}个包裹已发出
        </div>
      </header>

      <!-- main -->
      <main>
        <!-- 有数据显示 -->
        <div class="logist-content" v-if="result.length > 0">
          <div
            class="logist-content-list"
            v-for="(item, index) in result"
            :key="index"
            @click="goDetail(item)"
          >
            <div class="logist-title">
              <label>{{ item.isSign == 1 ? "已签收" : "未签收" }}</label>
              <span
                >{{ item.logisticsCompany }}：{{
                  item.logisticsNumber | numSplit
                }}</span
              >
            </div>
            <div class="logist-item">
              <label>客户名称 </label>
              <span>{{ item.customerName || "--" }}</span>
            </div>
            <div class="logist-item">
              <label>合同编号</label>
              <span>{{ item.contractNo || "--" }}</span>
            </div>
            <div class="logist-item">
              <label>收货人</label>
              <span>{{ item.linkMan || "--" }}</span>
            </div>
            <div class="logist-item">
              <label>收货地址</label>
              <span>{{ item.receiveAddress || "--" }}</span>
            </div>
          </div>
        </div>

        <van-empty description="暂无数据" v-else />
      </main>
    </div>

    <!-- 物流详情 -->
    <logisticsMessage v-model="showMessage" :messageObj="messageObj" />
  </van-popup>
</template>

<script>
// components
import { NavBar, Empty, Popup } from "vant";
import { searchLogisticsProcess } from "../../../api/delivery";
import logisticsMessage from "../logisticsMessage/index.vue";

export default {
  components: {
    [NavBar.name]: NavBar,
    [Empty.name]: Empty,
    [Popup.name]: Popup,
    logisticsMessage
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    receiverId: {
      type: [String, Number],
      default: ""
    }
  },
  data() {
    return {
      keyword: "",
      pageNumber: 1,
      result: [],
      total: 0,
      show: false,
      showMessage: false,
      messageObj: {}
    };
  },
  computed: {},

  watch: {
    value(val) {
      this.show = val;
    },
    show(val) {
      this.$emit("input", val);
    },
    receiverId(val) {
      this.search(val);
    }
  },
  methods: {
    async search(val) {
      this.result = [];
      try {
        const res = await searchLogisticsProcess({
          receiverId: val,
          pageNumber: 1,
          pageSize: 100
        });
        if (res) {
          this.result = res.list || [];
          this.total = res?.total;
        }
      } catch (error) {
        console.log(error);
      }
    },

    /**
     * 查看详情
     */
    goDetail(obj) {
      this.messageObj = {};
      const query = {
        id: obj.id,
        keyword: this.keyword
      };
      if (
        (obj.logisticsCompany && obj.logisticsCompany.includes("顺丰")) ||
        obj.logisticsCompany.includes("跨越")
      ) {
        query.customerName = obj.linkPhone.substring(obj.linkPhone.length - 4);
      }
      this.messageObj = obj;
      this.showMessage = true;
    },

    backUp() {
      this.show = false;
    }
  },
  filters: {
    numSplit(val) {
      return val.split(",")[0];
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .van-nav-bar .van-icon {
  color: #333;
  font-size: 18px;
}
.logistics-search-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  header {
    min-height: 100px;

    .logist-content-title {
      height: 30px;
      display: flex;
      align-items: center;
      background: rgba(255, 153, 0, 0.1);
      text-indent: 10px;
      color: #ff9900;
      font-size: 14px;
    }
  }

  main {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;

    .logist-content {
      .logist-content-list {
        margin: 12px;
        background-color: #fff;
        border-radius: 8px;
        font-size: 14px;
        box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.04);

        .logist-title {
          display: flex;
          justify-content: space-between;
          padding: 12px;
          background-color: #f6faff;

          label {
            color: #8acc47;
          }

          span {
            color: rgba(0, 0, 0, 0.5);
          }
        }

        .logist-item {
          display: flex;
          padding: 10px;

          label {
            width: 88px;
            color: rgba(0, 0, 0, 0.3);
          }

          span {
            display: inline-block;
            flex: 1;
            color: #000000;
          }
        }
      }

      .no-more {
        font-size: 14px;
        color: #ccc;
      }
    }
  }
}
</style>
