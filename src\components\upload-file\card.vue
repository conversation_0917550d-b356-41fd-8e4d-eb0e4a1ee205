<template>
  <div class="card">
    <!-- 选择 -->
    <div class="card-choose" v-if="!fileList.length">
      <div :class="cardFront ? 'card-bg' : 'card-bg-two'" @click="onChoose">
        <div class="add-icon">
          <div :class="['add-box', disabled ? 'mask' : '']">+</div>
          <p class="add-txt">
            {{ cardFront ? "点击上传人像面" : "点击上传国徽面" }}
          </p>
        </div>
      </div>
    </div>
    <div v-for="(item, i) in fileList" :key="i" class="card-box">
      <div class="card-img">
        <img :src="item.fileurl" alt="" />
        <div></div>
        <div class="del-box" @click="onHandleDel(item)">
          <img
            class="del-icon-box"
            src="../../assets/hrm/ico_edit.png"
            alt=""
          />
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
export default {
  components: {},
  props: {
    fileList: {
      type: Array,
      default() {
        return [];
      }
    },
    max: {
      type: Number
    },
    showUpload: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    cardFront: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      visibles: false,
      url: "",
      videoUrl: ""
    };
  },
  methods: {
    onChoose() {
      if (this.disabled) return;
      this.$emit("on-choose");
    },
    onHandleDel(item) {
      this.$emit("on-del", item);
    },
    /**查看大图 */
    showBigImg(e) {
      this.url = e.fileurl || "";
      this.visible = true;
    },
    showVideo(e) {
      this.videoUrl = e.fileurl || "";
      this.visibles = true;
    }
  }
};
</script>
  
<style scoped lang="scss">
.card {
  .card-choose {
    width: 343px;
    height: 204px;
    background: #f7f7f7;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;

    .card-bg {
      width: 289px;
      height: 173px;
      background-color: #fff;
      background-image: url("https://ovopark.oss-cn-hangzhou.aliyuncs.com/front/2023/12/19/18c8243f0932fe701a20957.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .add-icon {
        width: 84px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .add-box {
          width: 11.2vw;
          height: 11.2vw;
          background: #ff9900;
          border-radius: 50%;
          color: #fff;
          font-size: 8.53333vw;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 2.13333vw;
        }

        .add-txt {
          height: 20px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #333333;
          text-align: center;
          line-height: 20px;
        }
      }
    }

    .card-bg-two {
      width: 289px;
      height: 173px;
      background-color: #fff;
      background-image: url("https://ovopark.oss-cn-hangzhou.aliyuncs.com/front/2023/12/19/18c8243f96720506ff9eccc.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .add-icon {
        width: 84px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .add-box {
          width: 11.2vw;
          height: 11.2vw;
          background: #ff9900;
          border-radius: 50%;
          color: #fff;
          font-size: 8.53333vw;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 2.13333vw;
        }

        .add-txt {
          height: 20px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #333333;
          text-align: center;
          line-height: 20px;
        }
      }
    }
  }

  .card-box {
    width: 343px;
    height: 204px;
    background: #f7f7f7;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;

    .card-img {
      width: 289.2px;
      height: 172px;
      border-radius: 8px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        border-radius: 8px;
      }

      .del-box {
        position: absolute;
        top: -1px;
        right: -1px;
        width: 24px;
        height: 24px;
        background: #e5e5e5;
        border-radius: 0 8px 0 8px;
        display: flex;
        justify-content: center;
        align-items: center;

        .del-icon-box {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}
</style>
  