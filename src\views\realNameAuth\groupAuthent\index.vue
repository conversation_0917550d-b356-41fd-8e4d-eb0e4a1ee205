<template>
  <div class="groupAuthent">
    <van-nav-bar
      left-arrow
      @click-left="onClickLeft"
      title="实名认证"
    ></van-nav-bar>

    <tabs :isActiveA="true" :message="message"></tabs>
    <main>
      <div class="tips">
        您正在进行企业实名认证，通过后，您将成为企业主管理员
      </div>
      <!-- 企业证件 -->
      <div class="groupCertificate">
        <div class="groupCertificate-title">企业证件</div>
        <div class="group-tips">
          请上传彩色原件或加盖企业公章的复印件；非企业单位，请使用登记执照，照片仅限jpeg、jpg、png格式且大小不超过15M。企业信息将用于申请数字证书
        </div>
        <div class="businessLicense">
          <div class="businessLicense-title">
            <span>营业执照</span>
            <!-- <span class="must">*</span> -->
          </div>
          <div class="businessLicense-upload">
            <upload-file
              :isBusiness="true"
              :max="1"
              :size="15 * 1024"
              @on-upload-success="(val) => uploadSuccess(val)"
              v-model="businessUrlList"
            ></upload-file>
          </div>
        </div>
      </div>

      <div class="tips">以下信息自动识别，需仔细校对，如识别有误，请修正</div>
      <div class="input-box">
        <item-input
          v-model="cardGroupName"
          :label="'企业名称'"
          :placeholder="'请输入企业名称'"
          must
          @input="(val) => changeGroupName(true, val)"
          @focus="(val) => changeGroupName(false, val)"
          @blur="blur"
        ></item-input>

        <div class="select-input" v-if="showSelect">
          <div class="list" v-if="companyList.length > 0">
            <div
              class="defaut"
              v-for="(item, index) in companyList"
              :key="index"
              @click="changeGroup(item, index)"
            >
              {{ item.groupName }}
              <img
                class="activeIcon"
                v-if="index == activeIndex"
                src="../../../assets//ico_selected.png"
                alt=""
              />
            </div>
          </div>
          <div class="list" v-else>
            <div class="defaut-no">暂无数据</div>
          </div>
          <div class="more" @click.stop="changeMore">查询更多</div>
        </div>
      </div>

      <div class="input-box">
        <item-input
          v-model="creditCode"
          :label="'统一社会信用代码'"
          :placeholder="'请输入统一社会信用代码'"
          must
        ></item-input>
      </div>
      <div class="input-box">
        <item-input
          v-model="corp"
          :label="'法定代表人'"
          :placeholder="'请输入法定代表人'"
          must
        ></item-input>
      </div>
      <!-- 法人个人证件 -->
      <div class="legalPerson">
        <div class="legalPerson-title">法人/个人证件</div>
        <div class="tips">
          请上传清晰的身份证照片，系统将自动识别证件信息。照片仅限jpeg、jpg、png格式且大小不超过15M
        </div>
        <van-radio-group v-model="linkmanRelation">
          <van-radio name="1" checked-color="#FF9900">我是法人</van-radio>
          <van-radio name="2" checked-color="#FF9900"
            >我不是法人，我是经办人，已获得企业授权</van-radio
          >
        </van-radio-group>
      </div>

      <div class="input-box">
        <item-input
          v-model.trim="name"
          :label="'姓名'"
          :placeholder="'请输入姓名'"
          must
        ></item-input>
        <span class="autofill" @click="autofill">自动填写</span>
      </div>

      <div class="input-box">
        <item-input
          v-model.trim="idCode"
          :label="'身份证号'"
          :placeholder="'请输入身份证号'"
          must
        ></item-input>
      </div>
    </main>
    <footer>
      <div class="btn-box">
        <van-button
          :disabled="nextDisabled"
          @click="createBestSignAccount"
          class="btn"
          type="default"
          color="#FF9900"
          block
          >下一步</van-button
        >
      </div>
    </footer>

    <uploadPopup
      :showPopup="showPopup"
      @update="update"
      @personInfo="personInfo"
    ></uploadPopup>
  </div>
</template>

<script>
import itemInput from "@/components/item-input.vue";
import uploadPopup from "../components/uploadPopup.vue";
import UploadFile from "../../../components/upload-file/upload-file.vue";
import tabs from "../components/tabs.vue";
import { Button, RadioGroup, Radio, Toast, NavBar } from "vant";
// api
import {
  getOcrInfo,
  createBestSignAccount,
  subitEntCertification,
  getBusinessInfoByKeyword,
  getBusinessInfoByKeywordForYongYou
} from "@/api/realNameAuth";
export default {
  components: {
    itemInput,
    tabs,
    uploadPopup,
    UploadFile,
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Radio.name]: Radio,
    [RadioGroup.name]: RadioGroup,
    [Toast.name]: Toast
  },
  data() {
    return {
      activeIndex: -1,
      showSelect: false,
      companyList: [],
      message: {
        stepA: "企业认证",
        stepB: "实名认证"
      },
      showPopup: false,
      name: "", //身份证姓名
      idCode: "", //身份证号
      cardGroupName: "", //企业名称
      creditCode: "", //统一社会信用代码
      corp: "", //法定代表人
      linkmanRelation: "1", //1企业法人 2 企业经办人 3 个人
      attachmentBusinessUrl: "", //营业执照
      attachmentIDPositiveUrl: "", //身份证正面
      attachmentIDTheOtherSideUrl: "", //身份证反面
      businessUrlList: [] //营业执照数组
    };
  },
  computed: {
    nextDisabled() {
      return !(
        this.cardGroupName &&
        this.creditCode &&
        this.corp &&
        this.name &&
        this.idCode
      );
    }
  },
  watch: {
    businessUrlList(val) {
      if (val.length) {
        this.attachmentBusinessUrl = val[0].fileurl;
      } else {
        this.attachmentBusinessUrl = "";
        this.cardGroupName = "";
        this.creditCode = "";
        this.corp = "";
      }
    }
  },
  methods: {
    autofill() {
      this.showPopup = true;
    },

    update(val) {
      this.showPopup = val;
    },

    personInfo(val) {
      if (val) {
        this.name = val.name;
        this.idCode = val.idCode;
        this.attachmentIDPositiveUrl = val?.idCardFrontUrl;
        this.attachmentIDTheOtherSideUrl = val?.idCardBackUrl;
      }
    },

    // 获取ocr信息
    async uploadSuccess(val) {
      this.attachmentBusinessUrl = val?.fileurl;
      Toast.loading({
        duration: 0,
        message: "获取中...",
        forbidClick: true
      });
      try {
        const res = await getOcrInfo({
          type: "businessLicense",
          url: val?.fileurl
        });
        if (res) {
          let data = JSON.parse(res)?.data;
          this.creditCode = data?.creditCode; //统一社会信用代码
          this.cardGroupName = data?.companyName; //企业名称
          this.corp = data?.legalPerson; //法定代表人
          Toast.clear();
        }
      } catch (err) {
        if (err) {
          Toast.clear();
        }
      }
    },

    // 创建/编辑用户接口（新增/编辑企业认证下一步时调用）
    async createBestSignAccount() {
      try {
        const res = await createBestSignAccount({
          cardGroupName: this.cardGroupName,
          creditCode: this.creditCode,
          corp: this.corp,
          name: this.name,
          idCode: this.idCode,
          idCardFrontUrl: this.attachmentIDPositiveUrl || null,
          idCardBackUrl: this.attachmentIDTheOtherSideUrl || null,
          createType: 3,
          billingType: 0,
          linkmanRelation: this.linkmanRelation,
          mobile: sessionStorage.getItem("mobilePhoneAuth") || ""
        });
        if (res) {
          this.subitEntCertification(res);
        }
      } catch (err) {
        console.log(err);
      }
    },

    // 企业认证提交接口
    async subitEntCertification(account) {
      Toast.loading({
        duration: 0,
        message: "加载中...",
        forbidClick: true
      });
      let params = {
        contractId: this.$route.query?.contractId,
        enterpriseName: this.cardGroupName,
        creditCode: this.creditCode,
        legalPersonName: this.corp,
        attachmentBusinessUrl: this.attachmentBusinessUrl, //营业执照
        idName: this.name,
        idCode: this.idCode,
        attachmentIDPositiveUrl: this.attachmentIDPositiveUrl || null,
        attachmentIDTheOtherSideUrl: this.attachmentIDTheOtherSideUrl || null,
        isLegalPerson: this.linkmanRelation == 1 ? 1 : 0, //是否是法人 0否 1是
        cardType: this.linkmanRelation, //1企业法人 2企业经办人 3个人
        billingType: 0,
        customerBankId: this.$route.query?.customerBankId,
        signType: 2,
        mobile: sessionStorage.getItem("mobilePhoneAuth") || ""
      };
      try {
        const res = await subitEntCertification(params);
        if (res) {
          Toast.clear();
          if (this.linkmanRelation === "1") {
            // 我是法人
            this.$router.push({
              name: "legalPersonAuthent",
              query: {
                name: this.name || "",
                idCode: this.idCode || "",
                account: account,
                creditCode: this.creditCode || "",
                contractId: this.$route.query?.contractId,
                orderNo: this.$route.query?.orderNo,
                sourcePage: this.$route.query?.sourcePage
              }
            });
          } else if (this.linkmanRelation === "2") {
            //我是企业经办人
            this.$router.push({
              name: "noLegalPersonAuthent",
              query: {
                name: this.name || "",
                idCode: this.idCode || "",
                account: account,
                creditCode: this.creditCode || "",
                corp: this.corp || "",
                cardGroupName: this.cardGroupName || "",
                contractId: this.$route.query?.contractId,
                orderNo: this.$route.query?.orderNo,
                sourcePage: this.$route.query?.sourcePage
              }
            });
          }
        }
      } catch (err) {
        console.log(err);
      }
    },

    // 返回上一页
    onClickLeft() {
      this.$router.go(-1);
    },

    // 企业名称信息输入
    changeGroupName(flag, val) {
      if (!val && flag) {
        this.creditCode = "";
        this.corp = "";
      }
      val ? (this.showSelect = true) : (this.showSelect = false);
      val && this.getBusinessInfoByKeyword(val);
    },

    blur() {
      setTimeout(() => {
        this.showSelect = false;
      }, 300);
    },

    // 根据名称模糊查询企业信息
    async getBusinessInfoByKeyword(companyName) {
      try {
        const res = await getBusinessInfoByKeyword({
          companyName: companyName || "",
          no: 1,
          limit: 20
        });
        this.companyList = res?.records;
      } catch (error) {
        console.log(error);
      }
    },

    // 企业模糊查询-查询更多接口
    async changeMore() {
      try {
        const res = await getBusinessInfoByKeywordForYongYou({
          companyName: this.cardGroupName || "",
          no: 1,
          limit: 20
        });
        this.companyList = res?.records;
        this.showSelect = true;
      } catch (error) {
        console.log(error);
      }
    },

    // 选择查出的企业信息
    changeGroup(item, index) {
      this.activeIndex = index;
      this.cardGroupName = item?.groupName;
      this.creditCode = item?.creditCode;
      this.corp = item?.corp;
      this.showSelect = false;
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>