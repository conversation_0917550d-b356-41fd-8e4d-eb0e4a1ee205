export default {
  getJdSalReceiverModel: "/api/crm/jdout/v1/getJdSalReceiverModel",
  getLogistics: "/api/crm/logistics/v1/getLogistics", //物流信息
  searchLogisticsProcess: "/api/crm/jdout/v1/searchLogisticsProcess", //app查询接口
  getContractDetailForH5: "/api/crm/contract/v1/getContractDetailForH5", //合同详情
  payh5: "/api/crm/bill/v1/pay",
  contractPay: "/api/crm/cost/app/v1/contractPay", //合同付款
  h5BillDetail: "/api/crm/bill/v1/h5BillDetail", //新写账单详情页
  isPay: "/api/crm/bill/v1/isPay", //验证账单是否已经支付
  getAllDict: "/api/crm/dict/v1/getAllDict", //新的字典树
  queryProductTypesToTree: "/api/crm/product/v1/queryProductTypesToTree", //查询产品类别列表树
  confirmBill: "/api/crm/bill/v1/customerConfirmBill", //客户确认账单
  getOpenId: "/api/crm/wxPay/v3/getOpenId", //获取openId
  payJsApi: "/api/crm/wxPay/v3/payJsApi", //js预下单
  contractScanPay: "/api/crm/cost/app/v1/contractScanPay", //合同扫码支付获取预下单信息

  getUserByPhone: "/api/crm/payCollection/v1/getUserByPhone", //根据手机号查询企业门店
  getCollectionByOrderNo: "/api/crm/payCollection/v1/getCollectionByOrderNo", //根据id查询收款码
  qrPay: "/api/crm/ccbPay/v1/qrPay", //动态二维码（发起支付）
  getOpenId2: "/api/crm/wxPay/v3/getOpenId2", //获取openid 不需要token
  getWeChatInfo: "/api/crm/wxPay/v3/getWeChatInfo", //获取微信基本信息
  saveBillVoucher: "/api/crm/bill/v1/saveBillVoucher", // 确认账单付款凭证
  getCountOverdueBillByUser:
    "/api/crm/repaymentManage/v1/getCountOverdueBillByUser", //应收账单详情列表接口
  getNextCountOverdueBillByUser:
    "/api/crm/repaymentManage/v1/getNextCountOverdueBillByUser", //应收账单下层级列表接口
  cancelOrder: "/api/crm/bill/v1/cancelOrder", //取消订单
  getCustomerAccountList: "/api/crm/repaymentManage/v1/getCustomerAccountList", //获取客户账户列表
  getCurrentCustomer: "/api/crm/stbCustomer/v1/getCurrentCustomer", //根据authenticator获取用户信息
  getVerifyCode: "/api/crm/cmAccount/v1/getVerifyCode", //获取验证码
  payCustomerAccount: "/api/crm/cmAccount/v1/payCustomerAccount", //客户账号余额扣减接口
  getTicketOCR: "/api/common/tool/v1/getTicketOCR", //对公转账凭证ocr
  getPaySerialNewRecord: "/api/crm/repaymentManage/v1/getPaySerialNewRecord", //获取未关联流水列表(对公转账)
  relatedContract: "/api/crm/repaymentManage/v1/relatedContract", //回款关联合同
};
