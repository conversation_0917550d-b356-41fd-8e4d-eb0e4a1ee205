<template>
  <div class="shipMessage">
    <div v-if="loanAddress.length > 0">
      <div class="list" v-for="(item, index) in loanAddress" :key="index">
        <div class="item">
          <label class="name" for=""
            >{{ item.consignee }}-{{ item.cellphone }}</label
          >
          <div class="content-color">
            <span
              :style="{ color: cluingStateColor[item.deliverStatus - 1] }"
              >{{
                deliverStatusList.find((e) => e.value == item.deliverStatus)
                  ? deliverStatusList.find((e) => e.value == item.deliverStatus)
                      .dname
                  : ""
              }}</span
            >
          </div>
        </div>
        <div class="item">
          <label for="">期望发货日期</label>
          <div class="content"></div>
        </div>
        <div class="item">
          <label for="">详细地址</label>
          <div class="content">{{ item.address }}</div>
        </div>
        <div class="item">
          <label for="">合计金额</label>
          <div class="content">¥{{ item.totalMoney }}</div>
        </div>
      </div>
    </div>
    <van-empty v-else description="暂无数据" />
  </div>
</template>

<script>
import { Empty } from "vant";
export default {
  components: {
    [Empty.name]: Empty
  },
  props: {
    loanAddress: {
      type: Array,
      default: () => []
    },
    deliverStatusList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      cluingStateColor: ["purple", "blue", "green", "red", "pink", "green"]
    };
  }
};
</script>

<style lang="scss" scoped>
.shipMessage {
  padding: 8px 13px;
  background: #fff;

  .list {
    border: 0.5px solid #e5e5e5;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .name {
        height: 22px;
        font-family: PingFangSC-Medium;
        font-weight: 550;
        font-size: 14px;
        color: #000000;
        line-height: 22px;
      }

      label {
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 13px;
        color: #7f7f7f;
        line-height: 20px;
      }

      .content-color {
        width: 42px;
        height: 18px;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;

        span {
          height: 12px;
          font-family: PingFangSC-SNaNpxibold;
          font-weight: 600;
          font-size: 11px;
          letter-spacing: 0;
          text-align: center;
          line-height: 12px;
        }
      }

      .content {
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 13px;
        color: #333333;
        text-align: right;
        line-height: 20px;
      }
    }
  }
}
</style>