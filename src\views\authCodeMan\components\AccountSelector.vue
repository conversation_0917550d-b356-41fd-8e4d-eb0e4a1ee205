<template>
  <van-popup
    v-model="visible"
    position="right"
    :style="{ width: '100%', height: '100%' }"
  >
    <div class="account-selector-container">
      <div class="selector-header">
        <van-icon name="arrow-left" @click="onClose" />
        <span class="title">选择账号</span>
        <span></span>
      </div>

      <div class="search-box">
        <van-search
          v-model="searchKeyword"
          placeholder="搜索"
          shape="round"
          background="#fff"
          @search="handleSearch"
          @clear="handleClear"
          clearable
        />
      </div>

      <div 
        class="account-list" 
        ref="accountListRef" 
        @scroll="handleScroll"
      >
        <div
          v-for="account in accountList"
          :key="account.usercode"
          class="account-item"
          @click="selectAccount(account)"
        >
          <div class="account-checkbox">
            <van-checkbox
              :value="selectedId === account.usercode"
              icon-size="16px"
              checked-color="#ff9900"
            />
          </div>
          <div class="account-name">{{ account.username }}</div>
        </div>
        
        <div v-if="accountList.length === 0 && !loading" class="empty-data">
          暂无数据
        </div>
        
        <div v-if="loading" class="loading-more">
          <van-loading type="spinner" size="20px">加载中...</van-loading>
        </div>
        
        <div v-if="finished && !loading && accountList.length > 0" class="loading-finished">
          没有更多数据了
        </div>
      </div>

      <div class="selector-footer">
        <van-button type="primary" block @click="onConfirm" :disabled="!selectedId">确定</van-button>
      </div>
    </div>
  </van-popup>
</template>

<script>
import { Popup, Icon, Search, Checkbox, Button, Toast, Loading } from "vant";
import { getAccountOrDeptInfo } from "@/api/authCode";

export default {
  name: "AccountSelector",
  components: {
    VanPopup: Popup,
    VanIcon: Icon,
    VanSearch: Search,
    VanCheckbox: Checkbox,
    VanButton: Button,
    VanLoading: Loading
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentSoftwareId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      searchKeyword: "",
      selectedId: null,
      accountList: [],
      loading: false,
      finished: false,
      currentPage: 1,
      pageSize: 10
    };
  },
  watch: {
    visible(val) {
      if (val) {
        // 打开弹窗时重置所有状态，包括搜索关键词
        this.searchKeyword = ""; // 清空搜索关键词
        this.resetData(); // 重置其他数据
        this.fetchAccounts();
      }
    },
    // 监听搜索关键词变化
    searchKeyword(val, oldVal) {
      // 当用户手动清空搜索关键词时（不是初始化时）
      if (val === "" && oldVal && oldVal !== "") {
        // 自动触发重置搜索
        this.accountList = [];
        this.currentPage = 1;
        this.finished = false;
        this.fetchAccounts();
      }
    }
  },
  mounted() {
    // 监听容器大小变化，可能需要加载更多
    this.$nextTick(() => {
      if (this.visible) {
        this.checkNeedMoreData();
      }
    });
  },
  methods: {
    resetData() {
      // 只重置其他数据，不重置搜索关键词
      this.selectedId = null;
      this.accountList = [];
      this.currentPage = 1;
      this.finished = false;
    },
    
    // 处理搜索事件
    handleSearch() {
      // 确保使用当前输入框中的关键词进行查询
      if (this.searchKeyword !== undefined && this.searchKeyword !== null) {
        // 重置页码和数据，但保留关键词
        this.accountList = [];
        this.currentPage = 1;
        this.finished = false;
        
        // 触发查询
        this.fetchAccounts();
      }
    },
    
    // 处理滚动加载更多
    handleScroll() {
      if (this.loading || this.finished) return;
      
      const scrollEl = this.$refs.accountListRef;
      if (!scrollEl) return;
      
      const { scrollTop, scrollHeight, clientHeight } = scrollEl;
      if (scrollHeight - scrollTop - clientHeight < 50) {
        this.loadMore();
      }
    },
    
    // 检查是否需要加载更多数据
    checkNeedMoreData() {
      setTimeout(() => {
        const scrollEl = this.$refs.accountListRef;
        if (!scrollEl) return;
        
        const { scrollHeight, clientHeight } = scrollEl;
        
        // 如果内容高度小于或接近容器高度，且没有加载完，则继续加载更多
        if (scrollHeight <= clientHeight + 20 && !this.finished && !this.loading) {
          this.loadMore();
        }
      }, 100); // 稍微延迟一下，确保DOM已更新
    },
    
    loadMore() {
      this.currentPage++;
      this.fetchAccounts(false);
    },
    
    async fetchAccounts(isReset = true) {
      if (this.loading) return;
      
      if (isReset) {
        this.currentPage = 1;
        this.accountList = [];
        this.finished = false;
      }
      
      this.loading = true;
      
      try {
        // 设置请求超时
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 10000);
        });
        
        // 调用API获取账号数据
        const fetchPromise = getAccountOrDeptInfo({
          no: this.currentPage,
          limit: this.pageSize,
          softwareId: this.currentSoftwareId,
          type: 1, // 1表示账号
          activeState: 0,
          keyword: this.searchKeyword || undefined
        });
        
        // 竞争Promise，谁先完成就用谁的结果
        const response = await Promise.race([fetchPromise, timeoutPromise]);
        
        if (response.userList && Array.isArray(response.userList.records)) {
          const newData = response.userList.records;
          
          // 追加新数据到列表
          this.accountList = [...this.accountList, ...newData];
          
          // 如果返回的数据少于请求的数量，说明没有更多数据了
          if (newData.length < this.pageSize) {
            this.finished = true;
          }
        } else {
          // 如果返回数据异常，标记为已完成
          this.finished = true;
          if (this.currentPage === 1) {
            Toast("未查询到账号数据");
          }
        }
      } catch (error) {
        this.finished = true;
        Toast(error.message || "获取账号列表失败");
      } finally {
        this.loading = false;
        // 检查是否需要继续加载更多数据
        this.checkNeedMoreData();
      }
    },
    
    selectAccount(account) {      
      this.selectedId = account.usercode;
    },
    
    onClose() {
      this.$emit('update:visible', false);
    },
    
    onConfirm() {
      if (!this.selectedId) {
        Toast("请选择账号");
        return;
      }
      const selectedAccount = this.accountList.find(a => a.usercode === this.selectedId);
      if (selectedAccount) {
        this.$emit('confirm', {
          name: selectedAccount.username,
          usercode: selectedAccount.usercode,
          oldUserid: selectedAccount.oldUserid
        });
      }
      this.$emit('update:visible', false);
    },
    
    handleClear() {
      // 处理搜索框清除按钮的点击事件
      this.searchKeyword = "";
      this.accountList = [];
      this.currentPage = 1;
      this.finished = false;
      this.fetchAccounts();
    }
  }
};
</script>

<style scoped lang="scss">
.account-selector-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;

  .selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #ebedf0;

    .van-icon {
      font-size: 20px;
      color: #333;
    }

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      text-align: center;
    }
  }

  .search-box {
    padding: 8px 16px;
  }

  .account-list {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 12px;
    -webkit-overflow-scrolling: touch;

    .account-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #ebedf0;
      cursor: pointer;
      width: 100%;
      transition: background-color 0.2s;

      &:active {
        background-color: #f7f8fa;
      }

      .account-checkbox {
        margin-right: 12px;
        flex-shrink: 0;
      }

      .account-name {
        font-size: 14px;
        color: #333;
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding-right: 8px;
      }
    }
    
    .empty-data {
      margin-top: 40px;
      color: #969799;
      font-size: 14px;
      text-align: center;
    }
    
    .loading-more,
    .loading-finished {
      width: 100%;
      padding: 15px 0;
      text-align: center;
      color: #969799;
      font-size: 14px;
    }
  }

  .selector-footer {
    padding: 16px;

    .van-button {
      height: 40px;
      border-radius: 8px;
      width: 100%;
      background: #ff9900;
      border-color: #ff9900;
      
      &--disabled {
        background: #f5f5f5;
        border-color: #e8e8e8;
        color: #c8c9cc;
      }
    }
  }
}

::v-deep .van-search {
  padding: 0;
  
  &__content {
    background-color: #f7f8fa;
    border-radius: 4px;
  }

  &__field {
    padding: 0 12px;
  }
}
</style> 