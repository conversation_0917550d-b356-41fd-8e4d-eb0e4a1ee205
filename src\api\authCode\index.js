import urls from "./config";
import { get, post } from "../request";

export function getAuthCodeSoftwareTypeByH5(params = {}) {
  return get(urls.getAuthCodeSoftwareTypeByH5, params);
}

export function getAuthCodePageByTypeForH5(params = {}) {
  return get(urls.getAuthCodePageByTypeForH5, params);
}

export function activateAuthCode(data = {}) {
  return post(urls.activateAuthCode, data);
}

export function changeAuthCode(data = {}) {
  return post(urls.changeAuthCode, data);
}

export function getAccountOrDeptInfo(data = {}) {
  return get(urls.getAccountOrDeptInfo, data);
}

export function activeAuthorizationCodeByList(data = {}) {
  return get(urls.activeAuthorizationCodeByList, data);
}

export function changeDeptAuthorizationCodeByList(data = {}) {
  return get(urls.changeDeptAuthorizationCodeByList, data);
}

export function activeAccountAuthorizationCodeByList(data = {}) {
  return get(urls.activeAccountAuthorizationCodeByList, data);
}

export function changeAccountAuthorizationCodeByList(data = {}) {
  return get(urls.changeAccountAuthorizationCodeByList, data);
}
