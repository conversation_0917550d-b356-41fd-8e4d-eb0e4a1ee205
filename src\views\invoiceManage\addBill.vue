<template>
  <div class="add-bill">
    <van-nav-bar title="添加抬头" left-arrow @click-left="onClickLeft" />
    <div class="add-bill-box">
      <div class="bill-wrap">
        <van-form @submit="onSubmit" ref="billForm">
          <van-field
            v-model="billItem.billingName"
            name="billingName"
            label="发票抬头"
            placeholder="请填写"
          />
          <van-field name="radio" label="抬头类型">
            <template #input>
              <van-radio-group v-model="billingTypeName" direction="horizontal">
                <van-radio name="加盟企业">企业单位</van-radio>
                <van-radio name="加盟个人">个人</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field
            v-model="billItem.customerTaxpayerAccount"
            name="customerTaxpayerAccount"
            label="税号"
            placeholder="请填写"
          />
          <van-field
            v-model="billItem.customerOpenBank"
            name="customerOpenBank"
            label="开户银行"
            placeholder="请填写(选填)"
          />
          <van-field
            v-model="billItem.customerOpenAccount"
            name="customerOpenAccount"
            label="银行账号"
            placeholder="请填写(选填)"
          />
          <van-field
            v-model="billItem.billingAddress"
            name="billingAddress"
            label="企业地址"
            placeholder="请填写(选填)"
          />
        </van-form>
      </div>
      <div class="default-bill">
        <span class="default">设置为默认抬头</span>
        <van-switch v-model="checked" size="24px" />
      </div>
    </div>
    <div class="submit">
      <van-button
        round
        block
        color="#FF9900"
        native-type="button"
        @click="send"
        :disabled="disabledFlag"
        :loading="btnLoading"
        >保存</van-button
      >
    </div>
  </div>
</template>

<script>
import {
  NavBar,
  Field,
  Icon,
  Popup,
  RadioGroup,
  Radio,
  Button,
  Form,
  Switch,
  Toast
} from "vant";
import { saveBank, updateBank } from "../../api/bill";

export default {
  name: "addBill",
  components: {
    [NavBar.name]: NavBar,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [Popup.name]: Popup,
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio,
    [Button.name]: Button,
    [Form.name]: Form,
    [Switch.name]: Switch,
    [Toast.name]: Toast
  },
  data() {
    return {
      checked: false,
      btnLoading: false,
      billingTypeName: "加盟个人",
      billItem: {},
      basicId: "",
      contractNos: "",
      type: "create"
    };
  },
  computed: {
    disabledFlag() {
      return !this.billItem.billingName;
    }
  },
  created() {
    this.basicId = this.$route.query.basicId;
    this.contractNos = this.$route.query.contractNos;
    this.billItem = this.$route.query.billInfo
      ? JSON.parse(this.$route.query.billInfo)
      : {};
    this.billingTypeName =
      this.$route.query.billInfo?.billingType == 3 ? "加盟个人" : "加盟企业";
    this.type = this.$route.query.type;
  },
  methods: {
    onClickLeft() {
      this.$router.push({
        name: "applyBill",
        query: {
          contactNos: this.contractNos
        }
      });
    },
    async onSubmit() {
      const params = {
        ...this.billItem,
        billingTypeName: this.billingTypeName
      };

      if (this.type === "create") {
        params.basicId = this.basicId;
      } else {
        params.basicId = this.billItem.basicInfoId;
      }

      delete params.flag;
      delete params.createBy;
      delete params.createTime;
      delete params.basicInfoId;
      delete params.billingType;

      // 默认抬头存储在localstorage中
      if (this.checked) {
        localStorage.setItem("defaultBillName", this.billItem.billingName);
      }

      this.btnLoading = true;
      try {
        this.type === "create"
          ? await saveBank(params)
          : await updateBank(params);
        Toast.success("保存成功");
        this.$router.push({
          name: "applyBill",
          query: {
            contactNos: this.contractNos
          }
        });
      } catch (err) {
        Toast.fail({
          duration: 2000,
          message: err.result
        });
      } finally {
        this.btnLoading = false;
      }
    },
    send() {
      this.$refs.billForm.submit();
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .van-field__label {
  color: #333333;
}

.add-bill-box {
  padding: 12px 16px;
  .bill-wrap {
    border-radius: 6px;
    /deep/ .van-form {
      .van-field {
        &:first-child {
          border-radius: 6px 6px 0 0;
        }
        &:last-child {
          border-radius: 0 0 6px 6px;
        }
      }
    }
  }
  .default-bill {
    background: #ffffff;
    border-radius: 6px;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
    box-sizing: border-box;
    .default {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
    }
  }
}
.submit {
  background: #fff;
  position: fixed;
  bottom: 0;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 16px;
  box-sizing: border-box;
}
</style>
