<template>
  <div class="detail" :style="{ height: screenHeight }">
    <van-nav-bar left-text="" left-arrow fixed @click-left="back" />

    <div class="container">
      <main>
        <div class="info">
          <h3>基础信息</h3>
          <div class="receiver-info">
            <label>合同编号</label><span>{{ info.contractNo || "--" }}</span>
          </div>
          <div class="receiver-info">
            <label>客户名称</label><span>{{ info.customerName || "--" }}</span>
          </div>
          <div class="receiver-info">
            <label>销售负责人</label><span>{{ info.saleName || "--" }}</span>
          </div>
          <div class="receiver-info">
            <label>联系方式</label><span>{{ info.saleLinkMan || "--" }}</span>
            <a class="phone" :href="`tel:${info.saleLinkMan}`">
              <van-icon name="phone-o" size="18"
            /></a>
          </div>
          <div class="receiver-info">
            <label>收货人</label><span>{{ info.linkMan || "--" }}</span>
          </div>
          <div class="receiver-info">
            <label>联系方式</label><span>{{ info.linkPhone || "--" }}</span>
            <a class="phone" :href="`tel:${info.linkPhone}`">
              <van-icon name="phone-o" size="18"
            /></a>
          </div>
          <div class="receiver-info">
            <label>收货地址</label
            ><span>{{ info.receiveAddress || "--" }}</span>
          </div>
        </div>

        <div class="info no-border">
          <h3>产品信息</h3>
          <delivery-table :tableData="tableData"></delivery-table>
        </div>
        <div class="info">
          <van-swipe @change="changeBanner" class="banner-step">
            <van-swipe-item v-for="(ele, idx) in tracnNumberArr" :key="idx">
              <p class="delivery-num">{{ info.logistics + ":" + ele }}</p>

              <van-steps
                v-if="logisticsInfo.length"
                direction="vertical"
                :active="active"
              >
                <van-step v-for="(item, index) in logisticsInfo" :key="index">
                  <p>{{ item.AcceptStation }}</p>
                  <p>{{ item.AcceptTime }}</p>
                </van-step>
              </van-steps>
              <p class="errInfo" v-if="!logisticsInfo.length">暂无物流信息~</p>
            </van-swipe-item>
          </van-swipe>
        </div>
      </main>
    </div>
  </div>
</template>
  
  <script>
import { NavBar, Step, Steps, Icon, Toast, Swipe, SwipeItem } from "vant";
import deliveryTable from "../../../components/deliveryTable.vue";
import { getLogistics } from "../../../api/delivery";
import { getJdOutReceiverModel } from "../../../api/logistics";

export default {
  components: {
    [NavBar.name]: NavBar,
    [Step.name]: Step,
    [Steps.name]: Steps,
    [Icon.name]: Icon,
    [Toast.name]: Toast,
    [Swipe.name]: Swipe,
    [SwipeItem.name]: SwipeItem,
    deliveryTable
  },
  mounted() {
    const { id, customerName } = this.$route.query;
    this.getinfo(id, customerName);

    this.screenHeight = window.screen.availHeight - 46 + "px";
  },
  data() {
    return {
      tableData: [],
      active: 0, //步骤条
      info: {}, //详细信息
      logisticsInfo: [], //物流信息
      screenHeight: "",
      tracnNumberArr: [] //物流号arr
    };
  },
  methods: {
    /**
     * huoqu列表信息
     */
    async getinfo(id, customerName) {
      const params = {
        outId: id
      };
      const res = await getJdOutReceiverModel(params);
      this.info = res || {};
      this.tableData = res.children;

      const param = {
        logisticsCode: res.logistics,
        trackingNumber: res.logisticsNumber.split(",")[0] || "",
        customerName
      };
      if (res.logistics && res.logistics.includes("顺丰") || res.logistics.includes("跨越")) {
        params.customerName = res.linkPhone.substring(res.linkPhone.length - 4);
      }

      this.getWuliu(param);
      this.tracnNumberArr = res.logisticsNumber.split(",") || "--";
    },

    /**
     * 获取物流信息
     */
    getWuliu(params) {
      getLogistics(params)
        .then((val) => {
          this.logisticsInfo = JSON.parse(val).reverse();
        })
        .catch((err) => {
          Toast.fail({
            duration: 4000,
            message: err.data
          });
        });
    },
    /**
     * 退回
     */
    back() {
      this.$router.go(-1);
    },

    changeBanner(index) {
      const params = {
        logisticsCode: this.info.logistics,
        trackingNumber: this.tracnNumberArr[index]
      };
      this.getWuliu(params);
    }
  }
};
</script>
  
  <style lang="scss" scoped>
.detail {
  padding-top: 46px;
  width: 100%;
}
main {
  padding: 10px;
}
.info {
  margin-bottom: 16px;
  padding-bottom: 10px;
  padding-right: 2px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.04);
  font-size: 15px;
  h3 {
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 16px;
    text-indent: 20px;
    position: relative;
  }
  h3::before {
    content: "";
    width: 4px;
    height: 18px;
    background: #ff9900;
    border-radius: 1px;
    position: absolute;
    left: 12px;
  }
  .receiver-info {
    margin: 14px 0 12px 0;
    padding-left: 100px;
    position: relative;
    label {
      color: rgba(0, 0, 0, 0.3);
      position: absolute;
      left: 16px;
    }
  }
  .delivery-num {
    background: #f6faff;
    line-height: 44px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    text-indent: 20px;
  }
}
.no-border {
  border: none;
}
.phone {
  position: relative;
  bottom: -2px;
  left: 14px;
}
.errInfo {
  text-align: center;
  line-height: 30px;
  color: #ccc;
}

/deep/ .van-nav-bar .van-icon {
  color: #333;
  font-size: 18px;
}
.container {
  overflow: auto;
  height: 100%;
}

//轮播下面的点
/deep/ .van-swipe__indicators {
  bottom: 2px;
}
/deep/ .van-swipe__indicator--active {
  background-color: #f90;
}
</style>
  