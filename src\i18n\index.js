import Vue from "vue";
import VueI18n from "vue-i18n";

Vue.use(VueI18n);

// 各个国家的key
export const localeKeys = [
  { key: "en-US", name: "English" }, // 英语
  { key: "zh-C<PERSON>", name: "中文" }, // 中文
  { key: "zh-TW", name: "繁體中文" }, // 繁体中文
  { key: "id-ID", name: "Bahasa Indonesia" } // 印尼语
];

// 各个国家语言包
const messages = {};
for (const item of localeKeys) {
  const key = item.key;
  const langObj = require(`./locales/${key}/index.js`).default;
  messages[key] = {
    ...langObj
  };
}
// 创建i18n实例
export default new VueI18n({
  locale: navigator.userAgent.split("/").pop() || "zh-CN", //默认中文，或切换的语言
  messages,
  silentTranslationWarn: true // 忽略翻译警告
});
