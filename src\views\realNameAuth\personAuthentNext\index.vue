<template>
  <div class="personAuthentStep">
    <tabs :isActiveB="true" :message="message"></tabs>
    <main>
      <div class="input-box">
        <item-input
          :label="'姓名'"
          :placeholder="'请输入姓名'"
          v-model.trim="name"
          must
        ></item-input>
      </div>

      <div class="input-box">
        <item-input
          :label="'身份证号'"
          :placeholder="'请输入身份证号'"
          v-model.trim="idCode"
          must
        ></item-input>
      </div>

      <div class="input-box">
        <item-input
          :label="'手机号'"
          :placeholder="'请输入手机号'"
          v-model.trim="phone"
          must
        ></item-input>
      </div>

      <div class="input-box">
        <item-input
          ref="inputVerify"
          :focusInput="'inputVerify'"
          :label="'验证码'"
          :placeholder="'请输入验证码'"
          v-model="verifyCode"
          must
        ></item-input>
        <span class="verifyCode" v-if="showVerify" @click="sendVcode"
          >获取验证码</span
        >
        <span class="verifyCode-add" v-else>重新获取({{ count }}s)</span>
      </div>
    </main>
    <footer>
      <div class="btn-box">
        <van-button
          :disabled="!name || !idCode || !phone || !verifyCode"
          @click="verifyVcode"
          class="btn"
          type="default"
          color="#FF9900"
          block
          >确认提交</van-button
        >
      </div>
    </footer>
  </div>
</template>

<script>
import itemInput from "@/components/item-input.vue";
import tabs from "../components/tabs.vue";
import { Button, Toast } from "vant";
// api
import { sendVcode, verifyVcode } from "@/api/realNameAuth";
export default {
  components: {
    itemInput,
    tabs,
    [Button.name]: Button,
    [Toast.name]: Toast
  },
  data() {
    return {
      count: "",
      showVerify: true,
      timer: null,
      name: "",
      idCode: "",
      phone: "",
      account: "",
      verifyCode: "",
      vCodeKey: "",
      message: {
        stepA: "信息填写",
        stepB: "实名认证"
      }
    };
  },
  created() {
    let { name, idCode, account } = this.$route?.query;
    if (name && idCode && account) {
      this.name = name;
      this.idCode = idCode;
      this.account = account;
    }
    this.phone = sessionStorage.getItem("mobilePhoneAuth") || "";
  },
  methods: {
    // 实名认证获取验证码接口
    async sendVcode() {
      if (!this.phone) {
        return Toast.fail({
          duration: 2000,
          message: "请填写手机号",
          forbidClick: true
        });
      }
      Toast.loading({
        duration: 0,
        message: "获取验证码...",
        forbidClick: true
      });
      const TIME_COUNT = 60;
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.showVerify = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.showVerify = true;
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000);
      }
      try {
        const res = await sendVcode({
          mobile: this.phone,
          idName: this.name,
          idCode: this.idCode,
          account: this.account
        });
        console.log(res);
        if (res) {
          Toast.success({
            message: "获取验证码成功",
            forbidClick: true
          });
          this.$refs.inputVerify.focus();
          Toast.clear();
          this.vCodeKey = res.vcodeKey;
        }
      } catch (error) {
        if (error) {
          Toast.clear();
        }
      }
    },

    // 实名认证验证验证码接口
    async verifyVcode() {
      Toast.loading({
        duration: 0,
        message: "提交中...",
        forbidClick: true
      });
      try {
        await verifyVcode({
          vCode: this.verifyCode,
          idName: this.name,
          idCode: this.idCode,
          mobile: this.phone,
          account: this.account,
          signType: 2, //0:crm合同  1:电子合同 2 实名认证
          billingType: 1,
          vCodeKey: this.vCodeKey
        });
        Toast.clear();
        // 成功后回到合同 / 付款页
        let sourcePage = this.$route.query?.sourcePage;
        let tokenAuth = sessionStorage.getItem("tokenAuth");
        if (sourcePage == "contractPage") {
          this.$router.push({
            name: "contractDetail",
            query: {
              contractId: this.$route.query?.contractId,
              token: tokenAuth,
              isLogin: "1"
            }
          });
        } else if (sourcePage == "paymentLandedPage") {
          // 跳转付款页面
          this.$router.push({
            name: "paymentLanded",
            query: {
              orderNo: this.$route.query?.orderNo,
              sourcePage: "paymentLandedPage",
              token: tokenAuth,
              isLogin: "1"
            }
          });
        }
      } catch (error) {
        if (error) {
          Toast.clear();
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.personAuthentStep {
  background: #f7f6f8;
  display: flex;
  flex-direction: column;
  height: 100%;

  main {
    flex: 1;
    .input-box {
      background: #fff;
      border-radius: 5px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      position: relative;

      .verifyCode {
        position: absolute;
        width: 90px;
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #ff9900;
        line-height: 20px;
        text-align: right;
        right: 16px;
      }

      .verifyCode-add {
        position: absolute;
        width: 90px;
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #b2b2b2;
        line-height: 20px;
        text-align: right;
        right: 16px;
      }
    }
  }
  footer {
    height: 56px;
    background: #ffffff;
    box-shadow: 0 0 0 0 #f0f3fa;

    .btn-box {
      height: 100%;
      padding: 0 16px;
      display: flex;
      justify-content: center;
      align-items: center;

      .btn {
        border-radius: 21px;
      }
    }
  }
}
</style>