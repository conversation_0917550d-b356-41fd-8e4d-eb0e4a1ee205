<template>
  <div class="business">
    <!-- 选择 -->
    <div class="business-bg" v-if="!fileList.length" @click="onChoose">
      <div class="add-icon">
        <div class="add-box">+</div>
        <p class="add-txt">点击上传</p>
      </div>
    </div>

    <div v-for="(item, i) in fileList" :key="i" class="business-box">
      <div class="business-img">
        <img :src="item.fileurl" alt="" />
        <div></div>
        <div class="del-box" @click="onHandleDel(item)">
          <img
            class="del-icon-box"
            src="../../assets/hrm/ico_edit.png"
            alt=""
          />
        </div>
      </div>
    </div>
  </div>
</template>
    
    <script>
export default {
  components: {},
  props: {
    fileList: {
      type: Array,
      default() {
        return [];
      }
    },
    max: {
      type: Number
    },
    showUpload: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      visibles: false,
      url: "",
      videoUrl: ""
    };
  },
  methods: {
    onChoose() {
      if (this.disabled) return;
      this.$emit("on-choose");
    },
    onHandleDel(item) {
      this.$emit("on-del", item);
    },
    /**查看大图 */
    showBigImg(e) {
      this.url = e.fileurl || "";
      this.visible = true;
    },
    showVideo(e) {
      this.videoUrl = e.fileurl || "";
      this.visibles = true;
    }
  }
};
</script>
    
  <style scoped lang="scss">
.business {
  // height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 343px;
  height: 204px;
  background: #f7f7f7;
  border-radius: 8px;

  .business-bg {
    width: 289.2px;
    height: 172px;
    background-color: #fff;
    background-image: url("https://ovopark.oss-cn-hangzhou.aliyuncs.com/2024/01/09/yyzz.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;

    .add-icon {
      width: 84px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .add-box {
        width: 11.2vw;
        height: 11.2vw;
        background: #ff9900;
        border-radius: 50%;
        color: #fff;
        font-size: 8.53333vw;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 2.13333vw;
      }

      .add-txt {
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        text-align: center;
        line-height: 20px;
      }
    }
  }

  .business-box {
    width: 289.2px;
    height: 172px;
    border-radius: 8px;
    background: #f7f7f7;

    .business-img {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        border-radius: 8px;
      }

      .del-box {
        position: absolute;
        top: -1px;
        right: -1px;
        width: 24px;
        height: 24px;
        background: #e5e5e5;
        border-radius: 0 8px 0 8px;
        display: flex;
        justify-content: center;
        align-items: center;

        .del-icon-box {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}
</style>
    