<template>
  <div class="buildCost">
    <div class="info" v-if="buildList.length > 0">
      <div class="list" v-for="(item, index) in buildList" :key="index">
        <img src="../../../../assets/sb.png" alt="" />
        <div class="content">
          <div class="top">
            <p>{{ item.productName }}</p>
            <p>
              <span>总价</span> <span>￥{{ item.discountPrice }}</span>
            </p>
          </div>
          <div class="center">
            <span>单价 ¥{{ item.unitPrice }}</span>
            <span>折后单价 ¥{{ item.salePrice }}</span>
          </div>
          <div class="bottom">
            <span>折扣率 {{ item.discount * 100 }}%</span>
            <span>x{{ item.nums }}</span>
          </div>
        </div>
      </div>
    </div>
    <van-empty v-else description="暂无数据" />
  </div>
</template>

<script>
import { Empty } from "vant";
export default {
  components: {
    [Empty.name]: Empty
  },
  props: {
    buildList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  }
};
</script>

<style lang="scss" scoped>
.buildCost {
  padding: 0 13px;
  padding-top: 8px;
  background: #fff;

  .info {
    .list {
      margin-top: 15px;
      display: flex;
      padding-bottom: 15.5px;
      align-items: center;
      border-bottom: 0.5px solid #e5e5e5;

      &.list:last-child {
        border-bottom: none;
      }

      img {
        width: 80px;
        height: 80px;
        margin-right: 12px;
      }

      .content {
        flex: 1;
        display: flex;
        flex-direction: column;

        .top {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;
          p:nth-child(1) {
            height: 22px;
            font-family: PingFangSC-Regular;
            font-weight: 550;
            font-size: 14px;
            color: #000000;
            line-height: 22px;
          }

          p:nth-child(2) {
            span:nth-child(1) {
              height: 20px;
              font-family: PingFangSC-Regular;
              font-weight: 400;
              font-size: 12px;
              color: #b2b2b2;
              line-height: 20px;
            }

            span:nth-child(2) {
              height: 24px;
              font-family: PingFangSC-SNaNpxibold;
              font-weight: 600;
              font-size: 16px;
              color: #000000;
              line-height: 24px;
            }
          }
        }

        .center {
          margin-bottom: 14px;

          span {
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #7f7f7f;
            line-height: 20px;
          }

          span:nth-child(2) {
            margin-left: 16px;
          }
        }

        .bottom {
          display: flex;
          justify-content: space-between;

          span {
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #7f7f7f;
            line-height: 20px;
          }
        }
      }
    }
  }
}
</style>