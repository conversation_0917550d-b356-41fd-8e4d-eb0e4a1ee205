<template>
  <div class="refund-container">
    <van-nav-bar title="收款记录" left-arrow @click-left="onClickLeft" />
    <div class="refund-list">
      <div class="empty-date-box" @click="open" v-if="refundList.length == 0">
        <span class="date">{{ $moment(currentMonth).format("YYYY/MM") }}</span>
        <img src="../../assets/<EMAIL>" alt="" class="arrow" />
      </div>
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="getRefundList"
        >
          <div class="refund" v-for="(refunds, name) in group" :key="name">
            <van-sticky>
              <div class="sticky">
                <div class="date-box" @click="openTimePopup(name)">
                  <span class="date">{{ name.split("-")[0] }}</span>
                  <img
                    src="../../assets/<EMAIL>"
                    alt=""
                    class="arrow"
                  />
                </div>
                <div class="money">
                  <div class="text">共收款{{ name.split("-")[1] || 0 }}笔</div>
                  <!-- <p class="total">￥{{ name.split("-")[2] || 0 }}</p> -->
                </div>
              </div>
            </van-sticky>
            <div class="refund-box">
              <div class="record" v-for="item in refunds" :key="item.id">
                <div class="top">
                  <div>
                    <span class="time-wrap"
                      >{{ $moment(item.payTime2).format("MM月DD日") }}
                      <span class="time">{{
                        $moment(item.payTime2).format("HH:mm")
                      }}</span></span
                    >
                  </div>
                  <van-button class="relate" @click="goRelated(item)"
                    >关联</van-button
                  >
                </div>
                <div class="content">
                  <div class="left">
                    <img
                      src="../../assets/<EMAIL>"
                      alt=""
                      class="wechat"
                    />
                    <div class="remark">{{ item.mobileRemark }}</div>
                  </div>
                  <div class="bonus">+{{ item.amount }}</div>
                </div>
                <div class="bottom">
                  <div class="text">
                    已关联金额
                    <span class="nums">{{ item.alreadyRelatedAmount }}</span>
                  </div>
                  <div class="text">
                    待审批金额
                    <span class="nums">{{ item.pendingAmount }}</span>
                  </div>
                </div>
                <div class="line"></div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <van-popup v-model="showTimePopup" position="bottom">
      <van-datetime-picker
        v-model="currentMonth"
        type="year-month"
        :formatter="formatter"
        @cancel="showTimePopup = false"
        @confirm="handleMonthChange"
      />
    </van-popup>
  </div>
</template>

<script>
import {
  NavBar,
  Button,
  List,
  PullRefresh,
  Toast,
  Sticky,
  DatetimePicker,
  Popup
} from "vant";
import { payMessageCenter } from "../../api/water";
export default {
  components: {
    [NavBar.name]: NavBar,
    [Button.name]: Button,
    [List.name]: List,
    [PullRefresh.name]: PullRefresh,
    [Toast.name]: Toast,
    [Sticky.name]: Sticky,
    [DatetimePicker.name]: DatetimePicker,
    [Popup.name]: Popup
  },
  data() {
    return {
      refreshing: false,
      finished: false,
      page: {
        no: 1,
        limit: 20
      },
      total: 0,
      loading: false,
      showTimePopup: false,
      currentMonth: new Date(),
      group: {},
      payStartTime: "",
      payEndTime: "",
      refundList: []
    };
  },
  methods: {
    onClickLeft() {
      this.backToApp();
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.page.no = 1;
      this.getRefundList();
    },
    async getRefundList() {
      if (this.refreshing) {
        this.bankStatementList = [];
        this.refreshing = false;
      }
      const params = {
        isPayType: 0,
        ...this.page,
        payStatuses: "",
        relatedStatus: "",
        payStartTime: "",
        payEndTime: ""
        // payStartTime: this.$moment()
        //   .startOf("month")
        //   .format("YYYY-MM-DD"),
        // payEndTime: this.$moment().format("YYYY-MM-DD")
      };
      if (this.payStartTime && this.payEndTime) {
        params.payStartTime = this.payStartTime;
        params.payEndTime = this.payEndTime;
      }
      try {
        const res = await payMessageCenter(params);
        this.loading = false;
        this.refundList = res.records || [];
        if (res.records.length == 0) {
          // 说明没有数据，也需要手动finished设为true，否则会一直请求
          this.finished = true;
        }
        if (res.records.length) {
          let yearMonth = "";
          res.records.forEach(item => {
            yearMonth = this.$moment(item.payTime2).format("YYYY/MM");
            if (this.group[item.payTime2]) {
              this.group[`${yearMonth}-${res.total}`].push(item);
            } else {
              this.group[`${yearMonth}-${res.total}`] = [item];
            }
          });
          if (this.group[`${yearMonth}-${res.total}`].length >= res.total) {
            this.finished = true;
          } else {
            this.page.no++;
          }
        }
        console.log(this.group, "group");
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.data || error.result
        });
      }
    },
    openTimePopup(val) {
      this.currentMonth = new Date(val.split("-")[0]);
      this.showTimePopup = true;
    },
    open() {
      this.showTimePopup = true;
    },
    formatter(type, val) {
      if (type === "year") {
        return `${val}年`;
      } else if (type === "month") {
        return `${val}月`;
      }
      return val;
    },
    handleMonthChange(value) {
      this.currentMonth = value;
      this.payStartTime = this.$moment(value)
        .startOf("month")
        .format("YYYY-MM-DD");
      this.payEndTime = this.$moment(value)
        .endOf("month")
        .format("YYYY-MM-DD");
      this.group = {};
      this.getRefundList();
      this.showTimePopup = false;
    },
    goRelated(item) {
      this.$router.push({
        name: "relatedWater",
        query: {
          id: item.id,
          customerId: item.customerId
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
