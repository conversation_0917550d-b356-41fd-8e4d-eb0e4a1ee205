<template>
  <div class="add-page">
    <navbar title="选择地址" :routerLink="true" @goBack="goBack" />
    <div class="list">
      <van-swipe-cell
        class="swipe-list"
        v-for="(item, index) in addressList"
        :key="index"
      >
        <div class="swipe-content">
          <div class="checked" v-show="selectIndex == index">
            <img
              style="width: 16px; height: 16px"
              src="../../../assets/service/icon_select.png"
              alt=""
            />
          </div>
          <div class="content">
            <div class="message" @click.stop="changeAddress(item, index)">
              <p class="loacation">{{ item.district }}</p>
              <p class="address">{{ item.address }}</p>
              <p>
                <span class="name">{{ item.recipient }} {{ item.mobile }}</span>
                <span class="icon-default" v-show="item.isDefault == 1"
                  >默认</span
                >
              </p>
            </div>
            <img
              @click="editAddress(item)"
              class="icon-edit"
              src="../../../assets/service/ico_edit2.png"
              alt=""
            />
          </div>
        </div>
        <template #right>
          <van-button
            square
            text="设为默认"
            type="default"
            class="defaut-button"
            @click="changeDefault(item.id)"
          />
          <van-button
            square
            text="删除"
            type="danger"
            class="delete-button"
            @click="delAddress(item.id)"
          />
        </template>
      </van-swipe-cell>
    </div>
    <footer>
      <button class="btn" @click="addAddress">新增地址</button>
    </footer>
  </div>
</template>

<script>
// api
import {
  getAddressList,
  removeAddress,
  setDefaultAddress
} from "../../../api/serviceSubDetail";
import navbar from "../../../components/navbar.vue";
import { Button, SwipeCell, Notify } from "vant";
export default {
  components: {
    navbar,
    [Button.name]: Button,
    [SwipeCell.name]: SwipeCell,
    [Notify.name]: Notify
  },
  data() {
    return {
      selectIndex: 0, //选中
      addressList: []
    };
  },
  mounted() {
    let { addressId } = this.$route.query;
    this.getAddressList(addressId);
  },

  methods: {
    // 获取地址列表
    async getAddressList(addressId = "") {
      try {
        const res = await getAddressList();
        res.forEach((item) => {
          item.district = item.district
            ? item.district.replace(/\//g, " ")
            : "";
        });
        if (addressId) {
          this.selectIndex = res.findIndex((item) => {
            return item.id == addressId;
          });
        }

        this.addressList = res || [];
      } catch (error) {
        console.log(error);
      }
    },

    // 删除地址
    async delAddress(id) {
      try {
        await removeAddress({
          id
        });
        Notify({ type: "success", message: "删除成功" });
        this.getAddressList();
      } catch (error) {
        console.log(error);
      }
    },

    // 设置默认地址
    async changeDefault(id) {
      try {
        await setDefaultAddress({ id });
        Notify({ type: "success", message: "设置成功" });
        this.getAddressList();
      } catch (error) {
        console.log(error);
      }
    },

    // 选择地址
    changeAddress(item, index) {
      this.selectIndex = index;
      // 如果是地址管理页面，返回时记录当前页面
      if (this.$route.name == "addressManage") {
        sessionStorage.setItem("path", this.$route.name);
        this.$router.push({
          name: "serviceSubDetail",
          params: {
            isChange: true,
            id: item.id,
            recipient: item.recipient,
            mobile: item.mobile,
            address: item.address,
            district: item.district
          },
          query: {
            id: this.$route.query.id
          }
        });
      }
    },
    // 新增地址
    addAddress() {
      this.$router.push({
        name: "addAddress",
        params: { edit: false },
        query: {
          id: this.$route.query.id
        }
      });
    },
    // 编辑地址
    editAddress(val) {
      this.$router.push({
        name: "addAddress",
        params: {
          edit: true,
          id: val.id,
          recipient: val.recipient,
          mobile: val.mobile,
          location: val.location,
          address: val.address,
          isDefault: val.isDefault,
          district: val.district
        },
        query: {
          id: this.$route.query.id
        }
      });
    },

    // 地址列表页返回详情页
    goBack() {
      this.$router.push({
        name: "serviceSubDetail",
        query: {
          id: this.$route.query.id
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.add-page {
  height: 100%;
  background: #f7f7f7;
  display: flex;
  flex-direction: column;
  .list {
    flex: 1;
    background: #fff;
    margin-top: 8px;
    overflow: auto;

    .swipe-list {
      border-bottom: 0.5px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }
    }

    .swipe-content {
      height: 110px;
      display: flex;
      align-items: center;
      padding: 0 0 0 16px;

      .checked {
        width: 32px;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .content {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .message {
          flex: 1;
        }
      }

      p {
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        letter-spacing: 0;
        line-height: 20px;
        margin-bottom: 4px;
      }
      .icon-edit {
        width: 16px;
        height: 16px;
        margin: 0 16px;
      }
      .icon-default {
        width: 24px;
        height: 14px;
        background: #ff9900;
        font-size: 10px;
        border-radius: 2px;
        color: #fff;
        padding: 1px 2px;
        margin-left: 6px;
        border-radius: 3px;
      }

      .loacation {
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #7f7f7f;
        letter-spacing: 0;
        line-height: 20px;
      }

      .address {
        height: 24px;
        font-family: PingFangSC-SNaNpxibold;
        font-weight: 600;
        font-size: 16px;
        color: #000000;
        letter-spacing: 0;
        line-height: 24px;
      }

      .name {
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        letter-spacing: 0;
        line-height: 20px;
      }
    }
  }

  footer {
    height: 56px;
    background-color: #fff;
    border-top: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    justify-content: center;

    button {
      background: none;
      color: inherit;
      border: none;
      padding: 0;
      font: inherit;
      cursor: pointer;
      outline: inherit;
    }

    .btn {
      color: #fff;
      width: 343px;
      height: 40px;
      background: #ff9900;
      border-radius: 8px;
    }
  }
}

.defaut-button {
  width: 64px;
  height: 100%;
  font-size: 12px;
  background: #f7f7f7;
}
.delete-button {
  width: 64px;
  height: 100%;
  font-size: 12px;
  background: #ff4646;
}
</style>