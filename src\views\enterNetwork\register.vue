<template>
  <div>
    <van-sticky>
      <div class="top">
        <div class="tabs-box">
          <div
            class="btn"
            :class="{ active: activeTabName == 'dsq' }"
            @click="handleClick('dsq')"
          >
            电视墙注册
          </div>
          <div
            class="btn"
            :class="{ active: activeTabName == 'gl' }"
            @click="handleClick('gl')"
          >
            管理
          </div>
        </div>
      </div>
    </van-sticky>
    <dsq
      v-if="activeTabName == 'dsq'"
      :groupId="groupId"
      :deptId="deptId"
    ></dsq>
    <gl v-else :groupId="groupId"></gl>
  </div>
</template>
<script>
import { Toast, Sticky } from "vant";
import dsq from "./dsq.vue";
import gl from "./gl.vue";

export default {
  name: "enterNetworkRegister",
  components: {
    dsq,
    gl,
    [Toast.name]: Toast,
    [Sticky.name]: Sticky
  },
  data() {
    return {
      activeTabName: "dsq",
      groupId: "",
      deptId: ""
    };
  },
  created() {
    this.groupId = this.$route.query.groupId;
    this.deptId = this.$route.query.deptId;
  },
  methods: {
    handleClick(tab) {
      this.activeTabName = tab;
    }
  }
};
</script>
<style lang="scss" scoped>
.top {
  background-color: #fff;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tabs-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 196px;
  height: 36px;
  background: #f7f7fa;
  border-radius: 18px;
  padding: 0 4px;
  .btn {
    width: 94px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    background: #f7f7fa;
    font-size: 14px;
    color: #898fa3;
    &.active {
      color: #1e232e;
      background: #ffffff;
      border-radius: 16px;
    }
  }
}
</style>
