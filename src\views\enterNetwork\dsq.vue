<template>
  <div class="dsq-box">
    <div class="welcome">欢迎使用</div>
    <div class="sub-text">万店掌电视墙管理软件</div>
    <div class="paragraph">
      支持预案轮巡，具备云平台流媒体转发功能与远程多点分控功能。
    </div>
    <div class="paragraph">
      支持与指定前端进行双向语音对讲功能，支持前端报警联动电视墙视频弹出。
    </div>
    <div class="radio-box">
      <van-radio-group v-model="mode" checked-color="#FF9900">
        <van-radio name="1">使用企业默认授权码，已有万店掌订单</van-radio>
      </van-radio-group>
    </div>
    <div class="btn-box">
      <van-button round class="retry-scan" @click="handleResetDevice"
        >注册新设备</van-button
      >
      <van-button
        round
        type="info"
        color="#FF9900"
        class="register-btn"
        @click="grant"
        >注册</van-button
      >
    </div>
  </div>
</template>

<script>
import { RadioGroup, Radio, Button, Field, Toast } from "vant";
import { grantAuthorization } from "../../api/enterNetwork";
import Cookie from "js-cookie";
export default {
  components: {
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio,
    [Button.name]: Button,
    [Field.name]: Field,
    [Toast.name]: Toast
  },
  props: {
    groupId: {
      type: [String, Number],
      default: ""
    },
    deptId: {
      type: [String, Number],
      default: ""
    }
  },
  data() {
    return {
      mode: "1",
      productCode: "",
      activeRemark: "",
      macAddress: ""
    };
  },
  created() {
    this.productCode = this.$route.query.productCode;
    this.activeRemark = this.$route.query.activeRemark;
    this.macAddress = this.$route.query.macAddress;
  },
  methods: {
    async grant() {
      const params = {
        activeRemark: this.activeRemark,
        groupId: this.groupId,
        macAddress: this.macAddress,
        productCode: this.productCode
      };
      if (this.groupId && this.deptId && this.groupId != this.deptId) {
        Toast.fail({
          duration: 2000,
          message: "请使用客户端同一企业账号登录激活"
        });
        return;
      }
      try {
        await grantAuthorization(params);
        this.$router.push({
          name: "result",
          query: {
            groupId: this.groupId,
            deptId: this.deptId,
            token: Cookie.get("token")
          }
        });
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.result
        });
      }
    },
    handleResetDevice() {
      this.$router.push({
        name: "scan",
        query: {
          groupId: this.groupId,
          deptId: this.deptId,
          token: Cookie.get("token")
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .van-radio {
  margin-bottom: 24px;
}
/deep/ .van-cell {
  width: 343px;
  height: 56px;
  background: #f5f6fa;
  border-radius: 28px;
  padding: 0 16px;
  .van-cell__value {
    display: flex;
    align-items: center;
    .van-field__body {
      width: 100%;
    }
  }
}
.dsq-box {
  background-color: #fff;
  height: 100vh;
  padding: 24px 16px;
  .welcome {
    font-family: PingFangSC-SNaNpxibold;
    font-size: 28px;
    color: #1e232e;
    font-weight: bold;
    margin-bottom: 4px;
  }
  .sub-text {
    font-family: PingFangSC-SNaNpxibold;
    font-weight: bold;
    font-size: 24px;
    color: #1e232e;
    margin-bottom: 16px;
  }
  .paragraph {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #898fa3;
    line-height: 22px;
    margin-bottom: 8px;
  }
  .radio-box {
    margin-top: 24px;
  }

  .btn-box {
    display: flex;
    justify-content: space-between;
    margin-top: 150px;
    .retry-scan {
      width: 112px;
      height: 48px;
      border: 1px solid #e4e6f0;
      border-radius: 24px;
    }
    .register-btn {
      width: 219px;
      height: 48px;
      border-radius: 24px;
    }
  }
}
</style>
