<template>
  <div class="logistics-search-page">
    <!-- header -->
    <header>
      <van-nav-bar title="物流跟踪" left-arrow @click-left="onClickLeft" />
      <div class="search">
        <van-field
          v-model="keyWords"
          rows="1"
          placeholder="输入客户名称/合同号查询物流进度"
          clearable
          left-icon="search"
          @keydown.enter="search"
        />
      </div>
      <div class="search-conditions">
        <div class="item" @click="showTime = true">
          期望发货日期
          <img src="../../../assets/rect.png" alt="" />
        </div>
        <div class="item" @click="showDeliver = true">
          发货状态
          <img src="../../../assets/rect.png" alt="" />
        </div>
      </div>

      <div class="logist-content-title" v-if="result.length > 0">
        发货数据概览：{{ sent }} / {{ notSent }}（已发货/待发货）
      </div>
    </header>

    <!-- main -->
    <main>
      <!-- 未输入时候内容显示 -->
      <div class="main-tips" v-if="result.length == 0">
        <img class="img" src="../../../assets/kb.png" />
        <p>上方搜索关键词，快速查询物流信息～</p>
      </div>

      <!-- 有数据显示 -->
      <van-list
        class="logist-content"
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div
          class="logist-content-list"
          v-for="(item, index) in result"
          :key="index"
          @click="goDetail(item)"
        >
          <div class="logist-item">
            <label>客户名称 </label>
            <span>{{ item.customerName || "--" }}</span>
          </div>
          <div class="logist-item">
            <label>合同编号</label>
            <span>{{ item.contractNo || "--" }}</span>
          </div>
          <div class="logist-item">
            <label>收货人</label>
            <span>{{ item.receiver || "--" }}</span>
          </div>
          <div class="logist-item">
            <label>收货地址</label>
            <span>{{ item.address || "--" }}</span>
          </div>
        </div>
      </van-list>
    </main>

    <!-- 期望发货 -->
    <timePopup v-model="showTime" @sureClick="sureClick" />
    <!-- 发货状态 -->
    <deliverPopup v-model="showDeliver" @changeStatus="changeStatus" />
    <!-- 列表详情 -->
    <logisticsInfo v-model="showDetail" :receiverId="receiverId" />
  </div>
</template>

<script>
// api
import { getH5Receiver } from "../../../api/logisticsTrack";
// components
import { NavBar, Field, Loading, Icon, List } from "vant";
import deliverPopup from "./components/deliverPopup.vue";
import timePopup from "./components/timePopup.vue";
import logisticsInfo from "../logisticsInfo/index.vue";
export default {
  components: {
    [NavBar.name]: NavBar,
    [Field.name]: Field,
    [Loading.name]: Loading,
    [Icon.name]: Icon,
    [List.name]: List,
    deliverPopup,
    timePopup,
    logisticsInfo
  },
  data() {
    return {
      keyWords: "",
      pageNumber: 0,
      result: [],
      total: 0,
      busy: true,
      showTime: false,
      showDeliver: false,
      notSent: 0,
      sent: 0,
      deliveryStatus: "1,3",
      tempParams: {},
      type: "",
      showDetail: false,
      receiverId: "",
      loading: false,
      finished: false
    };
  },
  computed: {},
  methods: {
    changeStatus(val) {
      if (val == 2) {
        this.deliveryStatus = "1,3";
      } else {
        this.deliveryStatus = val;
      }
      this.init();
    },

    init() {
      this.result = [];
      this.pageNumber = 0;
      this.loading = true;
      this.finished = false;
      if (this.loading) {
        this.onLoad();
      }
    },

    sureClick(val) {
      // 清空this.tempParams内deliveryDateMap的key 和 value
      this.tempParams = {};
      this.type = val.type;
      // 获取今天日期
      const date = new Date();
      if (val?.type == 1) {
        let time = "";
        if (val.deliveryDateMap == 1) {
          // 今天以前 今天日期减一
          time = this.$moment(date.setDate(date.getDate() - 1)).format(
            "YYYY-MM-DD"
          );
          this.tempParams[`deliveryDateMap[${time}]`] = "<=";
        } else if (val.deliveryDateMap == 2) {
          time = this.$moment(date).format("YYYY-MM-DD");
          this.tempParams[`deliveryDateMap[${time}]`] = "=";
        } else if (val.deliveryDateMap == 3) {
          // 今天以后 今天日期加一
          time = this.$moment(date.setDate(date.getDate() + 1)).format(
            "YYYY-MM-DD"
          );
          this.tempParams[`deliveryDateMap[${time}]`] = ">=";
        }
      } else if (val?.type == 2) {
        this.tempParams.deliverStartDate = val.deliverStartDate;
        this.tempParams.deliverEndDate = val.deliverEndDate;
      }
      this.init();
    },

    onLoad() {
      this.pageNumber++;
      this.search();
    },

    // 搜索
    async search() {
      let params = {
        no: this.pageNumber,
        limit: 10,
        keyWords: this.keyWords,
        deliveryStatus: this.deliveryStatus
      };
      // 默认今天
      if (!this.type) {
        params[
          `deliveryDateMap[${this.$moment(new Date()).format("YYYY-MM-DD")}]`
        ] = `=`;
      }

      // 合并对象
      params = Object.assign(params, this.tempParams);
      try {
        const res = await getH5Receiver(params);
        this.notSent = res?.notSent;
        this.sent = res?.sent;
        this.total = res?.total;
        if (this.pageNumber == 1) {
          this.result = res?.record;
        } else {
          this.result = this.result.concat(res?.record);
        }
        this.loading = false;
        if (this.result.length >= this.total) {
          this.finished = true;
        }
      } catch (error) {
        console.log(error);
      }
    },

    /**
     * 查看详情
     */
    goDetail(obj) {
      this.showDetail = true;
      this.receiverId = obj.id;
    },

    // 返回上一页
    onClickLeft() {
      this.backToApp();
    }
  },
  filters: {
    numSplit(val) {
      return val.split(",")[0];
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .van-nav-bar .van-icon {
  color: #333;
  font-size: 18px;
}
.logistics-search-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  header {
    min-height: 100px;
    .search {
      padding: 10px 16px;
      background-color: #fff;
      display: flex;
      align-items: center;

      /deep/ .van-cell {
        height: 36px;
        background-color: #f0f0f0;
        padding: 7px 11px;
        border-radius: 6px;
      }
    }

    .search-conditions {
      display: flex;
      justify-content: space-between;
      background-color: #fff;
      height: 44px;
      line-height: 44px;
      font-size: 14px;
      color: #000000;

      .item {
        width: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 8px;
          height: 5px;
          margin-left: 5px;
        }
      }
    }

    .logist-content-title {
      height: 30px;
      display: flex;
      align-items: center;
      background: rgba(255, 153, 0, 0.1);
      text-indent: 10px;
      color: #ff9900;
      font-size: 14px;
    }
  }

  main {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;

    .main-tips {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-top: 300px;
      .img {
        width: 214px;
        height: 148px;
      }

      p {
        width: 100%;
        text-align: center;
        font-size: 13px;
        color: #b2b2b2;
      }
    }
    .logist-content {
      .logist-content-list {
        margin: 12px;
        background-color: #fff;
        border-radius: 8px;
        font-size: 14px;
        box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.04);

        .logist-title {
          display: flex;
          justify-content: space-between;
          padding: 12px;
          background-color: #f6faff;

          label {
            color: #8acc47;
          }

          span {
            color: rgba(0, 0, 0, 0.5);
          }
        }

        .logist-item {
          display: flex;
          padding: 10px;

          label {
            width: 88px;
            color: rgba(0, 0, 0, 0.3);
          }

          span {
            display: inline-block;
            flex: 1;
            color: #000000;
          }
        }
      }

      .no-more {
        font-size: 14px;
        color: #ccc;
      }
    }
  }
}
</style>
