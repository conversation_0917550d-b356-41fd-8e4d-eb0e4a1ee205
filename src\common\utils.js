import Cookie from "js-cookie";

/**
 * 检测传入的参数类型
 */
export function typeOf(obj) {
  const toString = Object.prototype.toString;
  const map = {
    "[object Boolean]": "boolean",
    "[object Number]": "number",
    "[object String]": "string",
    "[object Function]": "function",
    "[object Array]": "array",
    "[object Date]": "date",
    "[object RegExp]": "regExp",
    "[object Undefined]": "undefined",
    "[object Null]": "null",
    "[object Object]": "object",
  };
  return map[toString.call(obj)];
}

/**
 * 判断对象是否为空
 */
export function notEmptyObj(obj) {
  let arr = Object.keys(obj);
  return arr.length > 0;
}

/**
 * 对象去空
 */
export function deleteObjEmpty(target, fieldList) {
  let ret = {};

  if (fieldList == null) {
    for (let k in target) {
      if (target[k] !== "" && target[k] != null) ret[k] = target[k];
    }
  } else {
    ret = Object.assign({}, target);
    fieldList.forEach((k) => {
      if (ret[k] === "" || ret[k] == null) delete ret[k];
    });
  }

  return ret;
}

/**
 * 筛选出目标对象里有的字段，目标对象里没有的字段剔除
 */
export function objKeyFilter(target, obj) {
  const keyList = Object.keys(target);
  let ret = {};
  keyList.forEach((k) => {
    ret[k] = obj[k] && obj[k] != "null" ? obj[k] : "";
  });
  return ret;
}

/**
 * 防抖函数（防连点）
 */
export function simpleThrottler(fn, timer, params) {
  if (timer.timerId) {
    clearTimeout(timer.timerId);
    timer.timerId = "";
  }
  timer.timerId = setTimeout(() => {
    fn(params);
    timer.timerId = "";
  }, timer.delay);
}

/**
 * 节流器
 */
export class Throttle {
  constructor(interval = 200) {
    this._timeoutId = null;
    this._interval = interval;
    this.funcPool = [];
  }

  add(...funcList) {
    this.funcPool.push(...funcList);
  }

  remove(func) {
    this.funcPool = this.funcPool.filter((item) => item !== func);
  }

  /**
   * 触发函数
   * @param args 传递到每一个 fn 中的参数
   */
  handle(...args) {
    const funcPool = this.funcPool;
    if (this._timeoutId != null) clearTimeout(this._timeoutId);
    this._timeoutId = setTimeout(() => {
      funcPool.forEach((func) => {
        func(...args);
      });
    }, this._interval);
  }
}

/**
 * 对象深度查找
 */
export function findDeeply(target, callback) {
  const flag = typeOf(target);
  let result;
  if (flag === "array") {
    for (let i = 0; i < target.length; i++) {
      let item = target[i];
      result = findDeeply(item, callback);
      if (result) return result;
    }
  } else if (flag === "object") {
    if (callback(target)) {
      return target;
    }
    for (let k in target) {
      result = findDeeply(target[k], callback);
      if (result) return result;
    }
  }
}

/**
 * 对象深度查找，返回list数组
 */
export function findDeeplyList(target, callback, arr = []) {
  const flag = typeOf(target);

  if (flag === "array") {
    for (let i = 0; i < target.length; i++) {
      findDeeplyList(target[i], callback, arr);
    }
  } else if (flag === "object") {
    if (callback(target)) {
      arr.push(target);
    }
    for (let k in target) {
      findDeeplyList(target[k], callback, arr);
    }
  }
  return arr;
}

export function _userInfo(data = {}) {
  const { username, usercode, usertype, headUrl, id, areaList, superman } =
    data;

  Cookie.set("username", username, { expires: 365 });
  Cookie.set("usercode", usercode, { expires: 365 });
  Cookie.set("userId", id, { expires: 365 });
  Cookie.set("headUrl", headUrl);
  Cookie.set("usertype", usertype, { expires: 365 });
  Cookie.set("isSuper", superman);
  localStorage.setItem("areaList", JSON.stringify(areaList));
}

/**
 * 两个数组对比取不同的值
 */
export function getdiffarr(A, B) {
  var C = new Array();
  var D = new Array();
  var E = new Array();
  var Astr = "," + A.toString() + ",";
  var Bstr = "," + B.toString() + ",";
  for (var i in A) {
    if (Bstr.indexOf("," + A[i] + ",") < 0) {
      C.push(A[i]);
    }
  }
  for (var p in B) {
    if (Astr.indexOf("," + B[p] + ",") < 0) {
      D.push(B[p]);
    }
  }
  E.push(C);
  E.push(D);
  return E;
}

/**
 * 从一个数组中去除另一个数组中含有的值
 */
export function deleteArrayDiff(a, b) {
  for (var i = 0; i < b.length; i++) {
    for (var j = 0; j < a.length; j++) {
      if (a[j] == b[i]) {
        a.splice(j, 1);
        j = j - 1;
      }
    }
  }
  return a;
}

/**
 * 数组去重
 */
export function unique(arr) {
  let res = [];
  let json = {};
  for (let i = 0; i < arr.length; i++) {
    if (!json[arr[i]]) {
      res.push(arr[i]);
      json[arr[i]] = 1;
    }
  }
  return res;
}

const hasOneOf = (targetarr, arr) => {
  return targetarr.some((_) => arr.indexOf(_) > -1);
};

const hasChild = (item) => {
  return item.children && item.children.length !== 0;
};

const showThisMenuEle = (item, access) => {
  if (item.meta && item.meta.access && item.meta.access.length) {
    if (hasOneOf(item.meta.access, access)) return true;
    else return false;
  } else return true;
};

/**
 * 通过路由列表得到菜单列表
 *
 */
export const getMenuByRouter = (list, access) => {
  // console.log(list);
  let res = [];
  list.forEach((item) => {
    if (!item.meta || (item.meta && !item.meta.hideInMenu)) {
      let obj = {
        icon: (item.meta && item.meta.icon) || "",
        name: item.name,
        meta: item.meta,
      };
      if (
        (hasChild(item) || (item.meta && item.meta.showAlways)) &&
        showThisMenuEle(item, access)
      ) {
        obj.children = getMenuByRouter(item.children, access);
      }
      if (item.meta && item.meta.href) obj.href = item.meta.href;
      if (showThisMenuEle(item, access)) res.push(obj);
    }
  });
  return res;
};

/**
 * downLoadFile          下载网络资源文件
 * @param  {String}  url 资源文件地址
 */
export function downloadFile(url) {
  const suffixList = ["jpg", "jpeg", "png", "gif"];
  let iframe = document.getElementById("iframeReportImg"),
    list = url.split("."),
    length = list.length,
    urlSuffix = "",
    isImgFile = false,
    aElement = document.createElement("a");
  if (length === 0)
    return console.error("downloadFile 文件下载函数传入的资源地址不合法");
  urlSuffix = list[length - 1].toLowerCase();

  for (let i = 0, len = suffixList.length, item; i < len; i++) {
    item = suffixList[i];
    if (item === String(urlSuffix)) {
      isImgFile = true;
      break;
    }
  }

  // 如果是非图片类型文件
  if (!isImgFile) return (window.location.href = url);

  // 图片类型文件
  if (aElement.download != null) {
    // 如果浏览器支持 a.download
    aElement.download = url;
    aElement.href = url;
    aElement.target = "_blank";
    document.body.appendChild(aElement);
    aElement.click();
    document.body.removeChild(aElement);
  } else {
    // 否则
    _createIframe(url);
  }

  function _createIframe(imgSrc) {
    //如果隐藏的iframe不存在则创建
    if (iframe == null) {
      iframe = document.createElement("iframe");
      iframe.style.display = "none";
      iframe.width = 0;
      iframe.height = 0;
      iframe.id = "iframeReportImg";
      iframe.name = "iframeReportImg";
      iframe.src = "about:blank";
      document.body.appendChild(iframe);
      iframe.addEventListener("load", _downloadImg, false);
    }
    //iframe的src属性如不指向图片地址,则手动修改,加载图片
    if (String(iframe.src) != imgSrc) {
      iframe.src = imgSrc;
    } else {
      //如指向图片地址,直接调用下载方法
      _downloadImg();
    }
  }
  //下载图片的函数
  function _downloadImg() {
    //iframe的src属性不为空,调用execCommand(),保存图片
    if (iframe.src != "about:blank") {
      window.frames["iframeReportImg"].document.execCommand("SaveAs");
    }
  }
}
/**
 * 必要的级联展示的数据是label和value的形式的所以
 * data:原数据,
 * key:展示的字段
 * value:选中获取的字段
 */
export function treeChangeCascader(data, key, value, extend = "") {
  if (!(data instanceof Array)) {
    return [];
  }

  let cluing = data.map((item) => {
    let result = {
      label: item[key],
      value: item[value],
    };

    if (extend) {
      result[extend] = item[extend]; //额外希望获取的值
    }

    if (item.children && item.children.length) {
      result.children = item.children.map((element) => {
        let child = {
          label: element[key],
          value: element[value],
        };

        if (extend) {
          child[extend] = element[extend]; //额外希望获取的值
        }

        return child; //子级的转换结果
      });
    }

    return result;
  });

  return cluing; //最终转换的结果
}


/**
 * 判空函数
 * @param  {obj/arr/str}  检测对象
 */
export function empty(obj) {
  const objType = typeOf(obj);
  if (objType === "array") {
    return !obj.length > 0;
  } else if (objType === "object") {
    return !Object.keys(obj).length > 0;
  } else if (objType === "string") {
    // string
    return !obj.trim().length > 0;
  } else {
    return false;
  }
}

/**
 * 轮询函数
 * @param judgeFunc { Function }    判断函数，每次轮询通过这个判断函数执行结果，来判断是否完成轮询
 * @param callBack  { Function }    完成轮询执行的回调
 * @param interval  { Number }      轮询间隔
 * @param list      { like-array }  传入回调函数中的额外参数
 */
export function polling(judgeFunc, callBack, interval = 0, ...list) {
  const flag = typeOf(judgeFunc) === "function" && judgeFunc();
  if (flag) {
    callBack(...list);
  } else {
    setTimeout(() => {
      polling(judgeFunc, callBack, interval, ...list);
    }, interval);
  }
}

/**
 * 小数四舍五入
 * @param number        需要操作的数字
 * @param precision     需要保留小数位数，如果有的小数的话，默认不保留小数位
 * @returns {number}    返回的数
 */
export function round(number, precision = 0) {
  return Math.round(+number + "e" + precision) / Math.pow(10, precision);
  //same as:
  //return Number(Math.round(+number + 'e' + precision) + 'e-' + precision);
}

export function isFunction(obj) {
  return typeOf(obj) === "function";
}

/**
 * 数组去空【空字符串 null undefined】
 * @param arr Array 需要处理的原始数组
 * @return Array 处理后的数组对象
 *
 */
export function arrayDeleteEmpty(arr) {
  let clean = [];
  let target = "null undefined";

  for (let i = 0, len = arr.length; i < len; i++) {
    let item = arr[i];
    let type = typeOf(item);
    if ((type === "string" && empty(item)) || target.indexOf(type) >= 0)
      continue;
    clean.push(item);
  }
  return clean;
}

export function refCompare(target1, target2, filter, cb) {
  for (let i = 0, item; i < target1.length; i++) {
    item = target1[i];
    for (let k = 0, part; k < target2.length; k++) {
      part = target2[k];
      if (filter(item, part)) cb && cb(i, k);
    }
  }
}

//获取客户端详情
export function getUserAgentInfo(){
  let ua = navigator.userAgent.toLowerCase();
  return ua
}

   /**查询url中的参数 */
   export function GetQueryString(name) {
    let url = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    let newUrl = window.location.search.substr(1).match(url);
    if (newUrl != null) {
      return unescape(newUrl[2]);
    } else {
      return false;
    }
  }