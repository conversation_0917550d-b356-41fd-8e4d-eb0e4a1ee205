<template>
  <div class="paySuccess-page">
    <!-- <van-nav-bar title="付款成功"></van-nav-bar> -->
    <header>
      <div class="img-box">
        <img src="../assets/img/logo.png" alt="">
      </div>
      <div class="header-txt">万千门店·尽在掌控</div>
    </header>
    <!-- body -->
    <div class="page-body">
      <div class="success-box">
        <div class="success-icon">
          <img src="../assets/img/icon-success.png" alt="" />
        </div>
        <p class="text-success">恭喜您，付款成功</p>
      </div>
      <van-divider />
    </div>
  </div>
</template>

<script>
import { NavBar, Button, Divider } from "vant";
export default {
  components: {
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Divider.name]: Divider,
  },
  data() {
    return {};
  },
  mounted() {},
  watch: {},
  methods: {},
};
</script>

<style scoped lang="scss">
.paySuccess-page {
  height: 100%;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;

  header {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .img-box {
      margin-top: 42px;
      margin-bottom: 15px;
      
      img {
        width: 63px;
        height: 38px;
      }
    }
    .header-txt {
      width: 128px;
      height: 21px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 15px;
      color: #7F7F7F;
      margin: 0 auto;
      margin-bottom: 49px;
    }
  }
  .page-body {
    display: flex;
    justify-content: center;
    padding: 0 32px;
    flex-direction: column;
    .success-box {
      width: 232px;
      height: 183px;
      display: flex;
      margin: 0 auto;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-image: url("../assets/img/<EMAIL>");
      background-repeat: no-repeat;
      background-size: 100%;

      .success-icon {
        width: 48px;
        height: 48px;

        img {
          width: 100%;
          height: 100%;
        }
      }
      .text-success {
        height: 29px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 18px;
        color: #1cbb61;
        letter-spacing: 0;
        margin-top: 16px;
      }
    }

    .content {
      .list {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        .list-txet {
          height: 20px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #7f7f7f;
          letter-spacing: 0;
        }
        .list-content {
          height: 20px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          letter-spacing: 0;
          text-align: right;
        }
      }
    }
  }
  .footer {
    padding: 0 32px;
  }
  /deep/.van-button {
    border-radius: 5px;
  }
}
</style>