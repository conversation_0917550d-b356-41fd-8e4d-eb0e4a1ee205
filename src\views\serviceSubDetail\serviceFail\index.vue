<template>
  <div class="passPage">
    <!-- <van-nav-bar  left-arrow></van-nav-bar> -->
    <van-nav-bar title="支付中心"></van-nav-bar>
    <!-- 成功 -->
    <div class="pass-body" v-show="status">
      <div class="image">
        <img src="../../../assets/pass.png" alt="" />
      </div>
      <div class="payPass">支付成功</div>
      <div class="content">感谢使用微信支付</div>
    </div>
    <!-- 失败 -->
    <div class="pass-body" v-show="!status">
      <div class="image">
        <img src="../../../assets/fail.png" alt="" />
      </div>
      <div class="payPass">支付失败</div>
      <div class="content">支付遇到问题，请尝试重新支付</div>
    </div>
  </div>
</template>

<script>
import { NavBar, Button } from "vant";
export default {
  components: {
    [Button.name]: But<PERSON>,
    [NavBar.name]: NavBar
  },

  data() {
    return {
      status: false
    };
  },
  mounted() {
    let { tradeState } = this.$route.query;
    if (tradeState == "NOTPAY") {
      this.status = false;
    } else if (tradeState == "SUCCESS") {
      this.status = true;
    }
  }
};
</script>

<style scoped lang="scss">
.passPage {
  height: 100%;
  width: 100%;
  background-color: #fff;
}
/deep/.van-nav-bar__title {
  height: 24px;
  font-family: PingFangSC-Semibold;
  font-weight: bold;
  font-size: 17px;
  color: #333333;
  text-align: center;
}
.image {
  margin-top: 78px;
  display: flex;
  justify-content: center;
}
.payPass {
  display: flex;
  justify-content: center;
  margin-top: 12px;
  font-size: 16px;
  color: #333333;
  letter-spacing: 0;
}
.content {
  display: flex;
  justify-content: center;
  font-size: 13px;
  color: #b2b2b2;
  margin-top: 6px;
}
.button {
  margin-top: 120px;
  display: flex;
  justify-content: space-around;
  .btn {
    border: 1px solid #e5e5e5;
  }
}
/deep/.van-button--normal {
  width: 150px;
  height: 44px;
  padding: 0 4vw;
  border-radius: 6px;
}
</style>
