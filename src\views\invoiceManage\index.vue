<template>
  <div class="invoice-manage">
    <van-nav-bar title="发票管理" left-arrow @click-left="onClickLeft" />
    <van-sticky>
      <van-tabs v-model="billType" @change="change">
        <van-tab title="待开票" name="0"></van-tab>
        <van-tab title="已开票" name="1"></van-tab>
      </van-tabs>
    </van-sticky>
    <div class="invoice-container">
      <van-pull-refresh @refresh="refresh" v-model="refreshing">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div class="invoice" v-for="item in invoiceList" :key="item.id">
            <div class="invoice-top">
              <p class="bill-number">发票编号:{{ item.billingNo }}</p>
              <span
                :class="[
                  'audit',
                  item.status === '0' && 'orange',
                  item.status === '1' && 'gold',
                  item.status === '2' && 'lime',
                  item.status === '3' && 'yellow',
                  item.status === '4' && 'geekblue',
                  item.status === '5' && 'purple'
                ]"
                >{{ renderStatus(item.status) }}</span
              >
            </div>
            <div class="invoice-content">
              <div class="cell">
                <div class="label">客户名称:</div>
                <div class="value">{{ item.customerName }}</div>
              </div>
              <div class="cell">
                <div class="label">开票类型:</div>
                <div class="value">{{ item.billingType }}</div>
              </div>
              <div class="cell">
                <div class="label">开票抬头:</div>
                <div class="value">{{ item.billingName }}</div>
              </div>
              <div class="cell">
                <div class="label">税 号:</div>
                <div class="value">{{ item.taxpayerNo }}</div>
              </div>
              <div class="cell">
                <div class="label">申请时间:</div>
                <div class="value">{{ item.applyTime }}</div>
              </div>
            </div>
            <div class="invoice-bottom">
              <div class="contact-item">
                <van-icon name="contact" />
                <span class="value">{{ item.username }}</span>
              </div>
              <div class="contact-item">
                <van-icon name="balance-o" />
                <span class="value">{{ item.billingMoney }}</span>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>
<script>
import { NavBar, Icon, Tabs, Tab, Sticky, List, Cell, PullRefresh } from "vant";
import { getCrmBillingPage } from "../../api/bill";

export default {
  name: "invoiceManage",
  components: {
    [NavBar.name]: NavBar,
    [Icon.name]: Icon,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
    [Sticky.name]: Sticky,
    [List.name]: List,
    [Cell.name]: Cell,
    [PullRefresh.name]: PullRefresh
  },
  data() {
    return {
      invoiceList: [],
      billType: "0",
      no: 1,
      limit: 10,
      type: "electronics",
      total: 0,
      loading: false,
      finished: false,
      refreshing: false
    };
  },
  computed: {
    usableInvoiceStatus() {
      // 可用的发票状态查询
      const invoiceStatusList = [
        {
          label: "草稿",
          value: "5"
        },
        {
          label: "待审核",
          value: "0"
        },
        {
          label: "已审核",
          value: "1"
        },
        {
          label: "已开票",
          value: "2"
        },
        {
          label: "已作废",
          value: "3"
        },
        {
          label: "已红冲",
          value: "4"
        }
      ];
      if (this.type === "invoice") {
        return invoiceStatusList;
      } else if (this.type === "valueAddedTax" || this.type === "electronics") {
        return invoiceStatusList.splice(1, 5);
      } else {
        return invoiceStatusList.splice(3, 3);
      }
    }
  },
  mounted() {
    this.onLoad();
  },
  methods: {
    onClickLeft() {
      this.backToApp();
    },
    async onLoad() {
      if (this.refreshing) {
        this.invoiceList = [];
        this.refreshing = false;
      }
      const { no, limit, type, billType } = this;
      const params = {
        no,
        limit,
        pageType: type,
        isBilling: billType
      };
      this.loading = true;
      try {
        const res = await getCrmBillingPage(params);
        this.total = res.total;
        if (res?.records?.length) {
          const list = res?.records;
          this.invoiceList.push(...list);
          this.no += 1;
        } else {
          this.finished = true;
        }
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },
    refresh() {
      this.finished = false;
      this.loading = true;
      this.no = 1;
      this.onLoad();
    },
    renderStatus(status) {
      return this.usableInvoiceStatus.find(item => item.value === status)
        ?.label;
    },
    change(name) {
      this.billType = name;
      this.no = 1;
      this.invoiceList = [];
      this.onLoad();
    }
  }
};
</script>
<style scoped lang="scss">
/deep/ .van-nav-bar .van-icon {
  color: #666;
  font-size: 28px;
}
/deep/ .van-tab--active {
  color: #ff9900;
}
/deep/ .van-tabs__line {
  background-color: #ff9900;
}

.invoice-container {
  padding: 12px 16px;
  .invoice {
    background: #fff;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 12px;
    .invoice-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-family: PingFangSC-Medium;
      font-weight: bold;
      line-height: 24px;
      .bill-number {
        font-size: 16px;
        color: #000000;
      }
      .audit {
        font-size: 16px;
        &.orange {
          color: orange;
        }
        &.gold {
          color: gold;
        }
        &.lime {
          color: lime;
        }
        &.yellow {
          color: yellow;
        }
        &.geekblue {
          color: geekblue;
        }
        &.purple {
          color: purple;
        }
      }
    }
    .invoice-content {
      margin-top: 8px;
      .cell {
        font-weight: 400;
        line-height: 20px;
        color: #7f7f7f;
        margin-bottom: 4px;
        display: flex;
        font-size: 12px;
        .label {
          margin-right: 8px;
          width: 52px;
        }
      }
    }
    .invoice-bottom {
      margin-top: 20px;
      display: flex;
      align-items: center;
      .contact-item {
        display: flex;
        align-items: center;
        line-height: 20px;
        margin-right: 34.5px;
        .value {
          margin-left: 10px;
          font-weight: 500;
          font-size: 14px;
          color: #333333;
          font-family: PingFangSC-Medium;
        }
      }
    }
  }
}
</style>
