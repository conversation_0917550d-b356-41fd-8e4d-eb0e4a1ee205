<template>
  <div class="order-confirm-page">
    <!-- 导航栏 -->
    <div class="nav-header">
      <van-nav-bar title="确认订单" left-arrow @click-left="onClickLeft" />
    </div>

    <!-- 主要内容区域 -->
    <div class="content">
      <!-- 商品信息 -->
      <div class="product-section">
        <div class="section-header">
          <div class="section-title">小鸟探店-轻餐</div>
          <div class="status-tag success">
            <span>议价成功</span>
          </div>
        </div>
        <div class="product-info">
          <div class="info-row">
            <span class="label">产品类型</span>
            <span class="value">软件</span>
          </div>
          <div class="info-row">
            <span class="label">产品小类</span>
            <span class="value">小鸟探店</span>
          </div>
          <div class="info-row">
            <span class="label">单价</span>
            <span class="value">800</span>
          </div>
          <div class="info-row">
            <span class="label">购买时长（月）</span>
            <span class="value">12</span>
          </div>
          <div class="info-row">
            <span class="label">购买数量</span>
            <span class="value">1</span>
          </div>
          <div class="info-row">
            <span class="label">应付金额</span>
            <span class="value price">100</span>
          </div>
        </div>
      </div>

      <!-- 配套产品列表 -->
      <div class="accessories-section">
        <div class="section-title">配套产品列表</div>

        <!-- 产品项目1 -->
        <div class="product-item">
          <div class="product-name">P2 2.8mm</div>
          <div class="product-info">
            <div class="info-row">
              <span class="label">产品类型</span>
              <span class="value">软件</span>
            </div>
            <div class="info-row">
              <span class="label">产品小类</span>
              <span class="value">智能定点摄像机</span>
            </div>
            <div class="info-row">
              <span class="label">单价</span>
              <span class="value">80</span>
            </div>
            <div class="info-row">
              <span class="label">购买数量</span>
              <span class="value">1</span>
            </div>
            <div class="info-row">
              <span class="label">应付金额</span>
              <span class="value price">100</span>
            </div>
          </div>
        </div>

        <!-- 产品项目2 -->
        <div class="product-item">
          <div class="product-name">P2 2.8mm</div>
          <div class="product-info">
            <div class="info-row">
              <span class="label">产品类型</span>
              <span class="value">软件</span>
            </div>
            <div class="info-row">
              <span class="label">产品小类</span>
              <span class="value">智能定点摄像机</span>
            </div>
            <div class="info-row">
              <span class="label">单价</span>
              <span class="value">80</span>
            </div>
            <div class="info-row">
              <span class="label">购买数量</span>
              <span class="value">1</span>
            </div>
            <div class="info-row">
              <span class="label">应付金额</span>
              <span class="value price">100</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户信息 -->
      <div class="customer-section">
        <div class="section-title">客户信息</div>
        <div class="customer-info">
          <div class="info-row">
            <span class="label">客户信息</span>
            <span class="value"></span>
          </div>
          <div class="info-row">
            <span class="label">客户名称</span>
            <span class="value">苏州万店掌网络科技有限公司</span>
          </div>
          <div class="info-row">
            <span class="label">门店名称</span>
            <span class="value">AI万店掌24小时未来无人店</span>
          </div>
          <div class="info-row">
            <span class="label">门店名称</span>
            <span class="value">范德堡</span>
          </div>
        </div>

        <div class="delivery-info">
          <div class="info-row">
            <span class="label">收货地址</span>
            <span class="value">范德堡</span>
          </div>
          <div class="info-row">
            <span class="label">收货人</span>
            <span class="value">范德堡</span>
          </div>
          <div class="info-row">
            <span class="label">联系方式</span>
            <span class="value">12561231432</span>
          </div>
          <div class="info-row">
            <span class="label">地址</span>
            <span class="value">苏州万店掌网络科技有限公司</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部区域 -->
    <div class="bottom-section">
      <div class="total-price">
        <span class="currency">¥</span>
        <span class="amount">800.00</span>
      </div>
      <van-button type="warning" class="confirm-btn" @click="confirmOrder">
        确认下单
      </van-button>
    </div>
  </div>
</template>

<script>
import { NavBar, Button, Icon } from "vant";

export default {
  name: "orderConfirm",
  components: {
    [NavBar.name]: NavBar,
    [Button.name]: Button,
    [Icon.name]: Icon
  },
  data() {
    return {
      // 这里可以添加订单数据
    };
  },
  methods: {
    onClickLeft() {
      // 返回上一页
      this.$router.go(-1);
    },
    confirmOrder() {
      // 确认下单逻辑
      console.log("确认下单");
      // 这里可以添加提交订单的API调用
    }
  }
};
</script>

<style scoped>
.order-confirm-page {
  height: 100vh;
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
}

/* 导航栏区域 */
.nav-header {
  flex-shrink: 0;
}

.content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
}

/* 区域标题样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 16px 0;
  padding: 0;
  background-color: transparent;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
  padding: 0;
  background-color: transparent;
}

/* 独立的section-title样式（不在header中的） */
.accessories-section .section-title,
.customer-section .section-title {
  margin: 0 0 16px 0;
  padding: 0;
  background-color: transparent;
}

/* 产品信息区域 */
.product-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
  display: flex;
  flex-direction: column;
}

.product-info {
  display: flex;
  flex-direction: column;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-row:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.label {
  font-size: 15px;
  color: #333;
  flex-shrink: 0;
  font-weight: 400;
}

.value {
  font-size: 15px;
  color: #333;
  text-align: right;
  font-weight: 400;
}

.value.price {
  color: #333;
  font-weight: 400;
}

/* 成功状态标签 */
.status-tag {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  flex-shrink: 0;
}

.status-tag.success {
  background-color: #e8f5e8;
  color: #52c41a;
}

.status-tag .van-icon {
  margin-right: 4px;
  font-size: 12px;
}

/* 配套产品列表 */
.accessories-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
  display: flex;
  flex-direction: column;
}

.product-item {
  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.product-item:not(:last-child) {
  margin-bottom: 24px;
}

.product-item:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

.product-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

/* 客户信息区域 */
.customer-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
  display: flex;
  flex-direction: column;
}

.customer-info {
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.delivery-info {
  display: flex;
  flex-direction: column;
}

/* 底部区域 */
.bottom-section {
  flex-shrink: 0;
  background: white;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); */
}

.total-price {
  display: flex;
  align-items: baseline;
}

.currency {
  font-size: 14px;
  color: #333;
  margin-right: 2px;
}

.amount {
  font-size: 20px;
  font-weight: 500;
  color: #333;
}

.confirm-btn {
  width: 120px;
  height: 40px;
  border-radius: 6px;
  font-size: 14px;
  background: #ff9900;
  border-color: #ff9900;
}

.confirm-btn:active {
  background-color: #ff9900;
  border-color: #ff9900;
}

/* 导航栏样式调整 */
.order-confirm-page /deep/ .van-nav-bar {
  background-color: white;
  border-bottom: 1px solid #eee;
}

.order-confirm-page /deep/ .van-nav-bar__title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
</style>
