<template>
  <div class="post_pone">
    <header>
      <img src="../../assets/img/ico_back-t.png" alt="" @click="onClickLeft" />
      <div class="title">延期服务</div>
      <img src="../../assets/img/ico_filter.png" alt="" @click="show = true" />
    </header>

    <van-pull-refresh
      v-model="refreshing"
      @refresh="onRefresh"
      class="main"
      v-if="result.length > 0"
    >
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div
          class="list"
          v-for="(item, index) in result"
          :key="index"
          @click="viewDetail(item)"
        >
          <div class="list-top">
            <div class="list-top-no">
              <span class="contract">{{ item.delayNo }}</span>
              <span
                :class="{
                  status: item.state == 1,
                  statusTwo: item.state == 2,
                  statusThree: item.state == 3,
                  statusFour: item.state == 4 || item.state == 5
                }"
                >{{ item.state | invoiceStatus(item.status) }}</span
              >
            </div>
          </div>
          <div class="list-bottom">
            <div class="item">
              <label for="">客户名称</label>
              <div class="content">{{ item.customerName }}</div>
            </div>
            <div class="item">
              <label for="">延期服务</label>
              <div class="content">{{ item.softwareName }}</div>
            </div>
            <div class="item">
              <label for="">延期时长</label>
              <div class="content">{{ item.delayDay }}</div>
            </div>
            <div class="item">
              <label for="">生效时间</label>
              <div class="content">{{ item.effectiveTime }}</div>
            </div>
            <div class="item">
              <label for="">失效时间</label>
              <div class="content">{{ item.ineffectiveTime }}</div>
            </div>
            <div class="item">
              <label for="">创建人</label>
              <div class="content">{{ item.createBy }}</div>
            </div>
            <div class="item">
              <label for="">生成时间</label>
              <div class="content">{{ item.createTime }}</div>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>

    <van-empty v-else description="暂无数据" />

    <!-- 筛选条件 -->
    <popupDreaw v-model="show" @sure="sure"></popupDreaw>

    <!-- 详情页 -->
    <popupDetail
      v-model="showDetail"
      :id="id"
      :auditShow="auditShow"
      :rejectShow="rejectShow"
    ></popupDetail>
  </div>
</template>
<script>
import { List, PullRefresh, Empty, Toast } from "vant";
import popupDreaw from "./components/popupDreaw";
import popupDetail from "./components/popupDetail";
// api
import {
  getDelayList,
  getCurrentUserAllPrivileges
} from "@/api/postponeService";
export default {
  /**注册组件*/
  components: {
    [List.name]: List,
    [PullRefresh.name]: PullRefresh,
    [Empty.name]: Empty,
    [Toast.name]: Toast,
    popupDreaw,
    popupDetail
  },
  data() {
    return {
      show: false,
      showDetail: false,
      loading: false,
      finished: false,
      refreshing: false,
      result: [],
      page: {
        no: 1,
        limit: 10
      },
      state: "", //状态
      delayNo: "", //延期编号
      customerName: "", //客户名称
      deptName: "", //门店名称
      auditShow: false, //审批权限
      rejectShow: false, //驳回权限
      id: ""
    };
  },

  mounted() {
    this.getDelayList();
    this.getCurrentUserAllPrivileges();
  },

  methods: {
    // 查询
    sure(data) {
      this.page.no = 1;
      this.state = data.state;
      this.delayNo = data.delayNo;
      this.customerName = data.customerName;
      this.deptName = data.deptName;
      this.getDelayList();
    },

    onLoad() {
      this.page.no++;
      this.getDelayList();
    },

    // 下拉刷新
    async onRefresh() {
      this.page.no = 1;
      this.state = "";
      this.delayNo = "";
      this.customerName = "";
      this.deptName = "";
      this.finished = false;
      await this.getDelayList();
      setTimeout(() => {
        this.refreshing = false; //下拉刷新loading 关闭
      }, 500);
    },

    // 查询延期列表
    async getDelayList() {
      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        duration: 0
      });
      try {
        const res = await getDelayList({
          ...this.page,
          state: this.state,
          delayNo: this.delayNo,
          customerName: this.customerName,
          deptName: this.deptName
        });
        let list = [];
        if (this.page.no == 1) {
          list = res?.records || [];
        } else {
          list = this.result.concat(res?.records || []);
        }
        this.result = list;
        this.loading = false;
        if (this.result.length >= res.total) {
          this.finished = true;
        }
        Toast.clear();
      } catch (error) {
        console.log(error);
      }
    },

    // 查询权限点
    async getCurrentUserAllPrivileges() {
      try {
        const res = await getCurrentUserAllPrivileges();
        // 判断是否有审批权限
        this.auditShow = res.some(
          item => item.privilegeName === "SERVICE_DELAY_AUDIT"
        );
        this.rejectShow = res.some(
          item => item.privilegeName === "SERVICE_DELAY_REJECT"
        );
        // console.log(this.auditShow, this.rejectShow);
      } catch (error) {
        console.log(error);
      }
    },

    // 查看详情
    viewDetail(item) {
      this.id = item.id;
      this.showDetail = true;
    },

    // 返回app
    onClickLeft() {
      this.backToApp();
    }
  },

  filters: {
    invoiceStatus(val) {
      const statusMap = {
        1: "草稿",
        2: "审批中",
        3: "已生效",
        4: "已失效",
        5: "驳回"
      };
      return statusMap[val] || "";
    }
  }
};
</script>
<style scoped lang="scss">
@import "./index.scss";
</style>
