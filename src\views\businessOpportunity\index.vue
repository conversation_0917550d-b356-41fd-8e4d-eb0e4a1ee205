<template>
  <div class="business-opportunity">
    <!-- 头部导航栏 -->
    <div class="nav-bar">
      <img src="../../assets/ico_back_r.png" alt="" @click="onClickLeft" />
      <div class="nav-bar-title">谍报</div>
      <div></div>
    </div>

    <!-- 标签页切换 -->
    <div class="custom-tabs">
      <div class="custom-tabs-nav">
        <div
          class="custom-tab"
          :class="{ active: active === 0 }"
          @click="switchTab(0)"
        >
          最新线报
          <span v-if="unreadCount > 0" class="unread-badge">{{
            unreadCount
          }}</span>
          <div class="tab-line" v-if="active === 0"></div>
        </div>
        <div
          class="custom-tab"
          :class="{ active: active === 1 }"
          @click="switchTab(1)"
        >
          线索快讯
          <div class="tab-line" v-if="active === 1"></div>
        </div>
      </div>

      <div class="custom-tabs-content">
        <div v-if="active === 0" class="custom-tab-pane">
          <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list
              v-model="loading"
              :finished="finished"
              finished-text=""
              :immediate-check="false"
              @load="onLoad"
            >
              <div class="news-list" v-if="newsList.length > 0">
                <div
                  class="news-item"
                  v-for="(item, index) in newsList"
                  :key="index"
                  @click="handleNewsItemClick(item)"
                >
                  <div class="news-date">
                    {{ item.time }}
                    <span
                      class="news-tag"
                      :class="getTagClass(index)"
                      v-if="item.title"
                      >{{ item.title }}</span
                    >
                  </div>
                  <div class="news-content">
                    <div class="news-title">{{ item.text }}</div>
                    <div class="news-footer">
                      <div class="news-time">{{ item.createTime }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <van-empty v-else description="暂无数据" />
            </van-list>
          </van-pull-refresh>
        </div>

        <div v-else-if="active === 1" class="custom-tab-pane">
          <van-pull-refresh
            v-model="refreshingExpress"
            @refresh="onRefreshExpress"
          >
            <van-list
              v-model="loadingExpress"
              :finished="finishedExpress"
              finished-text=""
              :immediate-check="false"
              @load="onLoadExpress"
            >
              <div class="news-list" v-if="expressNewsList.length > 0">
                <div
                  class="news-item"
                  v-for="(item, index) in expressNewsList"
                  :key="index"
                  @click="handleNewsItemClick(item)"
                >
                  <div class="news-date">
                    {{ item.createTime }}
                    <span
                      class="news-tag"
                      :class="getTagClass(index)"
                      v-if="item.title"
                      >{{ item.title }}</span
                    >
                  </div>
                  <div class="news-content">
                    <div class="news-title">{{ item.text }}</div>
                    <div class="news-footer">
                      <div class="news-time">{{ item.time }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <van-empty v-else description="暂无数据" />
            </van-list>
          </van-pull-refresh>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { List, PullRefresh, Empty, Toast } from "vant";
import {
  getUserInformationList,
  getAllInformationList,
  getUserInformationCount
} from "../../api/businessOpportunity";

export default {
  components: {
    [List.name]: List,
    [PullRefresh.name]: PullRefresh,
    [Empty.name]: Empty,
    [Toast.name]: Toast
  },
  data() {
    return {
      active: 0,
      loading: false,
      finished: false,
      refreshing: false,
      loadingExpress: false,
      finishedExpress: false,
      refreshingExpress: false,
      pageNum: 1,
      pageSize: 10,
      newsList: [],
      expressNewsList: [],
      colorIndex: 0, // 添加颜色索引计数器
      unreadCount: 0, // 添加未读数量
      platform: "" // 添加平台判断
    };
  },
  created() {
    this.getInfo();
  },

  mounted() {
    this.platform = this.getPlatform();
  },

  methods: {
    onClickLeft() {
      this.backToApp();
    },

    async getInfo() {
      await this.getUserInformationList();
      await this.getUserInformationCount();
    },

    // 当前用户谍报数据
    async getUserInformationList(isLoadMore = false) {
      try {
        const res = await getUserInformationList({
          no: this.pageNum,
          limit: this.pageSize
        });

        if (isLoadMore) {
          this.newsList = [...this.newsList, ...(res.records || [])];
        } else {
          this.newsList = res.records || [];
        }

        this.loading = false;
        this.refreshing = false;
        // 判断是否还有更多数据
        this.finished = !res.records || res.records.length < this.pageSize;

        console.log("res: ", res.records);
      } catch (error) {
        console.error("获取用户信息失败:", error);
        this.loading = false;
        this.refreshing = false;
        Toast.clear(); // 确保错误时也清除加载提示
      }
    },

    // 所有谍报数据
    async getAllInformationList(isLoadMore = false) {
      try {
        const res = await getAllInformationList({
          no: this.pageNum,
          limit: this.pageSize
        });

        if (isLoadMore) {
          this.expressNewsList = [
            ...this.expressNewsList,
            ...(res.records || [])
          ];
        } else {
          this.expressNewsList = res.records || [];
        }

        this.loadingExpress = false;
        this.refreshingExpress = false;
        // 判断是否还有更多数据
        this.finishedExpress =
          !res.records || res.records.length < this.pageSize;

        console.log("express res: ", res.records);
      } catch (error) {
        console.error("获取所有信息失败:", error);
        this.loadingExpress = false;
        this.refreshingExpress = false;
        Toast.clear(); // 确保错误时也清除加载提示
      }
    },

    // 当前用户未读谍报数据统计
    async getUserInformationCount() {
      try {
        const res = await getUserInformationCount();
        this.unreadCount = res || 0;
      } catch (error) {
        console.error("获取用户未读信息失败:", error);
      }
    },

    // 切换标签页
    switchTab(index) {
      if (this.active === index) return; // 如果点击当前激活的标签，则不执行任何操作

      this.active = index;
      this.pageNum = 1; // 切换标签页时重置页码
      
      // 显示加载提示
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        duration: 0
      });

      if (index === 0) {
        this.finished = false;
        this.loading = true; // 设置loading状态，防止自动触发加载更多
        this.getUserInformationList().then(() => {
          this.loading = false;
          Toast.clear(); // 清除加载提示
        }).catch(() => {
          Toast.clear(); // 确保出错时也清除加载提示
        });
      } else if (index === 1) {
        this.finishedExpress = false;
        this.loadingExpress = true; // 设置loading状态，防止自动触发加载更多
        this.getAllInformationList().then(() => {
          this.loadingExpress = false;
          Toast.clear(); // 清除加载提示
        }).catch(() => {
          Toast.clear(); // 确保出错时也清除加载提示
        });
      }
    },

    onLoad() {
      // 加载更多数据
      if (this.refreshing) {
        this.pageNum = 1;
        this.getUserInformationList();
      } else {
        this.pageNum += 1; // 增加页码
        this.getUserInformationList(true);
      }
    },

    onRefresh() {
      // 下拉刷新，重置页码
      this.finished = false;
      this.pageNum = 1;
      this.getUserInformationList();
    },

    onLoadExpress() {
      // 加载更多数据
      if (this.refreshingExpress) {
        this.pageNum = 1;
        this.getAllInformationList();
      } else {
        this.pageNum += 1; // 增加页码
        this.getAllInformationList(true);
      }
    },

    onRefreshExpress() {
      // 下拉刷新，重置页码
      this.finishedExpress = false;
      this.pageNum = 1;
      this.getAllInformationList();
    },

    // 判断当前平台
    getPlatform() {
      const u = navigator.userAgent;
      const isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1;
      const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
      return isiOS ? "ios" : isAndroid ? "android" : "other";
    },

    handleNewsItemClick(item) {
      if (this.platform == "ios") {
        let prefix = location.host;
        const ENVIRONMENT = prefix.indexOf("ovopark") != -1; //false 测试服 true 正式服
        let suffix = ENVIRONMENT ? ".ovopark.com" : ".wandianzhang.com";
        let url = encodeURIComponent(
          `http://tiantih5${suffix}/notice/businessDetail?id=${item.id}&cluingId=${item.cluingId}`
        );
        window.location.href = `ioswdzforappstore://push/web?type=ivan_view&page=WDZBusinessWebViewController&hiddenBar=1&urlSting=${url}`;
      } else {
        this.$router.push({
          name: "businessDetail",
          query: {
            id: item.id,
            cluingId: item.cluingId,
            type: "news"
          }
        });
      }
    },
    // 获取标签类
    getTagClass(index) {
      const tagClasses = [
        "orange-tag",
        "purple-tag",
        "blue-tag",
        "green-tag",
        "red-tag"
      ];
      return tagClasses[index % tagClasses.length];
    }
  }
};
</script>

<style lang="scss" scoped>
.business-opportunity {
  height: 100%;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .nav-bar {
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    background-color: #fff;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #000000;

    img {
      width: 20px;
      height: 20px;
    }
  }

  .custom-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .custom-tabs-nav {
      display: flex;
      background-color: #fff;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 1px;
        background-color: #e5e5e5;
        transform: scaleY(0.5);
      }

      .custom-tab {
        flex: 1;
        height: 44px;
        line-height: 44px;
        text-align: center;
        font-size: 15px;
        color: #666;
        position: relative;

        .unread-badge {
          position: absolute;
          top: 6px;
          right: 28%;
          min-width: 16px;
          height: 16px;
          line-height: 16px;
          text-align: center;
          background: #ee0a24;
          border-radius: 8px;
          color: #fff;
          font-size: 12px;
          padding: 0 2px;
          transform: translateX(50%);
        }

        &.active {
          font-weight: 600;
          color: #333;
        }

        .tab-line {
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 20px;
          height: 3px;
          background-color: #ff9900;
          border-radius: 3px;
        }
      }
    }

    .custom-tabs-content {
      flex: 1;
      background-color: #f5f5f5;
      overflow: auto;

      .custom-tab-pane {
        height: 100%;
      }
    }
  }

  .news-list {
    padding: 12px 16px;
  }

  .news-item {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .news-date {
      padding: 12px 16px;
      font-size: 14px;
      color: #333;
      background-color: #fff;
      font-weight: 500;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .news-tag {
        font-size: 12px;
        color: #576b95;
        background-color: rgba(87, 107, 149, 0.1);
        border-radius: 4px;
        padding: 2px 6px;
        white-space: nowrap;

        &.orange-tag {
          color: #ff9900;
          background-color: rgba(255, 153, 0, 0.1);
        }

        &.purple-tag {
          color: #6954ef;
          background-color: rgba(105, 84, 239, 0.1);
        }

        &.blue-tag {
          color: #1989fa;
          background-color: rgba(25, 137, 250, 0.1);
        }

        &.green-tag {
          color: #07c160;
          background-color: rgba(7, 193, 96, 0.1);
        }

        &.red-tag {
          color: #ee0a24;
          background-color: rgba(238, 10, 36, 0.1);
        }
      }
    }

    .news-content {
      padding: 12px 16px;
      background-color: #fff;
      position: relative;

      .news-title {
        font-size: 15px;
        color: #333;
        font-family: PingFangSC-Regular;
        line-height: 24px;
        margin-bottom: 10px;
        word-break: break-all;
        max-height: 204px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 8; // 204px ÷ 24px(行高) ≈ 8行
        -webkit-box-orient: vertical;
      }

      .news-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .news-time {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }

  ::v-deep .van-empty {
    padding: 32px 0;
  }

  ::v-deep .van-pull-refresh {
    min-height: 100%;
  }

  ::v-deep .van-list__finished-text {
    display: none;
  }

  ::v-deep .van-list__loading {
    display: none;
  }
}
</style>
