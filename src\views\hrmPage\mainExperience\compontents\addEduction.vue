<template>
  <div>
    <van-popup
      v-model="show"
      position="right"
      :style="{ height: '100%', width: '100%' }"
    >
      <div class="add-eduction">
        <!-- title -->
        <div class="title">
          <van-icon name="cross" @click="closePopup" />
          <span class="title-name">{{
            editData.id ? "编辑教育经历" : "教育经历"
          }}</span>
          <span v-if="!editData.id" class="save" @click="saveEducation"
            >保存</span
          >
          <span v-else></span>
        </div>
        <!-- main -->
        <main>
          <van-field
            class="must"
            readonly
            clickable
            name="picker"
            :value="educationType"
            label="学历类型"
            placeholder="点击选择学历类型"
            @click="clickEducationType"
          />
          <van-popup v-model="showEducationType" position="bottom">
            <van-picker
              show-toolbar
              :columns="educationTypeList"
              :default-index="educationTypeIndex"
              @confirm="onEducationType"
              @cancel="showEducationType = false"
            >
              <template #option="option">
                <div>{{ option.dname }}</div>
              </template>
            </van-picker>
          </van-popup>

          <!--  -->
          <van-field
            class="must"
            label="毕业院校"
            v-model="form.graduationSchool"
            placeholder="请输入"
          />

          <van-field
            label="专业"
            class="must"
            v-model="form.major"
            placeholder="请输入"
          />

          <van-field
            class="no-must"
            readonly
            clickable
            name="picker"
            :value="academicDegree"
            label="学位"
            placeholder="点击选择学位"
            @click="clickDegree"
          />
          <van-popup v-model="showDegree" position="bottom">
            <van-picker
              show-toolbar
              :columns="academicDegreeList"
              :default-index="degreeIndex"
              @confirm="onDegree"
              @cancel="showDegree = false"
            >
              <template #option="option">
                <div>{{ option.dname }}</div>
              </template>
            </van-picker>
          </van-popup>

          <van-field
            class="must"
            readonly
            clickable
            name="picker"
            :value="education"
            label="学历"
            placeholder="点击选择学历"
            @click="clickEducation"
          />
          <van-popup v-model="showEducation" position="bottom">
            <van-picker
              show-toolbar
              :columns="staffEducationList"
              :default-index="educationIndex"
              @confirm="onEducation"
              @cancel="showEducation = false"
            >
              <template #option="option">
                <div>{{ option.dname }}</div>
              </template>
            </van-picker>
          </van-popup>

          <!-- 在校期间 -->
          <div class="duringSchool">
            <label class="must">在校时间</label>
            <div class="time">
              <p
                :class="this.form.admissionDate ? 'txtTime' : ''"
                @click="clickAdmissionDate"
              >
                {{
                  this.form.admissionDate ? this.form.admissionDate : "开始日期"
                }}
              </p>
              <span>至</span>
              <p
                :class="this.form.graduationDate ? 'txtTime' : ''"
                @click="clickGraduationDate"
              >
                {{
                  this.form.graduationDate
                    ? this.form.graduationDate
                    : "结束日期"
                }}
              </p>
            </div>
          </div>

          <!-- 开始日期 -->
          <van-popup v-model="showAdmissionDate" position="bottom">
            <van-datetime-picker
              v-model="admissionDate"
              type="date"
              title="选择年月日"
              :min-date="minDate"
              :max-date="maxDate"
              @confirm="onAdmissionDate"
              @cancel="showAdmissionDate = false"
            />
          </van-popup>

          <!-- 结束日期 -->
          <van-popup v-model="showGraduationDate" position="bottom">
            <van-datetime-picker
              v-model="graduationDate"
              type="date"
              title="选择年月日"
              :min-date="minDate"
              :max-date="maxDate"
              @confirm="onGraduationDate"
              @cancel="showGraduationDate = false"
            />
          </van-popup>
        </main>

        <!-- footer -->
        <footer v-show="editData.id">
          <van-button type="primary" class="last" @click="deleteEducation"
            >删除</van-button
          >
          <van-button type="primary" class="next" @click="updateEducation"
            >完成</van-button
          >
        </footer>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  NavBar,
  Button,
  Divider,
  CellGroup,
  Field,
  Icon,
  Dialog,
  Picker,
  Popup,
  DatetimePicker,
  Toast
} from "vant";
// api
import {
  saveEducation,
  updateEducation,
  deleteEducation
} from "../../../../api/hrm";
export default {
  components: {
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Divider.name]: Divider,
    [CellGroup.name]: CellGroup,
    [Field.name]: Field,
    [Icon.name]: Icon,
    [Picker.name]: Picker,
    [Popup.name]: Popup,
    [Dialog.Component.name]: Dialog.Component,
    [DatetimePicker.name]: DatetimePicker
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    academicDegreeList: {
      type: Array
    },
    staffEducationList: {
      type: Array
    },
    educationTypeList: {
      type: Array
    },
    editData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      showEducationType: false, //学历类型
      showDegree: false, //学位类型
      showEducation: false, //学历

      educationType: "", //学历类型
      graduationSchool: "", //毕业院校
      education: "", //学历
      academicDegree: "", //学位
      major: "", //专业
      admissionDate: new Date(), //入学日期
      graduationDate: new Date(), //毕业日期

      form: {
        id: "",
        admissionDate: "",
        graduationDate: "",
        graduationSchool: "",
        education: "",
        major: "", //专业
        educationType: "", //学历类型
        academicDegree: "" //学位
      },

      showAdmissionDate: false, //入学选择框
      showGraduationDate: false, //毕业选择日期框
      minDate: new Date(1979, 0, 1),
      maxDate: new Date(2099, 10, 1),

      educationTypeIndex: "", //学历类型index
      educationIndex: "",
      degreeIndex: ""
    };
  },
  watch: {
    value(val) {
      this.show = val;
    },
    show(val) {
      console.log(this.$route.query.id);
      this.$emit("input", val);
      if (!val) {
        for (let i in this.form) {
          this.form[i] = "";
        }
        this.educationType = "";
        this.academicDegree = "";
        this.education = "";
      }
    },

    showEducationType(val) {
      if (!val) {
        this.educationTypeIndex = "";
      }
    },

    showEducation(val) {
      if (!val) {
        this.educationIndex = "";
      }
    },
    showDegree(val) {
      if (!val) {
        this.degreeIndex = "";
      }
    },

    editData(val) {
      // this.form = {...val};
      this.form.admissionDate = val.admissionDate;
      this.form.graduationDate = val.graduationDate;
      this.form.graduationSchool = val.graduationSchool;
      this.form.education = val.education;
      this.form.major = val.major; //专业
      this.form.educationType = val.educationType; //学历类型
      this.form.academicDegree = val.academicDegree; //学位
      this.form.id = val.id;
      // 用于显示的内容赋值
      let educationTypeTemp = this.educationTypeList.find((item) => {
        return item.value == val.educationType;
      });
      this.educationType = educationTypeTemp && educationTypeTemp.dname; //学历类型 取 dname 不取 value

      // 学位显示
      let academicDegreeTemp = this.academicDegreeList.find((item) => {
        return item.value == val.academicDegree;
      });
      this.academicDegree = academicDegreeTemp && academicDegreeTemp.dname;

      // 学历
      let educationTemp = this.staffEducationList.find((item) => {
        return item.value == val.education;
      });
      this.education = educationTemp && educationTemp.dname;
    }
  },
  methods: {
    closePopup() {
      this.show = false;
    },

    clickEducationType(val) {
      this.educationTypeIndex = this.educationTypeList.findIndex((item) => {
        return item.dname == val.target.value;
      });
      this.showEducationType = true;
    },

    // 点击学历类型
    onEducationType(val) {
      console.log(val);
      this.educationType = val.dname;
      this.form.educationType = val.value;
      this.showEducationType = false;
    },

    clickDegree(val) {
      this.degreeIndex = this.academicDegreeList.findIndex((item) => {
        return item.dname == val.target.value;
      });
      this.showDegree = true;
    },

    // 点击学位
    onDegree(val) {
      this.academicDegree = val.dname;
      this.form.academicDegree = val.value;
      this.showDegree = false;
    },

    clickEducation(val) {
      this.educationIndex = this.staffEducationList.findIndex((item) => {
        return item.dname == val.target.value;
      });
      this.showEducation = true;
    },

    // 点击学历
    onEducation(val) {
      this.education = val.dname;
      this.form.education = val.value;
      this.showEducation = false;
    },

    clickAdmissionDate(val) {
      this.showAdmissionDate = true;
      this.admissionDate = new Date(val.target.innerText);
    },

    // 点击选择在校开始日期
    onAdmissionDate(val) {
      let admissionDateTemp = this.$moment(val).format("YYYY-MM-DD");
      this.form.admissionDate = admissionDateTemp;
      this.showAdmissionDate = false;
    },

    clickGraduationDate(val) {
      this.showGraduationDate = true;
      this.graduationDate = new Date(val.target.innerText);
    },

    // 点击选择在校结束日期
    onGraduationDate(val) {
      let graduationDateTemp = this.$moment(val).format("YYYY-MM-DD");
      this.form.graduationDate = graduationDateTemp;
      this.showGraduationDate = false;
    },

    // 保存
    async saveEducation() {
      let checks = [
        { value: this.educationType, message: "请选择学位类型！" },
        { value: this.form.graduationSchool, message: "请填写毕业院校！" },
        { value: this.form.major, message: "请填写专业！" },
        { value: this.education, message: "请选择学历！" },
        {
          value: this.form.admissionDate && this.form.graduationDate,
          message: "请填写在校时间！"
        }
      ];

      let check = checks.find((check) => !check.value);
      if (check) {
        return Toast.fail({
          duration: 2000,
          message: check.message
        });
      }

      if (
        Date.parse(this.form.graduationDate) <
        Date.parse(this.form.admissionDate)
      ) {
        return Toast.fail({
          duration: 2000,
          message: "毕业日期不能小于入学日期!"
        });
      }

      let obj = {
        ...this.form,
        staffId: this.$route.query.id
      };
      try {
        const res = await saveEducation(obj);
        this.$emit("eductionUpdata");
        console.log(res);
        this.show = false;
      } catch (error) {
        console.log(error);
      }
    },

    // 更新
    async updateEducation() {
      let checks = [
        { value: this.educationType, message: "请选择学位类型！" },
        { value: this.form.graduationSchool, message: "请填写毕业院校！" },
        { value: this.form.major, message: "请填写专业！" },
        { value: this.education, message: "请选择学历！" },
        {
          value: this.form.admissionDate && this.form.graduationDate,
          message: "请填写在校时间！"
        }
      ];

      let check = checks.find((check) => !check.value);
      if (check) {
        return Toast.fail({
          duration: 2000,
          message: check.message
        });
      }

      if (
        Date.parse(this.form.graduationDate) <
        Date.parse(this.form.admissionDate)
      ) {
        return Toast.fail({
          duration: 2000,
          message: "毕业日期不能小于入学日期!"
        });
      }

      let obj = {
        staffId: this.$route.query.id,
        ...this.form
      };
      try {
        const res = await updateEducation(obj);
        console.log(res);
        this.$emit("eductionUpdata");
        this.show = false;
      } catch (error) {
        console.log(error);
      }
    },

    // 删除教育经历
    async deleteEducation() {
      try {
        const res = await deleteEducation({ id: this.editData.id });
        this.$emit("eductionUpdata");
        this.show = false;
        console.log(res);
      } catch (error) {
        console.log(error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.add-eduction {
  height: 100%;
  display: flex;
  flex-direction: column;

  .title {
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;

    .title-name {
      // width: 68px;
      height: 24px;
      font-family: PingFangSC-S0pxibold;
      font-weight: 600;
      font-size: 17px;
      color: #333333;
      text-align: center;
    }
    .save {
      // width: 28px;
      height: 20px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #7f7f7f;
    }
    .save:hover {
      // width: 28px;
      height: 20px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #ff9900;
    }
  }

  main {
    flex: 1;
  }
  .duringSchool {
    position: relative;
    height: 54px;
    display: flex;
    // justify-content: space-between;
    align-items: center;

    label {
      display: inline-block;
      // width: 95.8px;
      font-size: 3.7234vw;
      box-sizing: border-box;
      margin-right: 5.1vw;
      color: #646566;
      text-align: left;
      word-wrap: break-word;
      padding: 0 4.26667vw;
    }

    .time {
      flex: 1;
      display: flex;
      align-items: center;
      span {
        width: 41.5px;
        color: #646566;
      }

      p {
        width: 83.5px;
        color: #c8c9cc;
        font-size: 3.7234vw;
      }

      .txtTime {
        color: #333333;
      }
    }
  }
}

.duringSchool::after {
  position: absolute;
  box-sizing: border-box;
  content: " ";
  pointer-events: none;
  right: -0.73333vw;
  bottom: 0;
  left: 0.26667vw;
  border-bottom: 1px solid #ebedf0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}

footer {
  height: 56px;
  background: #ffffff;
  box-shadow: 0 0 0 0 #f0f3fa;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid #f0f3fa;

  .last {
    background-color: #f0f0f0;
    border: 1px solid #f0f0f0;
    width: 138px;
    height: 40px;
    color: #7f7f7f;
    font-weight: 500;
    font-size: 14px;
    text-align: center;
    border-radius: 21px;
  }
  .next {
    width: 197px;
    height: 40px;
    background: #ff9900;
    border-radius: 21px;
    border: 1px solid #ff9900;
  }
}
/deep/.van-cell {
  padding: 3.46667vw 4.26667vw;
}

.must {
  &::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
}

.no-must {
  &::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #fff;
  }
}
</style>