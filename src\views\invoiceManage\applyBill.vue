<template>
  <div class="bill" ref="bill">
    <van-nav-bar title="申请开票" left-arrow @click-left="onClickLeft" />
    <div class="apply-bill">
      <div class="contract-container">
        <div class="contract-number">
          合同编号：{{ contractInfo.contractNo }}
        </div>
        <div class="bonus">合同金额：</div>
        <div class="bonus-nums">
          <van-field
            v-model="contractInfo.price"
            name="price"
            class="priceInput"
          />
        </div>
      </div>
      <div class="bill-container">
        <van-form @submit="onSubmit" ref="billForm">
          <van-field
            v-model="invoiceType"
            label="发票类型"
            placeholder="请输入用户名"
            name="invoiceType"
            right-icon="arrow"
            @click="showPicker = true"
            readonly
          />
          <van-field
            v-model="invioceTaitou"
            label="发票抬头"
            placeholder="请选择"
            name="invioceTaitou"
            right-icon="arrow"
            @click="selectInvoice"
            readonly
          />
          <van-field name="invioceTitIdx" label="抬头类型">
            <template #input>
              <van-radio-group
                v-model="invioceTitIdx"
                direction="horizontal"
                disabled
              >
                <van-radio :name="3" icon-size="14px">个人</van-radio>
                <van-radio :name="2" icon-size="14px">企业</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field
            v-model="invioceInfo.customerTaxpayerAccount"
            label="税号"
            placeholder="请填写"
            name="customerTaxpayerAccount"
            required
            :rules="[
              { required: true, message: '税号不能为空', trigger: 'onBlur' }
            ]"
            readonly
          />
          <van-field
            v-model="invioceInfo.customerOpenBank"
            label="开户银行"
            placeholder="请填写(选填)"
            name="customerOpenBank"
            readonly
          />
          <van-field
            v-model="invioceInfo.customerOpenAccount"
            label="银行账号"
            placeholder="请填写(选填)"
            name="customerOpenAccount"
            readonly
          />
          <van-field
            v-model="invioceInfo.billingAddress"
            label="企业地址"
            placeholder="请填写(选填)"
            name="billingAddress"
            readonly
          />
          <van-field
            v-model="invioceInfo.email"
            label="邮箱地址"
            placeholder="请填写(必填)"
            name="email"
            class="email"
            :rules="[{ required: true, message: '请填写邮箱地址' }]"
          />
        </van-form>
      </div>
      <div class="service-container" v-if="hasInstallService">
        <div class="title">
          <span class="add-text">服务发生地</span>
          <div class="add-btn" @click="addService">
            <van-icon name="plus" />
            添加服务发生地
          </div>
        </div>
        <div
          class="project-container"
          v-for="item in serviceAreaData"
          :key="item.projectName"
          @click="editService(item)"
        >
          <van-swipe-cell>
            <div class="item">
              <span class="label">项目名称</span
              ><span class="value">{{ item.projectName }}</span>
            </div>
            <div class="item">
              <span class="label">建筑服务发生地</span
              ><span class="value">{{ item.location }}</span>
            </div>
            <div class="item">
              <span class="label">详细地址</span
              ><span class="value">{{ item.detailAddress }}</span>
            </div>
            <template #right>
              <van-button
                square
                text="删除"
                type="danger"
                class="delete-button"
                @click="deleteService(item)"
              />
            </template>
          </van-swipe-cell>
        </div>
      </div>
    </div>
    <div class="submit-btn">
      <van-button block round color="#FF9900" native-type="button" @click="send"
        >提交申请</van-button
      >
    </div>
    <van-popup
      v-model="showInvoiceTitlePopup"
      :safe-area-inset-bottom="true"
      round
      position="bottom"
      class="invoice-popup"
    >
      <div class="invoice-top">
        <span class="title">抬头选择</span>
        <img
          :src="require('../../assets/bill/<EMAIL>')"
          alt=""
          @click="showInvoiceTitlePopup = false"
        />
      </div>
      <div class="invoice-list-wrap">
        <div
          :class="['invoice-item', activeIndex === index && 'selected']"
          @click="choose(index)"
          v-for="(item, index) in taitouList"
          :key="item.id"
        >
          <div class="invoice-item-top">
            <div class="company">{{ item.billingName }}</div>
            <img
              :src="require('../../assets/bill/<EMAIL>')"
              alt=""
              @click="edit(item)"
            />
          </div>
          <div class="invoice-item-bottom">
            {{
              item.billingType == 0 || item.billingType == 2 ? "企业" : "个人"
            }}
          </div>
          <div class="choosedIcon" v-if="activeIndex === index">
            <img :src="require('../../assets/bill/<EMAIL>')" alt="" />
          </div>
        </div>
        <div class="add-invoice-btn" @click="addNewInvoiceTitle">
          <img :src="require('../../assets/bill/<EMAIL>')" alt="" />
          <span class="btn-text">添加新抬头</span>
        </div>
      </div>
    </van-popup>
    <van-popup v-model="showPicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="columns"
        @confirm="onConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>
    <van-popup v-model="showArea" position="bottom">
      <van-picker
        show-toolbar
        :columns="serviceColumns"
        @confirm="onServiceAreaConfirm"
        @cancel="showArea = false"
      />
    </van-popup>
    <van-popup
      v-model="showAddService"
      position="bottom"
      class="service-popup"
      round
    >
      <div class="service-top">
        <span class="title">建筑服务发生地</span>
        <img
          :src="require('../../assets/bill/<EMAIL>')"
          alt=""
          @click="showAddService = false"
        />
      </div>
      <div class="service-content">
        <van-form @submit="onServiceSubmit" ref="serviceForm">
          <van-field
            v-model="serviceInfo.projectName"
            label="项目名称"
            placeholder="请填写"
            name="projectName"
            required
            :rules="[
              { required: true, message: '请输入项目名称', trigger: 'onBlur' }
            ]"
          />
          <van-field
            v-model="serviceInfo.location"
            label="建筑服务发生地"
            placeholder="点击选择服务发生地"
            name="location"
            @click="showArea = true"
            required
            :rules="[
              {
                required: true,
                message: '请选择服务发生地',
                trigger: 'onChange'
              }
            ]"
          />
          <van-field
            v-model="serviceInfo.detailAddress"
            label="地址"
            placeholder="请输入详细地址"
            name="detailAddress"
            required
            :rules="[
              { required: true, message: '请输入地址', trigger: 'onBlur' }
            ]"
          />
          <div style="margin: 16px;">
            <van-button
              color="#FF9900"
              round
              block
              type="info"
              native-type="submit"
              >保存</van-button
            >
          </div>
        </van-form>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  NavBar,
  Field,
  Icon,
  Popup,
  RadioGroup,
  Radio,
  Button,
  Form,
  Toast,
  Picker,
  Area,
  SwipeCell
} from "vant";
import {
  getContract,
  saveBilling,
  getTaxAreaInfo,
  saveServicePlace,
  getServicePlace,
  deleteServicePlace
} from "../../api/bill";

export default {
  name: "applyBill",
  components: {
    [NavBar.name]: NavBar,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [Popup.name]: Popup,
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio,
    [Button.name]: Button,
    [Form.name]: Form,
    [Picker.name]: Picker,
    [Area.name]: Area,
    [SwipeCell.name]: SwipeCell,
    [Toast.name]: Toast
  },
  data() {
    return {
      contractInfo: {},
      invioceInfo: {},
      invoiceType: "数电票（普通发票）",
      show: false,
      invioceTitIdx: 1,
      invioceTaitou: "",
      customerTaxpayerAccount: "",
      customerOpenBank: "",
      customerOpenAccount: "",
      billingAddress: "",
      email: "",
      showInvoiceTitlePopup: false,
      activeIndex: 0,
      invioceModel: "0",
      basicInfoId: "",
      billingId: "",
      sliderValue: 100,
      invioceTitle: [],
      originInvoiceTitle: [],
      remarkExplain: "",
      remark: "",
      currentList: [],
      tableData: [], //当前展示的表格数据
      originData: [], //原始的金额，编辑时用到
      contractNos: "",
      originHeight: "",
      showPicker: false,
      columns: ["数电票（增值税专用发票）", "数电票（普通发票）"],
      serviceColumns: [],
      showAddService: false,
      showArea: false,
      serviceInfo: {
        projectName: "",
        location: "",
        detailAddress: ""
      },
      serviceAreaData: [],
      hasInstallService: false,
      serviceType: "add",
      serviceId: null
    };
  },
  computed: {
    numAll() {
      let all = 0;
      this.currentList.forEach(item => {
        if (item._checked) {
          all +=
            Number(item.hardwarePrice) +
            Number(item.installPrice) +
            Number(item.softwarePrice);
        }
      });
      return Number(all.toFixed(2));
    },
    digitalType() {
      return this.invoiceType === "数电票（普通发票）" ? "5" : "4";
    },
    taitouList() {
      if (this.invoiceType === "数电票（普通发票）") {
        return this.originInvoiceTitle.filter(
          item => item.billingType == 2 || item.billingType == 3
        );
      } else {
        return this.originInvoiceTitle.filter(item => item.billingType == 2);
      }
    }
  },
  watch: {
    tableData: {
      handler(val) {
        if (val) {
          const idx = Number(this.invioceModel);
          this.tableDataMap = this.originData[idx];

          this.tableData.forEach((item, index) => {
            if (this.billingId) {
              item._checked = item.selected === 1 ? true : false;
            } else {
              item._checked = true;
            }

            /**
             * 有status，为编辑状态，不用处理数据，
             */
            item.models.forEach(res => {
              if (this.status) {
                return;
              }
              const amount = res.usedAmount || res.remainAmount1;
              res.nums = Number((amount / res.salePrice).toFixed(2)) || 0;
              res.usedAmount = amount;
            });
            this.originData[idx][index] &&
              (this.originData[idx][index].selected = item.selected);
          });
          this.currentList = this.tableData.filter(item => {
            return item._checked;
          });
        }
      },
      deep: true
    }
  },
  created() {
    this.contactNos = this.$route.query.contactNos || "";
    this.billingId = this.$route.query.billingId || "";
    this.showInvoiceTitlePopup = this.$route.query.openInvoiceTitle || false;
    this.originHeight =
      document.documentElement.clientHeight || document.body.clientHeight;
  },
  mounted() {
    this.getContract();
    this.getTaxAreaInfo();
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    onClickLeft() {
      this.backToApp();
    },
    showPopup() {
      this.show = true;
    },
    async onSubmit() {
      if (this.hasInstallService && this.serviceAreaData.length === 0) {
        Toast.fail({
          duration: 2000,
          message: "请添加服务发生地！"
        });
        return;
      }
      const params = JSON.parse(JSON.stringify(this.invioceInfo));
      params.contractNos = this.contactNos || "";
      params.id = this.billingId || "";
      params.customerId = this.invioceInfo.basicInfoId;
      params.taxpayerNo = this.invioceInfo.customerTaxpayerAccount;
      params.bank = this.invioceInfo.customerOpenBank;
      params.bankAccount = this.invioceInfo.customerOpenAccount;
      params.billingType = this.digitalType;
      params.addType = 1;
      params.type = 0;
      params.receiverAddress = params.receiverAddress || "";

      delete params.createTime;
      delete params.createBy;
      delete params.updateTime;
      delete params.updateBy;
      delete params.percentage;

      params.remark = this.remark;
      params.remarkExplain = this.remarkExplain;
      params.allAmount = Number(this.contractInfo.price);
      params.percentage = this.sliderValue;

      params.contractProducts = this.getAllModels();
      params.serviceLocationList = this.serviceAreaData;

      try {
        const res = await saveBilling(params);
        if (res) Toast.success("申请成功！");
        this.$router.push({
          name: "invoiceManage"
        });
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.data || error.result
        });
      }
    },
    getAllModels() {
      const arr = [];
      this.currentList.forEach(item => {
        arr.push(...item.models);
      });
      arr.forEach(item => {
        item.nums = Number((item.usedAmount / item.salePrice).toFixed(2)) || 0;
      });
      return arr;
    },
    send() {
      this.$refs.billForm.submit();
    },
    selectInvoice() {
      this.showInvoiceTitlePopup = true;
    },
    choose(index) {
      this.activeIndex = index;
      this.invioceTaitou = this.taitouList[index]?.billingName;
      this.invioceInfo = this.taitouList[index] || {};
      this.invioceTitIdx = this.taitouList[index]?.billingType;
      this.showInvoiceTitlePopup = false;
    },
    addNewInvoiceTitle() {
      this.$router.push({
        name: "addBill",
        query: {
          basicId: this.basicInfoId,
          contractNos: this.contactNos,
          type: "create"
        }
      });
    },
    async getContract() {
      const params = {
        contractNos: this.contactNos
      };
      const res = await getContract(params);
      this.remarkExplain = res.remarkExplain || "";
      this.remark = res.remark || "";
      this.originData = [res.contractBilling, res.receiverBilling];
      if (res.contractBilling) {
        this.tableData = res.contractBilling;
        this.contractInfo = res.contractBilling[0];
        this.basicInfoId = res.contractBilling[0].customerId;
        this.judgeService(res.contractBilling);
      }

      this.sliderValue = res.percentage || 100;
      this.invioceTitle = res.bankBilling;
      this.originInvoiceTitle = res.bankBilling;

      if (localStorage.getItem("defaultBillName")) {
        const cached = localStorage.getItem("defaultBillName");
        let selectIndex = res.bankBilling.findIndex(
          item => item.billingName === cached
        );
        if (selectIndex == -1) {
          // 如果没找到，就默认选中第一个
          selectIndex = 0;
        }
        this.activeIndex = selectIndex;
        this.invioceInfo = res.bankBilling[selectIndex] || {};
        this.invioceTaitou = res.bankBilling[selectIndex]?.billingName;
        this.invioceTitIdx = res.bankBilling[selectIndex]?.billingType;
      } else {
        this.invioceInfo = res.bankBilling[0] || {};
        this.invioceTaitou = res.bankBilling[0]?.billingName;
        this.invioceTitIdx = res.bankBilling[0]?.billingType;
      }
    },
    async getTaxAreaInfo() {
      try {
        const res = await getTaxAreaInfo();
        this.serviceColumns = res?.map(item => item.address);
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.result || error.data
        });
      }
    },
    edit(item) {
      this.$router.push({
        name: "addBill",
        query: {
          billInfo: JSON.stringify(item),
          contractNos: this.contactNos,
          type: "edit"
        }
      });
    },
    isIOS() {
      const userAgent = navigator.userAgent;
      return /(iPhone|iPad|iPod|iOS|Mac|Mac)/i.test(userAgent);
    },
    handleResize() {
      const scrollElement = this.$refs.bill;
      const resizeHeight =
        document.documentElement.clientHeight || document.body.clientHeight;
      if (resizeHeight < this.originHeight) {
        // 键盘谈起
        if (!this.isIOS()) {
          // 是安卓机型
          scrollElement.setAttribute("style", "padding-bottom: 40px");
        }
      } else {
        // 键盘收起
        if (!this.isIOS()) {
          scrollElement.removeAttribute("style");
        }
      }
    },
    onConfirm(value) {
      this.invoiceType = value;
      this.showPicker = false;
      this.activeIndex = 0;
      this.invioceTaitou = this.taitouList[0]?.billingName;
      this.invioceInfo = this.taitouList[0] || {};
      this.invioceTitIdx = this.taitouList[0]?.billingType;
    },
    onServiceAreaConfirm(value) {
      this.serviceInfo.location = value;
      this.showArea = false;
    },
    addService() {
      this.showAddService = true;
      this.serviceType = "add";
      this.serviceInfo = {
        projectName: "",
        location: "",
        detailAddress: ""
      };
    },
    editService(row) {
      this.serviceType = "edit";
      this.serviceId = row.id;
      this.serviceInfo.projectName = row.projectName;
      this.serviceInfo.detailAddress = row.detailAddress;
      this.serviceInfo.location = row.location;
      this.showAddService = true;
    },
    async onServiceSubmit() {
      const params = {
        projectName: this.serviceInfo.projectName,
        location: this.serviceInfo.location,
        detailAddress: this.serviceInfo.detailAddress
      };
      if (this.serviceType === "edit") {
        params.id = this.serviceId;
      }
      try {
        const res = await saveServicePlace(params);
        if (res) {
          this.getServiceList(res);
        }
      } catch (error) {
        console.log(error);
      }
      this.showAddService = false;
    },
    async getServiceList(id) {
      try {
        const res = await getServicePlace({
          id
        });
        if (!res) return;
        if (this.serviceType === "add") {
          // 新增
          this.serviceAreaData.push({
            projectName: res.projectName,
            location: res.location,
            detailAddress: res.detailAddress,
            id: res.id
          });
        } else {
          // 编辑
          const index = this.serviceAreaData.findIndex(
            item => item.id === res.id
          );
          if (index > -1) {
            this.serviceAreaData.splice(index, 1, {
              projectName: res.projectName,
              location: res.location,
              detailAddress: res.detailAddress,
              id: res.id
            });
          }
        }
      } catch (error) {
        console.log(error);
      }
    },
    async deleteService(row) {
      try {
        await deleteServicePlace({
          id: row.id
        });
        Toast.success("删除成功");
        const index = this.serviceAreaData.findIndex(
          item => item.id === row.id
        );
        if (index > -1) {
          this.serviceAreaData.splice(index, 1);
        }
      } catch (error) {
        console.log(error);
      }
    },
    judgeService(res) {
      let hasInstallService = false;
      if (res && res.length) {
        res.forEach(item => {
          if (item.models && item.models.length) {
            item.models.forEach(it => {
              if (it.invoiceProductName === "安装服务" && it.nums > 0) {
                hasInstallService = true;
              }
            });
          }
        });
      }
      this.hasInstallService = hasInstallService;
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .van-nav-bar .van-icon {
  color: #333;
  font-size: 18px;
}

/deep/ .van-popup__close-icon {
  img {
    width: 16px;
    height: 16px;
  }
}
.delete-button {
  height: 100%;
}
.bill {
  max-height: 700px;
  overflow: auto;
}
.apply-bill {
  padding: 12px 16px;
  .contract-container {
    background: #fff;
    border-radius: 6px;
    padding: 16px;
    .contract-number {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
      line-height: 22px;
      margin-bottom: 16px;
    }
    .bonus {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #7f7f7f;
      line-height: 20px;
      margin-bottom: 4px;
    }
    .bonus-nums {
      .priceInput {
        font-family: PingFangSC-SNaNpxibold;
        font-weight: 600;
        font-size: 24px;
        color: #000000;
        line-height: 32px;
        padding: 0;
      }
    }
  }
  .bill-container {
    border-radius: 6px;
    margin-top: 12px;
  }
  .service-container {
    margin-top: 12px;
    border-radius: 6px;
    padding: 16px;
    background-color: #fff;
    margin-bottom: 60px;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .add-text {
        font-size: 14px;
        color: #000000;
      }
      .add-btn {
        font-size: 14px;
        color: #165dff;
        cursor: pointer;
        .van-icon-plus {
          vertical-align: text-bottom;
        }
      }
    }
    .project-container {
      border-bottom: 1px solid #ebedf0;
      padding: 8px 0;
      &:last-child {
        border-bottom: none;
      }
      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: 21px;
        color: #646566;
        font-size: 14px;
      }
    }
  }
}
.submit-btn {
  background: #fff;
  position: fixed;
  bottom: 0;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 16px;
  box-sizing: border-box;
}
.invoice-popup {
  .invoice-top {
    padding: 16px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    .title {
      font-family: PingFangSC-Medium;
      font-weight: 700;
      font-size: 14px;
      color: #000000;
    }
    img {
      width: 16px;
      height: 16px;
      position: absolute;
      right: 16px;
    }
  }
  .invoice-list-wrap {
    padding: 8px 16px;
    background: #fff;
    .invoice-item {
      padding: 12px 16px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      margin-bottom: 8px;
      position: relative;
      .invoice-item-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .company {
          font-family: PingFangSC-Medium;
          font-weight: bold;
          font-size: 14px;
          color: #000000;
        }
        img {
          width: 20px;
          height: 20px;
        }
      }
      .invoice-item-bottom {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #7f7f7f;
        line-height: 20px;
        margin-top: 4px;
      }
      &.selected {
        border: 1px solid #ff9900;
        .choosedIcon {
          width: 24px;
          height: 24px;
          border-radius: 6px 0 6px 0;
          background: #ff9900;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          right: -1px;
          bottom: -1px;
          img {
            width: 16px;
            height: 16px;
          }
        }
      }
    }
    .add-invoice-btn {
      padding: 10px 0;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
      .btn-text {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
      }
    }
  }
}
.service-popup {
  .service-top {
    padding: 16px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    .title {
      font-family: PingFangSC-Medium;
      font-weight: 700;
      font-size: 14px;
      color: #000000;
    }
    img {
      width: 16px;
      height: 16px;
      position: absolute;
      right: 16px;
    }
  }
  .service-content {
    background: #fff;
  }
}
</style>
