<template>
  <div class="related-running-water">
    <van-sticky>
      <van-nav-bar
        title="关联流水"
        left-arrow
        @click-left="onClickLeft"
        :border="false"
      />
      <div class="water-top">
        <div class="title">{{ paymentDetail.oppositeAccountName }}</div>
        <div class="water-bonus">
          <span class="desc"
            >可被关联最大金额<span class="nums">{{
              paymentDetail.notRelatedAmount
            }}</span></span
          >
          <span class="desc"
            >本次关联金额<span class="nums">{{
              currentRelatedMoney
            }}</span></span
          >
        </div>
        <div class="arrow-box" v-if="fold">
          <img
            src="../../assets/<EMAIL>"
            alt=""
            class="down"
            @click="expand"
          />
        </div>

        <div class="hidden-box" v-if="!fold">
          <div class="line"></div>
          <div class="item">
            <span class="label">客户名称</span>
            <span class="value">{{ paymentDetail.customerName }}</span>
          </div>
          <div class="item">
            <span class="label">对方账号</span>
            <span class="value">{{ paymentDetail.oppositeBankAccount }}</span>
          </div>
          <div class="item">
            <span class="label">回款流水号</span>
            <span class="value">{{ paymentDetail.seqNo }}</span>
          </div>
          <div class="arrow-box" v-if="!fold">
            <img
              src="../../assets/<EMAIL>"
              alt=""
              @click="expand"
              class="up"
            />
          </div>
        </div>
        <div class="tabs">
          <van-tabs
            v-model="activeName"
            color="#FF9900"
            title-active-color="#FF9900"
            line-height="2px"
          >
            <van-tab title="关联合同" name="1"></van-tab>
            <van-tab title="已关联" name="2"></van-tab>
          </van-tabs>
        </div>
      </div>
    </van-sticky>
    <div class="running-water-content">
      <div class="search" v-if="activeName == '1'">
        <van-search
          placeholder="搜索"
          v-model="searchVal"
          @search="handleSearch"
          class="search-box"
        >
        </van-search>
        <van-icon
          name="https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/07/09/<EMAIL>"
          size="20"
          @click="openFilter"
        />
      </div>
      <div class="related-contract" v-if="activeName == '1'">
        <div class="title">待关联合同</div>
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="getRunningWaterList"
          >
            <div
              class="water-box"
              v-for="(item, index) in runningWaterList"
              :key="item.id"
            >
              <div class="top">
                <div class="water-left">
                  <van-checkbox
                    v-model="item.checked"
                    icon-size="16px"
                    checked-color="#FF9900"
                    :disabled="item.paymentStatus == '3'"
                  ></van-checkbox>
                  <div class="customer-info">
                    <p class="customer-name">{{ item.customerName }}</p>
                    <p class="contract-no">合同编号：{{ item.contractNo }}</p>
                  </div>
                </div>
                <div class="water-right">
                  <van-button
                    class="change"
                    @click="handleChangeMoney(item, index)"
                  >
                    调整金额
                  </van-button>
                </div>
              </div>
              <div class="content">
                <div class="item">
                  <span class="label">门店名称</span>
                  <span class="value">{{ item.customerRemark || "-" }}</span>
                </div>
                <div class="item">
                  <span class="label">回款状态</span>
                  <span class="value">{{
                    item.paymentStatus == "1"
                      ? "未回款"
                      : item.paymentStatus == "2"
                      ? "回款中"
                      : "已回款"
                  }}</span>
                </div>
                <div class="item">
                  <span class="label">总额/回款金额/欠款金额</span>
                  <span class="value"
                    >{{ item.price }}/{{ item.paymentAmount }}/{{
                      item.arrearsAmount
                    }}</span
                  >
                </div>
                <div class="item">
                  <span class="label">销售备注</span>
                  <span class="value">{{ item.projectRemark }}</span>
                </div>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
      <div class="hasRelated" v-if="activeName == '2'">
        <div class="title">已关联合同</div>
        <van-pull-refresh v-model="rlFreshing" @refresh="onRlfresh">
          <van-list
            v-model="rlLoading"
            :finished="rlFinished"
            finished-text="没有更多了"
            :immediate-check="false"
            @load="getRelatedList"
          >
            <div
              class="water-box"
              v-for="item in hasRelatedList"
              :key="item.id"
            >
              <div class="top">
                <span class="contract">{{ item.contractNo }}</span>
                <span
                  class="revert"
                  @click="handleRevert(item)"
                  :class="{ grey: item.approvalStatus == '1' }"
                  >撤销</span
                >
              </div>
              <div class="has-related-content">
                <div class="item">
                  <span class="label">待审批</span>
                  <span class="value">{{ item.payMoney }}</span>
                </div>
                <div class="item">
                  <span class="label">审批状态</span>
                  <span class="value">{{
                    item.approvalStatus == 0
                      ? "待审批"
                      : item.approvalStatus == "1"
                      ? "通过"
                      : "驳回"
                  }}</span>
                </div>
                <div class="item">
                  <span class="label">关联人</span>
                  <span class="value">{{ item.relatedName }}</span>
                </div>
                <div class="item">
                  <span class="label">关联时间</span>
                  <span class="value">{{ item.relatedTime }}</span>
                </div>
              </div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
    <div class="footer">
      <van-button
        class="submit"
        @click="handleSubmit"
        :disabled="checkedIdList.length == 0"
        >提交</van-button
      >
    </div>
    <filter-dialog
      v-model="showFilter"
      ref="filter"
      @filter="handleFilter"
      type="contract"
    />
    <van-popup v-model="showChangeMoneyPopup" position="bottom" round>
      <div class="dialog-top">
        <span class="title">调整金额</span>
        <img
          src="../../assets/<EMAIL>"
          alt=""
          class="close"
          @click="showChangeMoneyPopup = false"
        />
      </div>
      <div class="form-item">
        <div class="form-item-name">
          <label class="label">硬件设备回款金额(必填)</label>
        </div>
        <div class="form-item-content">
          <van-field
            v-model="hardPrice"
            type="number"
            class="field"
            placeholder="请输入"
          ></van-field>
        </div>
        <div class="tips">
          硬件设备金额 {{ productInfo.priceHard || 0 }} 元，剩余
          <span class="nums">{{ productInfo.arrearsPriceHard || 0 }}</span>
          元未回款
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-name">
          <label class="label">软件服务回款金额(必填)</label>
        </div>
        <div class="form-item-content">
          <van-field
            v-model="softPrice"
            type="number"
            class="field"
            placeholder="请输入"
          ></van-field>
        </div>
        <div class="tips">
          软件服务金额 {{ productInfo.pricePlatform || 0 }} 元，剩余
          <span class="nums">{{ productInfo.arrearsPricePlatform || 0 }}</span>
          元未回款
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-name">
          <label class="label">安装施工回款金额(必填)</label>
        </div>
        <div class="form-item-content">
          <van-field
            v-model="instancPrice"
            type="number"
            class="field"
            placeholder="请输入"
          ></van-field>
        </div>
        <div class="tips">
          安装施工 {{ productInfo.priceConstruction || 0 }} 元，剩余
          <span class="nums">{{
            productInfo.arrearsPriceConstruction || 0
          }}</span>
          元未回款
        </div>
      </div>
      <div class="operate-btn">
        <van-button class="reset btn" @click="handleReset">重置</van-button>
        <van-button class="refund btn" @click="handleRefund"
          >全额回款</van-button
        >
        <van-button class="makesure btn" @click="handleSave">确认</van-button>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  NavBar,
  Button,
  Toast,
  Tab,
  Tabs,
  Sticky,
  Search,
  Icon,
  Checkbox,
  List,
  PullRefresh,
  Popup,
  Field
} from "vant";
import filterDialog from "../../components/filter.vue";
import {
  paymentAdvancedQuery,
  getPaymentInfoByContractId,
  relatedContract,
  getPayBackApproveListBySerial,
  approvePayment,
  getPaymentDetail
} from "../../api/water";
export default {
  components: {
    [NavBar.name]: NavBar,
    [Button.name]: Button,
    [Toast.name]: Toast,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
    [Sticky.name]: Sticky,
    [Search.name]: Search,
    [Icon.name]: Icon,
    [Checkbox.name]: Checkbox,
    [List.name]: List,
    [PullRefresh.name]: PullRefresh,
    [Popup.name]: Popup,
    [Field.name]: Field,
    filterDialog
  },
  data() {
    return {
      fold: true,
      activeName: "1",
      searchVal: "",
      refreshing: false,
      finished: false,
      page: {
        no: 1,
        limit: 20
      },
      total: 0,
      loading: false,
      runningWaterList: [],
      hasRelatedList: [],
      showChangeMoneyPopup: false,
      showFilter: false,
      hardPrice: 0,
      softPrice: 0,
      instancPrice: 0,
      rlLoading: false,
      rlFinished: false,
      rlFreshing: false,
      rlTotal: 0,
      rowId: null,
      customerId: null,
      productInfo: {},
      currentContractNo: "",
      currentChangeIndex: 0,
      paymentDetail: {}
    };
  },
  computed: {
    checkedIdList() {
      return this.runningWaterList
        .filter(item => item.checked)
        .map(item => item.id);
    },
    currentRelatedMoney() {
      return this.runningWaterList
        .filter(item => item.checked)
        .reduce((prev, cur) => {
          return (
            prev +
            cur.arrearsPriceHard +
            cur.arrearsPricePlatform +
            cur.arrearsPriceConstruction
          );
        }, 0);
    }
  },
  watch: {
    activeName(val) {
      if (val == "2") {
        this.page.no = 1;
        this.hasRelatedList = [];
        this.getRelatedList();
      }
    }
  },
  created() {
    this.rowId = this.$route.query.id;
    this.customerId = this.$route.query.customerId;
  },
  mounted() {
    this.getPaymentDetail();
  },
  methods: {
    async getPaymentDetail() {
      try {
        const res = await getPaymentDetail({
          id: this.rowId
        });
        this.paymentDetail = res;
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.data || error.result
        });
      }
    },
    onClickLeft() {
      this.$router.go(-1);
    },
    expand() {
      this.fold = !this.fold;
    },
    handleSearch() {
      this.page.no = 1;
      this.runningWaterList = [];
      this.getRunningWaterList();
    },
    async getRunningWaterList() {
      const params = this.$refs.filter.getComponentParams();
      const { keyWord, ...rest } = params;
      if (this.refreshing) {
        this.runningWaterList = [];
        this.refreshing = false;
      }
      try {
        const res = await paymentAdvancedQuery({
          customerId: this.customerId,
          keyWord: this.searchVal || keyWord,
          ...this.page,
          ...rest
        });
        this.total = res.total || 0;
        this.runningWaterList = this.runningWaterList.concat(res.records || []);
        this.loading = false;
        if (this.runningWaterList.length >= this.total) {
          this.finished = true;
        } else {
          this.page.no++;
        }
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.data || error.result
        });
      }
    },
    async getRelatedList() {
      if (this.rlFreshing) {
        this.hasRelatedList = [];
        this.rlFreshing = false;
      }
      try {
        const res = await getPayBackApproveListBySerial({
          ...this.page,
          id: Number(this.rowId)
        });
        this.hasRelatedList = this.hasRelatedList.concat(res.records || []);
        this.rlTotal = res.total || 0;
        this.rlLoading = false;
        if (this.hasRelatedList.length >= this.rlTotal) {
          this.rlFinished = true;
        } else {
          this.page.no++;
        }
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.data || error.result
        });
      }
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.page.no = 1;
      this.getRunningWaterList();
    },
    onRlfresh() {
      // 清空列表数据
      this.rlFinished = false;
      // 将 loading 设置为 true，表示处于加载状态
      this.rlLoading = true;
      this.page.no = 1;
      this.hasRelatedList = [];
      this.getRelatedList();
    },
    async handleChangeMoney(item, index) {
      try {
        const res = await getPaymentInfoByContractId({
          contractNo: item.contractNo
        });
        this.productInfo = res;
        this.currentContractNo = item.contractNo;
        this.currentChangeIndex = index;
        // 重置金额
        this.handleReset();
        this.showChangeMoneyPopup = true;
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.data || error.result
        });
      }
    },
    async handleSubmit() {
      const paymentDetailList = this.runningWaterList
        .filter(item => item.checked)
        ?.map(it => ({
          contractNo: it.contractNo,
          hardwareMoney: it.arrearsPriceHard,
          softwareMoney: it.arrearsPricePlatform,
          constructionMoney: it.arrearsPriceConstruction,
          serialId: Number(this.rowId),
          payTime: this.$moment().format("YYYY-MM-DD")
        }));
      const params = {
        paymentDetailList
      };
      try {
        await relatedContract(params);
        Toast.success({
          duration: 2000,
          message: "关联成功"
        });
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.data || error.result
        });
      }
    },
    openFilter() {
      this.showFilter = true;
    },
    handleFilter() {
      this.page.no = 1;
      this.runningWaterList = [];
      this.getRunningWaterList();
    },
    handleReset() {
      this.hardPrice = 0;
      this.softPrice = 0;
      this.instancPrice = 0;
    },
    handleRefund() {
      this.hardPrice = this.productInfo.arrearsPriceHard;
      this.softPrice = this.productInfo.arrearsPricePlatform;
      this.instancPrice = this.productInfo.arrearsPriceConstruction;
    },
    handleSave() {
      const sum =
        Number(this.hardPrice) +
        Number(this.softPrice) +
        Number(this.instancPrice);
      const sum1 =
        Number(this.productInfo.arrearsPriceHard) +
        Number(this.productInfo.arrearsPricePlatform) +
        Number(this.productInfo.arrearsPriceConstruction);
      if (sum == 0) {
        Toast.fail({
          duration: 2000,
          message: "关联金额总和不能为0，请检查"
        });
        return;
      }
      if (sum > sum1) {
        Toast.fail({
          duration: 2000,
          message: "关联金额总和超过最大可关联金额，请检查"
        });
        return;
      }
      this.runningWaterList[this.currentChangeIndex].arrearsPriceHard = Number(
        this.hardPrice
      );
      this.runningWaterList[
        this.currentChangeIndex
      ].arrearsPricePlatform = Number(this.softPrice);
      this.runningWaterList[
        this.currentChangeIndex
      ].arrearsPriceConstruction = Number(this.instancPrice);
      this.showChangeMoneyPopup = false;
    },
    async handleRevert(item) {
      // 调用后端接口取消关联
      try {
        await approvePayment({
          approvalStatus: 2,
          contractPayIds: [item.contractPayId]
        });
        Toast.success({
          duration: 2000,
          message: "撤销成功"
        });
        this.hasRelatedList = [];
        this.getRelatedList();
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.data || error.result
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
