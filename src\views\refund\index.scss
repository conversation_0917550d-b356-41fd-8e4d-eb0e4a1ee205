.refund-list {
  background-color: #fff;
  // .refund {

  // }
  .sticky {
    padding: 16px;
    background: #fafafa;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .date-box {
      display: flex;
      align-items: center;
      .date {
        font-size: 16px;
        color: #000000;
      }
      .arrow {
        width: 16px;
        height: 16px;
        margin-left: 4px;
      }
    }
    .text {
      font-size: 12px;
      color: #7f7f7f;
      line-height: 16px;
      margin-bottom: 4px;
    }
    .total {
      font-size: 16px;
      color: #333333;
      font-weight: bold;
      text-align: right;
    }
  }
  .refund-box {
    background-color: #fff;
    .record {
      padding: 16px;
      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        .time-wrap {
          font-size: 14px;
          color: #7f7f7f;
          line-height: 22px;
          .time {
            margin-left: 8px;
          }
        }
        .relate {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 22px;
          background: rgba(255, 153, 0, 0.06);
          border-radius: 11px;
          font-size: 12px;
          color: #ff9900;
          padding: 0;
        }
      }
      .content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        .left {
          display: flex;
          .alipay,
          .wechat {
            width: 24px;
            height: 24px;
          }
          .remark {
            font-size: 16px;
            color: #000000;
            font-weight: 700;
            margin-left: 12px;
          }
        }

        .bonus {
          font-size: 16px;
          color: #000000;
          font-weight: 700;
        }
      }
      .bottom {
        font-size: 13px;
        color: #7f7f7f;
        line-height: 20px;
        margin-left: 36px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
      }
      .line {
        width: 343px;
        height: 0.5px;
        background: #ededed;
        margin: 0 auto;
      }
    }
  }
  // .empty-date-box {
  //   padding: 4.26667vw;
  //   background: #fafafa;
  //   display: flex;
  //   align-items: center;
  //   .date {
  //     font-size: 16px;
  //     color: #000000;
  //   }
  //   .arrow {
  //     width: 16px;
  //     height: 16px;
  //     margin-left: 4px;
  //   }
  // }
}
