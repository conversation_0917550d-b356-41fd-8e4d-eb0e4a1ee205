<template>
  <div class="item-input">
    <div class="label">
      <span>{{ label }}</span>
      <span class="must" v-if="must">*</span>
    </div>
    <div class="input">
      <input
        :id="focusInput"
        :type="type"
        :placeholder="placeholder"
        class="w-input"
        :value="value"
        @input="handleinput"
        @focus="handleFocus"
        @blur="handleBlur"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    label: {
      type: String,
      default: "姓名"
    },
    placeholder: {
      type: String,
      default: "请输入"
    },
    must: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: "text" //默认为文本输入框
    },
    value: {
      type: String,
      default: ""
    },
    focusInput:{
      type: String,
    }
  },
  methods: {
    handleinput(event) {
      //父组件在绑定v-model时，同时绑定input事件
      this.$emit("input", event.target.value);
    },

    handleFocus(event) {
      this.$emit("focus", event.target.value);
    },

    handleBlur(event) {
      this.$emit("blur", event.target.value);
    },

    focus() {
      let element = document.getElementById(this.focusInput); // 获取到指定标签
      element.focus(); // 重新聚焦输入框
    }
  }
};
</script>

<style lang="scss" scoped>
.item-input {
  min-width: 0;
  display: flex;
  align-items: center;
  height: 52px;
  border-bottom: 1px solid #f0f3fa;

  .label {
    width: 124px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    display: flex;
    align-items: center;

    .must {
      color: red;
      margin-top: 5px;
      margin-left: 4px;
    }
  }

  .w-input {
    flex: 1;
    font-size: 14px;
    min-width: 0;
    height: 52px;
    border: 0; /*清除自带的2px的边框*/
    padding: 0; /*清除自带的padding间距*/
    outline: none; /*清除input点击之后的黑色边框*/
  }

  input::-webkit-input-placeholder {
    /* WebKit browsers，webkit内核浏览器 */
    color: #a1a1a1;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #b2b2b2;
    letter-spacing: 0;
  }
}
</style>