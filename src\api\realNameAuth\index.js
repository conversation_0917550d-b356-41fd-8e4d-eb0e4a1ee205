import urls from "./config";
import { get, postJson } from "../request";

//获取验证码
export function getVerifyCode(params) {
  return get(urls.getVerifyCode, params);
}

//验证验证码
export function checkVerifyCode(params) {
  return get(urls.checkVerifyCode, params);
}

//微信登录
export function createLinkmanUserForWx(params) {
  return postJson(urls.createLinkmanUserForWx, params);
}

// 根据openId查询用户是否绑定手机号
export function getUserByOpenId(params) {
  return get(urls.getUserByOpenId, params);
}

// 查询当前用户是否已经实名认证通过（仅登录查询）
export function getUserRealName(params) {
  return get(urls.getUserRealName, params);
}

//获取ocr信息
export function getOcrInfo(params) {
  return get(urls.getOcrInfo, params);
}

//创建/编辑用户接口（新增/编辑企业认证下一步时调用）
export function createBestSignAccount(params) {
  return postJson(urls.createBestSignAccount, params);
}

//企业认证确认提交接口
export function subitEntCertification(params) { 
  return get(urls.subitEntCertification, params);
}

//实名认证获取验证码接口
export function sendVcode(params) { 
  return get(urls.sendVcode, params);
}

//验证验证码
export function verifyVcode(params) { 
  return get(urls.verifyVcode, params);
}

//下载认证pdf
export function downloadAuthPdf(params) { 
  return postJson(urls.downloadAuthPdf, params);
}

// 授权书申请
export function applyReview(params) { 
  return get(urls.applyReview, params);
}

export function getBusinessInfoByKeyword(params) {
  return get(urls.getBusinessInfoByKeyword, params);
}

export function getBusinessInfoByKeywordForYongYou(params) {
  return get(urls.getBusinessInfoByKeywordForYongYou, params);
}
