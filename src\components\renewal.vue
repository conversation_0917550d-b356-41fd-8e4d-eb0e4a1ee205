<template>
  <div class="renewal-container">
    <van-nav-bar
      left-arrow
      @click-left="onClickLeft"
      :title="navName"
    ></van-nav-bar>
    <div class="renewal-search">
      <van-sticky>
        <div class="sticky-box">
          <div class="date-wrap" @click="showDatePopup = true">
            <span class="date">{{ $t(activeDate.text) }}</span>
            <img src="../assets/rect_fill.png" alt="" class="rect" />
          </div>
          <div class="area-wrap" @click="handleAreaChange">
            <span class="area" :class="{ active: storageValue }">{{
              storageValue || $t("region")
            }}</span>
            <img
              src="../assets/rect_fill.png"
              alt=""
              class="rect"
              v-if="storageValue"
            />
            <img src="../assets/rect.png" alt="" class="rect" v-else />
          </div>
        </div>
      </van-sticky>
    </div>
    <div class="renewval-content">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model="loading"
          :finished="finished"
          :finished-text="$t('noMore')"
          @load="getShopList"
          :offset="50"
          :immediate-check="false"
          :error.sync="error"
          :error-text="$t('rsquestFail')"
        >
          <div
            class="shop-item"
            v-for="item in shops"
            :key="item.id"
            @click="onCheckedChange(item)"
          >
            <van-checkbox
              v-model="item.checked"
              checked-color="#FF9900"
            ></van-checkbox>
            <div class="shop-box">
              <div class="title">{{ item.deptName || item.customerName }}</div>
              <div class="expried">
                <!-- XX天过期 简体中文 -->
                <span
                  class="tag"
                  v-if="
                    item.expiredDay &&
                      item.expiredDay != '已过期' &&
                      ident == 'zh-CN'
                  "
                  >{{ item.expiredDay }} 天过期</span
                >
                <!-- 繁体 -->
                <span
                  class="tag"
                  v-if="
                    item.expiredDay &&
                      item.expiredDay != '已过期' &&
                      ident == 'zh-TW'
                  "
                  >{{ item.expiredDay }} 天過期</span
                >
                <!-- 英文 -->
                <span
                  class="tag"
                  v-if="
                    item.expiredDay &&
                      item.expiredDay != '已过期' &&
                      ident == 'en-US'
                  "
                  >Expires in {{ item.expiredDay }}d</span
                >
                <!-- 印尼 -->
                <span
                  class="tag"
                  v-if="
                    item.expiredDay &&
                      item.expiredDay != '已过期' &&
                      ident == 'id-ID'
                  "
                  >Expired in {{ item.expiredDay }}days</span
                >
                <span
                  class="tag expired-tag"
                  v-if="item.expiredDay && item.expiredDay == '已过期'"
                  >{{ $t("expired") }}</span
                >
                <span>{{ $t("expiryDate") }}：{{ item.expireTime }}</span>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <div class="place-box"></div>
    <div class="renewval-foonter">
      <div class="total">
        <van-checkbox
          v-model="checkAll"
          checked-color="#FF9900"
          @change="handleAllChecked"
        />
        <span class="all">{{ $t("selectAll") }}</span>
        <span class="selectedNums" v-if="ident == 'en-US'"
          >({{ checkedNums }} {{ "selected" }} / {{ total }}
          {{ "in total" }})</span
        >
        <span class="selectedNums" v-if="ident == 'zh-CN'"
          >(已选{{ checkedNums }}个/共{{ total }}个)</span
        >
        <span class="selectedNums" v-if="ident == 'zh-TW'"
          >(已選{{ checkedNums }}個/共{{ total }}個)</span
        >
        <span class="selectedNums" v-if="ident == 'id-ID'"
          >{{ checkedNums }} telah dipilih / {{ total }} total</span
        >
        <!-- 这种中文化的翻译不好固定插入，单独处理一下 -->
      </div>
      <div class="renewal-now">
        <van-button
          type="primary"
          color="#FF9900"
          @click="handleRenew"
          :disabled="checkedNums == 0"
        >
          {{ $t("renewNow") }}
        </van-button>
      </div>
    </div>
    <van-popup
      v-model="showDatePopup"
      round
      position="bottom"
      closeable
      :style="{ height: '20%' }"
    >
      <div class="date-title">{{ $t("timeRange") }}</div>
      <div class="date-box">
        <div
          class="date-item"
          v-for="item in dateOption"
          :key="item.value"
          :class="{ active: item.value == activeDate.value }"
          @click="handleDateChange(item)"
        >
          {{ $t(item.text) }}
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  NavBar,
  Checkbox,
  CheckboxGroup,
  Button,
  Sticky,
  Popup,
  List,
  PullRefresh,
  Toast
} from "vant";
import {
  getXFServiceDetailList,
  costDataSummary,
  generateDeptBill
} from "../api/renewal";
export default {
  components: {
    [NavBar.name]: NavBar,
    [Checkbox.name]: Checkbox,
    [CheckboxGroup.name]: CheckboxGroup,
    [Button.name]: Button,
    [Sticky.name]: Sticky,
    [Popup.name]: Popup,
    [List.name]: List,
    [PullRefresh.name]: PullRefresh,
    [Toast.name]: Toast
  },
  data() {
    return {
      dateOption: [
        { text: "last30", value: 0 },
        { text: "last60", value: 1 },
        { text: "last90", value: 2 },
        { text: "all", value: 3 }
      ],
      shops: [],
      checkAll: false,
      showDatePopup: false,
      activeDate: {
        text: "last30",
        value: 0
      },
      page: {
        no: 1,
        limit: 10
      },
      loading: false,
      finished: false,
      error: false,
      refreshing: false,
      total: 0,
      groupId: "",
      userId: "",
      softwareId: "",
      storageValue: "",
      customerId: "",
      navName: "巡店续费",
      ident: ""
    };
  },
  computed: {
    dateTime() {
      const dateVal = this.activeDate.value;
      let endTime = "";
      if (dateVal == 0) {
        // 近30天
        endTime = this.$moment()
          .add(30, "days")
          .endOf("day")
          .format("YYYY-MM-DD HH:mm:ss");
        return {
          startTime: "",
          endTime
        };
      } else if (dateVal == 1) {
        // 近60天
        endTime = this.$moment()
          .add(60, "days")
          .endOf("day")
          .format("YYYY-MM-DD HH:mm:ss");
        return {
          startTime: "",
          endTime
        };
      } else if (dateVal == 2) {
        // 近90天
        endTime = this.$moment()
          .add(90, "days")
          .endOf("day")
          .format("YYYY-MM-DD HH:mm:ss");
        return {
          startTime: "",
          endTime
        };
      } else {
        return {
          startTime: "",
          endTime: ""
        };
      }
    },
    checkedNums() {
      return this.shops.filter(item => item.checked).length;
    },
    checkedDeptIds() {
      return this.shops.filter(item => item.checked).map(item => item.deptId);
    }
  },
  async created() {
    this.groupId = this.$route.query.groupId;
    this.softwareId = this.$route.query.softwareId;
    this.navName = this.$route.query.softwareName;
    // 为京东电脑数码定制化处理
    if (this.groupId == 5103) {
      this.activeDate = {
        text: "last60",
        value: 1
      };
    }
    if (sessionStorage.getItem("checkedIdList")) {
      this.storageValue = JSON.parse(sessionStorage.getItem("checkedIdList"))
        ?.map(item => item.name)
        ?.join(",");
    }
    if (sessionStorage.getItem("activeDate")) {
      this.activeDate = JSON.parse(sessionStorage.getItem("activeDate"));
    }
  },
  async mounted() {
    console.log(navigator.userAgent.split("/").pop());
    this.ident = navigator.userAgent.split("/").pop() || "zh-CN";
    this.userId = this.$route.query.id;
    if (this.userId) {
      await this.costDataSummary();
    }
    this.getShopList();
  },
  methods: {
    onClickLeft() {
      this.backToApp();
    },
    handleDateChange(item) {
      this.activeDate = item;
      this.showDatePopup = false;
      this.page.no = 1;
      this.shops = [];
      this.getShopList();
      sessionStorage.setItem("activeDate", JSON.stringify(item));
    },
    handleAreaChange() {
      this.$router.push({
        name: "organization",
        query: {
          groupId: this.groupId || 1084
        }
      });
    },
    async getShopList() {
      if (this.refreshing) {
        this.shops = [];
        this.refreshing = false;
      }
      const params = {
        ...this.page,
        ...this.dateTime,
        softwareId: Number(this.softwareId)
      };
      if (sessionStorage.getItem("checkedIdList")) {
        const checkedIdList = JSON.parse(
          sessionStorage.getItem("checkedIdList")
        );
        params.areaIds = checkedIdList?.map(it => it.id)?.join(",");
      }
      this.loading = true;
      try {
        const res = await getXFServiceDetailList(params);
        const result = res?.records?.map(item => ({
          ...item,
          checked: false
        }));
        this.shops = this.shops.concat(result || []);
        this.customerId = this.shops[0]?.customerId;
        this.total = res?.total || 0;
        this.loading = false;
        if (this.shops.length >= this.total) {
          this.finished = true;
        } else {
          this.page.no++;
        }
      } catch (error) {
        this.error = true;
        this.loading = false;
        Toast.fail({
          duration: 2000,
          message: error.result || error.data
        });
      }
    },
    async costDataSummary() {
      try {
        const res = await costDataSummary({
          id: this.userId
        });
        if (res.serviceList && res.serviceList.length) {
          const findOne = res.serviceList?.reduce((acc, cur) => {
            return acc.expireNum >= cur.expireNum ? acc : cur;
          });
          this.softwareId = findOne?.softwareId;
          this.navName = findOne?.softwareName;
        }
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.result || error.data
        });
      }
    },
    handleAllChecked(checked) {
      this.shops.forEach(item => {
        item.checked = checked;
      });
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false;
      sessionStorage.clear();
      this.storageValue = "";

      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.page.no = 1;
      this.activeDate = {
        text: "last30",
        value: 0
      };
      // 为京东电脑数码定制化处理
      if (this.groupId == 5103) {
        this.activeDate = {
          text: "last60",
          value: 1
        };
      }
      this.getShopList();
    },
    async handleRenew() {
      const params = {
        billingCycle: "",
        checkCycle: "",
        countType: "",
        isAdvance: "",
        generateType: 1,
        customerId: this.customerId,
        deptIds: this.checkedDeptIds,
        billSource: "2"
      };
      try {
        const res = await generateDeptBill(params);
        Toast.success({
          duration: 2000,
          message: "账单创建成功"
        });
        // 如果res为0，说明是门店账单一次会生成多条，此时不用跳转到账单详情页
        if (res == 0) return;
        this.$router.push({
          name: "billDetail",
          query: {
            billId: res,
            token: this.$route.query.token
          }
        });
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.result || error.data
        });
      }
    },
    onCheckedChange(item) {
      item.checked = !item.checked;
    }
  }
};
</script>

<style lang="scss" scoped>
.renewal-container {
  background-color: rgb(247, 247, 247);
  /deep/ .van-nav-bar .van-icon {
    font-size: 28px;
    color: #666;
  }
  .renewal-search {
    .sticky-box {
      display: flex;
      justify-content: space-around;
      align-items: center;
      background: #ffffff;
      height: 44px;
      .area-wrap {
        display: flex;
        align-items: center;
        font-size: 14px;
        .area {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          max-width: 120px;
          &.active {
            color: #ff9900;
          }
        }
        .rect {
          width: 10px;
          height: 4px;
          margin-left: 4px;
        }
      }
      .date-wrap {
        display: flex;
        align-items: center;
        color: #ff9900;
        font-size: 14px;
        .rect {
          width: 10px;
          height: 4px;
          margin-left: 4px;
        }
      }
    }
  }
  .renewval-content {
    margin-top: 6px;
    background-color: #ffffff;
    height: 100%;
    .shop-item {
      display: flex;
      align-items: flex-start;
      border-bottom: 1px solid #e5e5e5;
      padding: 12px;
      .shop-box {
        margin-left: 12px;
        .title {
          font-size: 16px;
          color: #000000;
          font-weight: 600;
          margin-bottom: 4px;
        }
        .area {
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          margin-bottom: 6px;
        }
        .expried {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          .tag {
            display: inline-block;
            padding: 0 4px;
            height: 20px;
            background: #ed830c0f;
            border-radius: 2px;
            font-size: 10px;
            color: #ed830c;
            line-height: 20px;
            text-align: center;
            margin-right: 8px;
            &.expired-tag {
              color: red;
            }
          }
        }
      }
    }
  }
  .place-box {
    width: 100vw;
    height: 56px;
    background-color: rgb(247, 247, 247);
  }
  .renewval-foonter {
    width: 100vw;
    position: fixed;
    bottom: 0;
    left: 0;
    height: 52px;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    box-sizing: border-box;
    .total {
      display: flex;
      align-items: center;
      .all {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 22px;
        margin: 0 8px;
      }
      .selectedNums {
        font-weight: 400;
        font-size: 14px;
        color: #000000;
        line-height: 22px;
      }
    }
    .van-button {
      width: 103px;
      height: 38px;
      border-radius: 6px;
    }
  }
}
.date-title {
  font-size: 16px;
  color: #000000;
  font-weight: bold;
  height: 48px;
  text-align: center;
  line-height: 48px;
}
.date-box {
  display: flex;
  padding: 16px;
  .date-item {
    width: 80px;
    height: 32px;
    background: #f7f7f7;
    border-radius: 4px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    &.active {
      color: #ff9900;
      border: 1px solid #ff9900;
    }
  }
}
/deep/ .van-dropdown-menu__title {
  color: #ff9900;
  font-size: 14px;
  &::after {
    border-left-color: #ff9900;
    border-bottom-color: #ff9900;
    width: 10px;
    height: 4px;
  }
}
/deep/ .van-popup--top {
  background: #ffffff;
  border-radius: 0 0 12px 12px;
}
/deep/ .van-nav-bar .van-icon {
  color: #333;
}
/deep/ .van-dropdown-menu__bar {
  box-shadow: none;
}
</style>
