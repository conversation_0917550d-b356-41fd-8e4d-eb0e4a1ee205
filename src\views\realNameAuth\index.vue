<template>
  <div class="realNameAuth">
    <div class="title">快捷登录注册</div>
    <main>
      <div class="input-box">
        <input
          v-model="from.phone"
          type="number"
          oninput="if(value.length>11)value=value.slice(0,11)"
          class="input"
          placeholder="请输入手机号"
        />
        <img
          v-if="showDelete"
          @click="clearPhone"
          class="delete"
          src="https://ovopark.oss-cn-hangzhou.aliyuncs.com/2023/12/15/ico_close.png"
          alt=""
        />
      </div>
      <div class="input-box">
        <input
          class="input"
          id="inputCode"
          v-model="from.verifyCode"
          oninput="if(value.length>6)value=value.slice(0,6)"
          type="number"
          placeholder="请输入验证码"
        />
        <span class="authCode" v-if="showVerify" @click="clickVerify(1)"
          >获取验证码</span
        >
        <span class="authCode-and" v-else>重新获取({{ count }}s)</span>
      </div>
      <van-button type="default" :disabled="disabledStatus" @click="login"
        >登录</van-button
      >
      <div class="agree">
        <div>
          <van-radio-group v-model="radio">
            <van-radio checked-color="#FF9900" name="1"></van-radio
          ></van-radio-group>
        </div>
        <div class="agree-txt">
          我已阅读并同意 <span class="agree-color">《隐私策略》、</span
          ><span class="agree-color">《服务协议》</span> 和<span
            class="agree-color"
            >《数字证书使用协议》</span
          >
        </div>
      </div>
      <div class="whyLogin" @click="viewVideo">付款为什么要登录？</div>
    </main>
    <footer v-if="isWeChatBrowser">
      <p>其他登录方式</p>
      <img
        @click="getWechatCode"
        class="wx"
        src="https://ovopark.oss-cn-hangzhou.aliyuncs.com/2023/12/15/<EMAIL>"
        alt=""
      />
    </footer>

    <van-dialog v-model="showRelevance" :show="show" :showConfirmButton="false">
      <div slot="title" class="title-name">
        关联手机号
        <img
          @click="showRelevance = false"
          src="https://ovopark.oss-cn-hangzhou.aliyuncs.com/2023/12/15/ico_close.png"
          alt=""
        />
      </div>
      <div class="dialog-box">
        <div class="dialog-input-box">
          <input
            v-model="from.phone"
            type="number"
            oninput="if(value.length>11)value=value.slice(0,11)"
            class="dialog-input"
            placeholder="请输入手机号"
          />
        </div>
        <div class="dialog-input-box">
          <input
            class="dialog-input"
            id="dialog-input-verify"
            v-model="from.verifyCode"
            oninput="if(value.length>6)value=value.slice(0,6)"
            type="number"
            placeholder="请输入验证码"
          />
          <span
            class="dialog-authCode"
            v-if="showVerify"
            @click="clickVerify(2)"
            >获取验证码</span
          >
          <span class="dialog-authCode-and" v-else>重新获取({{ count }}s)</span>
        </div>

        <van-button class="dialog-btn" type="default" @click="login"
          >绑定</van-button
        >
      </div>
    </van-dialog>
  </div>
</template>

<script>
// api
import {
  getUserByOpenId,
  getVerifyCode,
  checkVerifyCode,
  createLinkmanUserForWx
} from "../../api/realNameAuth";
import { getOpenId2, getWeChatInfo } from "../../api/delivery";
// components
import { Button, Toast } from "vant";
import { Radio, RadioGroup, Dialog } from "vant";
import Cookie from "js-cookie";
export default {
  components: {
    [Button.name]: Button,
    [Radio.name]: Radio,
    [RadioGroup.name]: RadioGroup,
    [Dialog.Component.name]: Dialog.Component,
    [Toast.name]: Toast
  },
  data() {
    return {
      from: {
        phone: "",
        verifyCode: ""
      },
      radio: "",
      showDelete: false,
      show: false,
      showRelevance: false, //关联手机号
      showVerify: true,
      timer: null,
      count: "",
      code: "", //微信code
      nickname: "",
      openId: ""
    };
  },
  computed: {
    disabledStatus() {
      if (this.radio && this.from.phone && this.from.verifyCode) {
        return false;
      } else {
        return true;
      }
    },

    //判断是不是微信
    isWeChatBrowser() {
      const userAgent = navigator.userAgent.toLowerCase();
      return userAgent.includes("micromessenger");
    }
  },
  watch: {
    "from.phone"(val) {
      val ? (this.showDelete = true) : (this.showDelete = false);
    }
  },
  created() {
    if (this.IsWeixinOrAlipay() == "false") {
      return this.$router.push({
        name: "otherScan"
      });
    }

    // let sourcePage = this.$route.query?.sourcePage;
    // sessionStorage.setItem("sourcePage", sourcePage);
    let code = this.$route.query?.code;
    if (code) {
      this.getOpenId2(code);
    }
  },
  methods: {
    IsWeixinOrAlipay() {
      var ua = window.navigator.userAgent.toLowerCase();
      //判断是不是微信
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        return "WeiXIN";
      }
      //判断是不是支付宝
      if (ua.match(/AlipayClient/i) == "alipayclient") {
        return "Alipay";
      }
      //哪个都不是
      return "false";
    },

    // 清空手机号
    clearPhone() {
      this.from.phone = "";
    },

    // 根据code获取openId
    async getOpenId2(code) {
      try {
        const res = await getOpenId2({ code });
        if (res) {
          this.openId = res.openid;
          this.getWeChatInfo(res);
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 获取微信基础信息
    async getWeChatInfo(obj) {
      if (!obj.access_token || !obj.openid) return;
      try {
        const res = await getWeChatInfo({
          accessToken: obj.access_token,
          openId: obj.openid
        });
        let wxMessage = {
          nickname: res.nickname,
          openId: res.openid
        };
        sessionStorage.setItem("wxMessage", JSON.stringify(wxMessage));

        this.createLinkmanUserForWx(obj.openid);
      } catch (error) {
        console.log(error);
      }
    },

    // 微信登录
    async createLinkmanUserForWx(openId) {
      try {
        const res = await createLinkmanUserForWx({ openId });
        // console.log(res, "微信登录");
        if (res) {
          Cookie.set("token", res.token);
          sessionStorage.setItem("tokenAuth", res.token);
          this.getUserByOpenId(openId, res.token);
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 根据openId查询用户是否绑定手机号
    async getUserByOpenId(openId, token) {
      try {
        const res = await getUserByOpenId({
          openId
        });
        // console.log(res, "是否绑定手机号");
        if (res && !res.mobilePhone) {
          this.showRelevance = true;
        } else if (res && res.mobilePhone) {
          sessionStorage.setItem("mobilePhoneAuth", res.mobilePhone);
          if (this.$route.query?.sourcePage == "contractPage") {
            // 跳转合同付款页面
            this.$router.push({
              name: "contractDetail",
              query: {
                contractId: this.$route.query?.contractId,
                token: token,
                isLogin: "1"
              }
            });
          } else if (this.$route.query?.sourcePage == "paymentLandedPage") {
            // 跳转付款页面
            this.$router.push({
              name: "paymentLanded",
              query: {
                orderNo: this.$route.query?.orderNo,
                sourcePage: "paymentLandedPage",
                token: token,
                isLogin: "1"
              }
            });
          }
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 获取验证码
    async getVerifyCode(val) {
      try {
        await getVerifyCode({
          phone: this.from.phone
        });
        if (val == 1) {
          let element = document.getElementById("inputCode"); // 获取到指定标签
          element.focus(); // 重新聚焦输入框
        } else if (val == 2) {
          let element = document.getElementById("dialog-input-verify"); // 获取到指定标签
          element.focus(); // 重新聚焦输入框
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 获取微信授权
    getWechatCode() {
      if (!this.radio) {
        return Toast.fail({
          duration: 2000,
          message: "请先勾选协议"
        });
      }
      let prefix = location.host;
      const ENVIRONMENT = prefix.indexOf("ovopark") != -1; //false 测试服 true 正式服
      let suffix = ENVIRONMENT ? ".ovopark.com" : ".wandianzhang.com";
      let appid = ENVIRONMENT ? "wxbc2eda1c2aeebb8f" : "wx9dfb48abbcaa7231"; //微信APPid
      let code = this.getUrlCode().code; //是否存在code
      let local = "";
      if (this.$route.query?.sourcePage == "contractPage") {
        local = `http://tiantih5${suffix}/notice/realNameAuth?sourcePage=${this.$route.query?.sourcePage}&contractId=${this.$route.query?.contractId}`;
      } else if (this.$route.query?.sourcePage == "paymentLandedPage") {
        local = `http://tiantih5${suffix}/notice/realNameAuth?sourcePage=${this.$route.query?.sourcePage}&orderNo=${this.$route.query?.orderNo}`;
      }

      if (code == null || code === "") {
        //不存在就打开下面的地址进行授权
        window.location.href =
          "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" +
          appid +
          "&redirect_uri=" +
          encodeURIComponent(local) +
          "&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect";
      } else {
        this.code = code;
      }
    },

    // 截取url中的code方法
    getUrlCode() {
      var url = location.search;
      console.log(url);
      var theRequest = new Object();
      if (url.indexOf("?") != -1) {
        var str = url.substr(1);
        var strs = str.split("&");
        for (var i = 0; i < strs.length; i++) {
          theRequest[strs[i].split("=")[0]] = strs[i].split("=")[1];
        }
      }
      return theRequest;
    },

    // 验证验证码
    async login() {
      if (!this.from.phone || !this.from.verifyCode) {
        return;
      }
      try {
        const res = await checkVerifyCode({
          ...this.from,
          openId: this.openId ? this.openId : null
        });
        if (res) {
          Cookie.set("token", res.token);
          sessionStorage.setItem("tokenAuth", res.token);
          sessionStorage.setItem("mobilePhoneAuth", this.from.phone);
          Toast.success({
            duration: 2000,
            message: "登录成功"
          });
          if (this.$route.query?.sourcePage == "contractPage") {
            // 跳转合同付款页面
            this.$router.push({
              name: "contractDetail",
              query: {
                contractId: this.$route.query?.contractId,
                token: res?.token,
                isLogin: "1"
              }
            });
          } else if (this.$route.query?.sourcePage == "paymentLandedPage") {
            // 跳转付款页面
            this.$router.push({
              name: "paymentLanded",
              query: {
                orderNo: this.$route.query?.orderNo,
                sourcePage: "paymentLandedPage",
                token: res?.token,
                isLogin: "1"
              }
            });
          }
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 获取验证码
    clickVerify(val) {
      if (!(this.from.phone && this.from.phone.length == 11)) {
        return;
      }
      // 获取验证码接口调用
      this.getVerifyCode(val);
      const TIME_COUNT = 60;
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.showVerify = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.showVerify = true;
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000);
      }
    },
    showAuth() {
      this.show = true;
    },
    confirm() {
      this.show = false;
    },
    cancel() {
      this.show = false;
    },
    // 付款为什么要登录？
    viewVideo() {
      window.location.href =
        "https://ovopark.oss-cn-hangzhou.aliyuncs.com/2024/03/12/video.mp4";
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>