* {
  box-sizing: border-box;
}

.order-page {
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  display: grid;
  grid-template-rows: auto 1fr auto;
  grid-template-areas:
    "header"
    "main"
    "footer";
  /* 将背景图设置在整个页面底层 */
  background: url("https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2025/07/16/bg.png")
    no-repeat center top;
  background-size: cover;
  background-attachment: fixed; /* 固定背景，不随滚动移动 */
  position: relative;
}

/* 固定头部区域 */
.page-header {
  grid-area: header;
  background: transparent; /* 透明背景，显示页面背景图 */
  z-index: 10;
  position: relative;
}

/* 导航栏 */
.nav-bar {
  padding: 16px 16px;
}

.nav-back {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

/* 可滚动主内容区域 */
.page-main {
  grid-area: main;
  overflow: hidden; /* 防止主区域本身滚动 */
}

/* 滚动内容容器 */
.scroll-content {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  /* 优化滚动体验 */
  -webkit-overflow-scrolling: touch;
  background: transparent;
}

/* 背景图区域（在滚动内容中） */
.bg-section {
  background: transparent;
  padding: 0 16px 20px;
  position: relative;
  padding-top: 20px;
  /* 确保有足够的最小高度来显示背景图 */
  min-height: 200px;
}

/* 主要内容 */
.main-content {
  padding: 0 16px 20px;
  background: #f7f7f7;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  margin-top: 16px;
}

/* 固定底部区域 */
.page-footer {
  grid-area: footer;
  background: white;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #eee;
  z-index: 10;
}

/* 卡片样式 */
.order-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  /* 添加背景模糊效果 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  /* 确保卡片在背景图之上 */
  position: relative;
  z-index: 2;
}

.product-card,
.customer-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:last-child {
    margin-bottom:0;
  }
}


/* 订单头部 */
.order-header {
  margin-bottom: 20px;
}

.order-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.order-number {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 检查项目 */
.check-list {
  margin-bottom: 24px;
  border: 0.5px solid #ff990080;
  border-radius: 4px;
  background-image: linear-gradient(180deg, #fff7eb 0%, #ffffff 100%);
  padding: 16px;
}

.check-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 0;
  border: none;
  border-radius: 0;
  background: transparent;
}

.check-item:last-child {
  margin-bottom: 0;
}

.check-text {
  font-size: 13px;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
}

.check-icon {
  color: #ff9900;
  font-weight: bold;
  font-size: 16px;
  line-height: 1;
}

.table-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  font-size: 14px;
  min-height: 44px;
  width: 100%;
}

.table-row.clickable:hover {
  background-color: #f8f8f8;
}

/* 可点击行的值和箭头组合 */
.value-with-arrow {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  flex: 1;
}

.value-with-arrow .value {
  color: #333;
  font-weight: 400;
  text-align: right;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.arrow-icon {
  color: #c8c8c8;
  font-size: 16px;
  flex-shrink: 0;
}

.label {
  color: #333;
  font-weight: 400;
  flex-shrink: 0;
  min-width: 80px;
}

.value {
  color: #333;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 8px;
  text-align: right;
  /* justify-content: flex-end; */
}

/* 数量控制 */
.stepper-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity {
  min-width: 30px;
  text-align: center;
  font-weight: 500;
  color: #333;
}

.stepper-btn {
  width: 24px;
  height: 24px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.stepper-btn:hover {
  background: #f5f5f5;
}

.stepper-btn:active {
  background: #e8e8e8;
}

/* 区域标题 */
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 55px;
  padding: 12px 16px;
}

.info-icon {
  color: #999;
  font-size: 14px;
}

/* 产品名称 */
.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

/* 评级 */
.rating-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.rating-label {
  font-size: 14px;
  color: #333;
  margin-right: 12px;
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  color: #ddd;
  font-size: 24px;
}

.star.active {
  color: #ff9900;
}

/* 客户信息 */
.section-subtitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

/* 在table-row中的section-subtitle特殊处理 */
.table-row .section-subtitle {
  margin: 0;
  font-size: 14px;
  font-weight: 400;
}

/* 底部操作栏样式调整 */
.total-price {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.confirm-button {
  background: #ff8c00;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 32px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.confirm-button:hover {
  background: #ff7f00;
}

.confirm-button:active {
  background: #ff6500;
}

/* 地址弹出层样式 */
.address-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 门店弹出层样式 */
.store-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 店长弹出层样式 */
.manager-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.address-list {
  flex: 1;
  padding: 16px 20px;
  overflow-y: auto;
}

.address-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s;
}

.address-item:last-child {
  border-bottom: none;
}

.address-info {
  flex: 1;
  text-align: left;
  margin-right: 12px;
}

.address-name {
  font-size: 15px;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
  text-align: left;
}

.address-detail {
  font-size: 13px;
  color: #666;
  line-height: 1.3;
  text-align: left;
}

/* 地址弹窗中的勾选图标位置调整 */
.address-popup .van-icon {
  margin-top: 2px;
  flex-shrink: 0;
}

/* 搜索容器样式 */
.search-container {
  border-bottom: 1px solid #f0f0f0;
}

/* 加载状态容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

/* 空状态样式 */
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

/* 地址项详细信息 */
.address-full {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.3;
}

/* 门店列表样式 */
.store-list {
  flex: 1;
  padding: 16px 20px;
  overflow-y: auto;
}

.store-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s;
}

.store-item:last-child {
  border-bottom: none;
}

.store-info {
  flex: 1;
  text-align: left;
  margin-right: 12px;
}

.store-name {
  font-size: 15px;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
  text-align: left;
}

.store-detail {
  font-size: 13px;
  color: #666;
  line-height: 1.3;
  text-align: left;
}

/* 店长列表样式 */
.manager-list {
  flex: 1;
  padding: 16px 20px;
  overflow-y: auto;
}

.manager-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s;
}

.manager-item:last-child {
  border-bottom: none;
}

.manager-info {
  flex: 1;
  text-align: left;
  margin-right: 12px;
}

.manager-name {
  font-size: 15px;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
  text-align: left;
}

.manager-detail {
  font-size: 13px;
  color: #666;
  line-height: 1.3;
  text-align: left;
}

/* 新增地址按钮容器 */
.add-address-btn {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: white;
}

/* 新增地址按钮样式 */
.add-address-btn .van-button {
  border: 1px dashed #d9d9d9;
  background: #fafafa;
  color: #666;
  font-size: 14px;
}

.add-address-btn .van-button:active {
  background: #f0f0f0;
}

.add-address-btn .van-button .van-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* 新增门店按钮容器 */
.add-store-btn {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: white;
}

/* 新增门店按钮样式 */
.add-store-btn .van-button {
  border: 1px dashed #d9d9d9;
  background: #fafafa;
  color: #666;
  font-size: 14px;
}

.add-store-btn .van-button:active {
  background: #f0f0f0;
}

.add-store-btn .van-button .van-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* 新增店长按钮容器 */
.add-manager-btn {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: white;
}

/* 新增店长按钮样式 */
.add-manager-btn .van-button {
  border: 1px dashed #d9d9d9;
  background: #fafafa;
  color: #666;
  font-size: 14px;
}

.add-manager-btn .van-button:active {
  background: #f0f0f0;
}

.add-manager-btn .van-button .van-icon {
  margin-right: 8px;
  font-size: 16px;
}

