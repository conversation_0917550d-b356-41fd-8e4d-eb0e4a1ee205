.related-running-water {
  background-color: #f7f7f7;
  .water-top {
    padding: 16px;
    background-color: #fff;
    padding-bottom: 0;
    .title {
      font-size: 16px;
      color: #000000;
      font-weight: bold;
      margin-bottom: 12px;
    }
    .water-bonus {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      .desc {
        font-size: 13px;
        color: #7f7f7f;
        &:first-child {
          margin-right: 24px;
        }
        .nums {
          color: #333333;
          margin-left: 4px;
        }
      }
    }
    .arrow-box {
      text-align: center;
    }
    .down,
    .up {
      width: 20px;
      height: 20px;
    }
    .line {
      height: 1px;
      background-color: #e5e5e5;
      margin: 12px 0;
    }
    .item {
      margin-bottom: 8px;
      line-height: 20px;
      .label {
        display: inline-block;
        width: 86px;
        font-size: 13px;
        color: #7f7f7f;
      }
      .value {
        color: #333333;
        font-size: 13px;
      }
    }
  }
  .running-water-content {
    background-color: #fff;
    padding: 12px 16px;
    margin-top: 6px;
    .search {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      .search-box {
        padding: 0;
        ::v-deep .van-field {
          width: 280px;
        }
      }
    }
    .title {
      font-size: 14px;
      color: #000000;
      font-weight: bold;
      margin-bottom: 12px;
    }
    .water-box {
      background: #fafafa;
      border-radius: 6px;
      margin-bottom: 12px;
      .top {
        padding: 12px 16px;
        border-bottom: 1px solid #e5e5e5;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .water-left {
          display: flex;
          align-items: center;
          .customer-info {
            margin-left: 12px;
            .customer-name {
              font-size: 14px;
              color: #000000;
              font-weight: bold;
              margin-bottom: 4px;
            }
            .contract-no {
              font-size: 12px;
              color: #7f7f7f;
            }
          }
        }
        .change {
          width: 72px;
          height: 28px;
          background: #ff9900;
          border-radius: 6px;
          font-size: 14px;
          color: #ffffff;
          text-align: center;
          line-height: 28px;
          padding: 0;
        }
        .contract {
          font-size: 14px;
          color: #000000;
          font-weight: bold;
        }
        .revert {
          font-size: 14px;
          color: #ff1111;
          cursor: pointer;
          &.grey {
            color: #ccc;
            cursor: not-allowed;
            pointer-events: none;
          }
        }
      }
      .content {
        padding: 12px 16px 12px 44px;
        .item {
          margin-bottom: 8px;
          line-height: 20px;
          .label {
            font-size: 13px;
            color: #7f7f7f;
            display: inline-block;
            margin-right: 16px;
          }
          .value {
            color: #333333;
            font-size: 13px;
          }
        }
      }
      .has-related-content {
        padding: 12px 16px;
        .item {
          margin-bottom: 8px;
          line-height: 20px;
          .label {
            font-size: 13px;
            color: #7f7f7f;
            display: inline-block;
            width: 80px;
          }
          .value {
            color: #333333;
            font-size: 13px;
          }
        }
      }
    }
  }
  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-top: 1px solid #e5e5e5;
    .submit {
      width: 343px;
      height: 44px;
      background: #ff9900;
      border-radius: 8px;
      font-size: 17px;
      color: #ffffff;
      text-align: center;
      line-height: 44px;
      padding: 0;
    }
  }
  .dialog-top {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 16px;
    .title {
      font-size: 16px;
      color: #000000;
      font-weight: bold;
    }
    .close {
      width: 16px;
      height: 16px;
      position: absolute;
      right: 16px;
      top: 12px;
    }
  }
  .form-item {
    background-color: #fff;
    margin-bottom: 8px;
    .form-item-name {
      height: 44px;
      line-height: 44px;
      padding: 0 16px;
      .label {
        font-size: 14px;
        color: #000000;
        font-weight: bold;
      }
    }
    .form-item-content {
      padding: 0 16px;
      .field {
        background: #f7f7f7;
        border-radius: 6px;
        height: 40px;
      }
    }
    .tips {
      font-size: 12px;
      color: #7f7f7f;
      margin-top: 8px;
      padding: 0 16px;
      .nums {
        color: #ff1111;
        font-weight: bold;
      }
    }
  }
  .operate-btn {
    width: 100%;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #e5e5e5;
    .btn {
      height: 44px;
      line-height: 44px;
      background: #ffffff;
      border-radius: 8px;
      font-size: 13px;
      color: #333333;
      padding: 0;
      text-align: center;
      &.reset {
        width: 82px;
        margin-right: 12px;
      }
      &.refund {
        width: 118px;
        margin-right: 12px;
      }
      &.makesure {
        background: #ff9900;
        width: 120px;
        color: #fff;
      }
    }
  }
}
