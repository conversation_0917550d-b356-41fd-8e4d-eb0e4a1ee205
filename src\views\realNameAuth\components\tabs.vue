<template>
  <div class="tabs">
    <div class="step-box">
      <div :class="isActiveA ? 'stepActive' : 'stepAdef'">1</div>
      <p :class="isActiveA ? 'stepActiveTxt' : 'stepDefTxt'">{{message.stepA}}</p>
    </div>
    <div class="line"></div>
    <div class="step-box">
      <div :class="isActiveB ? 'stepActive' : 'stepDef'">2</div>
      <p :class="isActiveB ? 'stepActiveTxt' : 'stepDefTxt'">{{message.stepB}}</p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    isActiveA: {
      type: Boolean,
      default: false
    },
    isActiveB: {
      type: Boolean,
      default: false
    },
    message: {
      type: Object,
    }
  },
  data() {
    return {};
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.tabs {
  height: 89px;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 16px;

  .line {
    width: 148px;
    height: 2px;
    margin: 0 10px;
    background: #e5e5e5;
  }

  .step-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    font-size: 12px;
    color: #ff9900;

    p {
      margin-top: 8px;
    }
  }

  .step,
  .stepActive {
    width: 32px;
    height: 32px;
    background: #ff9900;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 14px;
  }

  .stepDef {
    width: 32px;
    height: 32px;
    background: #f0f0f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #b2b2b2;
    font-size: 14px;
  }
  .stepAdef {
    width: 32px;
    height: 32px;
    background: #ff99001a;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff9900;
    font-size: 14px;
  }

  .stepActiveTxt {
    color: #ff9900;
  }

  .stepDefTxt {
    color: #7f7f7f;
  }
}
</style>
