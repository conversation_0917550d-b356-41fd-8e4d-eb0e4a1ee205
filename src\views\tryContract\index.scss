.tryPage {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f7f7;
  overflow: hidden;

  main {
    flex: 1;
    padding: 12px 16px;
    overflow: auto;
    .tryList {
      padding: 12px 16px;
      background: #ffffff;
      border-radius: 6px;
      margin-bottom: 12px;

      .try-title {
        border-bottom: 1px solid #e5e5e5;
        .try-name {
          font-family: PingFangSC-Medium;
          font-weight: 550;
          font-size: 14px;
          color: #000000;
          line-height: 22px;
          margin-bottom: 4px;
        }

        .try-type {
          font-family: PingFangSC-Medium;
          font-weight: 550;
          font-size: 12px;
          text-align: right;
          line-height: 22px;
        }
        .try-title-top {
          display: flex;
          justify-content: space-between;
        }
        .try-no {
          height: 20px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #7f7f7f;
          line-height: 20px;
          margin-bottom: 12px;
        }
      }

      .try-content {
        margin-top: 12px;
        .item {
          display: flex;
          //   justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          label {
            display: inline-block;
            width: 78px;
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 13px;
            color: #7f7f7f;
            line-height: 20px;
            margin-right: 8px;
          }

          p {
            // width: 104px;
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 13px;
            color: #333333;
            line-height: 20px;
          }
        }
      }
    }
  }
}
