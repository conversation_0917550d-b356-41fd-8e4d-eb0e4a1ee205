<template>
  <van-popup
    v-model="show"
    position="right"
    :style="{ height: '100%', width: '100%' }"
  >
    <div class="popup">
      <div class="header">
        <img
          src="../../../assets/img/ico_back-t.png"
          alt=""
          @click="show = false"
        />
        <div class="title">筛选</div>
        <div></div>
      </div>
      <div class="main">
        <div class="item">
          <p>申请编号</p>
          <div class="v-input">
            <input
              :value="orderNo"
              @input="
                (event) => {
                  orderNo = event.target.value;
                }
              "
              type="text"
              placeholder="请输入申请编号"
            />
          </div>
        </div>

        <div class="item">
          <p>客户名称</p>
          <div class="v-input">
            <input
              :value="customerName"
              @input="
                (event) => {
                  customerName = event.target.value;
                }
              "
              type="text"
              placeholder="请输入客户名称"
            />
          </div>
        </div>

        <div class="item">
          <p>合同编号</p>
          <div class="v-input">
            <input
              :value="contractNo"
              @input="
                (event) => {
                  contractNo = event.target.value;
                }
              "
              type="text"
              placeholder="请输入合同编号"
            />
          </div>
        </div>

        <div class="item">
          <p>创建日期</p>
          <div class="item-date">
            <div class="v-date">
              <input
                :value="startTime"
                type="text"
                readonly
                placeholder="开始时间"
                @focus="showStartDate = true"
              />
            </div>
            <div class="line"></div>
            <div class="v-date">
              <input
                :value="endTime"
                type="text"
                readonly
                placeholder="结束时间"
                @focus="showEndDate = true"
              />
            </div>
          </div>
        </div>

        <div class="item">
          <p>审批状态</p>
          <div class="item-status">
            <div
              class="item-list"
              :class="activeIndices.includes(e.value) ? 'active-item-list' : ''"
              v-for="(e, eIndex) in statusList"
              :key="eIndex"
              @click="changeStatus(e, e.value)"
            >
              {{ e.dname }}
            </div>
          </div>
        </div>
      </div>
      <div class="footer">
        <van-button class="resect" @click="reset">重置</van-button>
        <van-button class="sure" color="#FF9900" @click="sure">确认</van-button>
      </div>
    </div>

    <!-- 开始时间 -->
    <van-popup
      round
      position="bottom"
      :style="{ height: '40%' }"
      v-model="showStartDate"
    >
      <van-datetime-picker
        v-model="currentStartDate"
        type="date"
        title="选择年月日"
        :columns-order="['year', 'month', 'day']"
        :formatter="formatter"
        @confirm="conStartfirm"
        @cancel="showStartDate = false"
      />
    </van-popup>

    <!-- 结束时间 -->
    <van-popup
      round
      position="bottom"
      :style="{ height: '40%' }"
      v-model="showEndDate"
    >
      <van-datetime-picker
        v-model="currentEndDate"
        type="date"
        title="选择年月日"
        :columns-order="['year', 'month', 'day']"
        :formatter="formatter"
        :min-date="minDate"
        @confirm="conEndfirm"
        @cancel="showEndDate = false"
      />
    </van-popup>
  </van-popup>
</template>

<script>
import { Popup, Button, DatetimePicker } from "vant";
export default {
  components: {
    [Popup.name]: Popup,
    [Button.name]: Button,
    [DatetimePicker.name]: DatetimePicker
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeIndices: [],
      show: false,
      orderNo: "",
      customerName: "",
      contractNo: "",
      auditStatus: "",
      startTime: "", // 开始时间
      endTime: "", // 结束时间
      statusList: [
        { value: 1, dname: "审批中" },
        { value: 2, dname: "已通过" },
        { value: 3, dname: "驳回" },
        { value: 4, dname: "草稿" }
      ],
      currentStartDate: new Date(),
      currentEndDate: new Date(),
      showStartDate: false,
      showEndDate: false,
      minDate: ""
    };
  },
  watch: {
    value(val) {
      this.show = val;
      this.$nextTick(() => {
        this.getFlex();
      });
    },
    show(val) {
      this.$emit("input", val);
    }
  },

  mounted() {},

  methods: {
    // 选择开始日期
    conStartfirm() {
      this.endTime = "";
      this.currentEndDate = "";
      this.showStartDate = false;
      this.startTime = this.$moment(this.currentStartDate).format("YYYY-MM-DD");
      this.minDate = this.currentStartDate;
    },

    // 选择结束日期
    conEndfirm() {
      this.showEndDate = false;
      this.endTime = this.$moment(this.currentEndDate).format("YYYY-MM-DD");
    },

    // 格式化时间
    formatter(type, val) {
      if (type === "year") {
        return val + "年";
      }
      if (type === "month") {
        return val + "月";
      }
      if (type === "day") {
        return val + "日";
      }
      return val;
    },

    // 查询
    sure() {
      this.$emit("sure", {
        orderNo: this.orderNo,
        customerName: this.customerName,
        contractNo: this.contractNo,
        auditStatus: this.activeIndices?.join(","),
        startTime: this.startTime,
        endTime: this.endTime
      });
      this.show = false;
    },

    // 重置
    reset() {
      this.orderNo = "";
      this.customerName = "";
      this.contractNo = "";
      this.auditStatus = "";
      this.startTime = "";
      this.endTime = "";
      this.currentStartDate = new Date();
      this.currentEndDate = new Date();
      this.activeIndices = [];
      this.sure();
    },

    // 改变状态
    changeStatus(e, value) {
      const index = this.activeIndices.indexOf(value);
      if (index >= 0) {
        // 如果 value 已经在 activeIndices 中，就从 activeIndices 中删除它
        this.activeIndices.splice(index, 1);
      } else {
        // 如果 value 不在 activeIndices 中，就添加到 activeIndices 中
        this.activeIndices.push(value);
      }
    },

    getFlex() {
      // 获取容器宽度
      const containerWidth = document.querySelector(".item-status").offsetWidth;
      // 获取单个元素的宽度（包括 margin）
      const itemWidth = document.querySelector(".item-list").offsetWidth;
      // 计算每行可以放置的元素数量
      const itemsPerRow = Math.floor(containerWidth / itemWidth);
      // 获取所有的元素
      const items = document.querySelectorAll(".item-list");
      // 遍历所有的元素
      items.forEach((item, index) => {
        // 如果元素是每行的最后一个，则去除其右边距
        if ((index + 1) % itemsPerRow === 0) {
          item.style.marginRight = "0";
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import "../index.scss";
</style>