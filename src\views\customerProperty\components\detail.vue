<template>
  <van-popup
    v-model="show"
    position="right"
    :style="{ height: '100%', width: '100%' }"
  >
    <div class="customer-property-detail">
      <van-nav-bar left-arrow @click-left="show = false" title="客户详情" />
      <div class="detail-wrap">
        <div class="customer-brief">
          <div class="logo-name">
            <div class="logo"></div>
            <div class="name">{{ info.customerName }}</div>
          </div>
          <div class="brief">
            <p class="txt">
              {{ info.industry }} · {{ info.cluingSource }} ·
              {{ info.brandMulti }}
            </p>
          </div>
        </div>
        <div class="customer-sort">
          <van-tabs
            background="#F7F7F7"
            color="#000"
            line-height="3px"
            title-inactive-color="#7F7F7F"
            @change="getCustomerInfoDetail"
            v-model="activeTab"
          >
            <van-tab title="基础信息" name="0">
              <div class="sort-item basic-info">
                <van-loading size="24px" vertical v-show="showLoading"
                  >加载中...</van-loading
                >
                <div class="item">
                  <span class="lable">当前客户负责人</span>
                  <span class="value">{{ info.saleUserUserName }}</span>
                </div>
                <div class="item">
                  <span class="lable">合作状态</span>
                  <span class="value">{{ info.cooperationStatus }}</span>
                </div>
                <div class="item">
                  <span class="lable">门店数量</span>
                  <span class="value">{{ info.shopCount }}</span>
                </div>
                <div class="item">
                  <span class="lable">业务联系</span>
                  <div class="business-wrap">
                    <span
                      class="value list"
                      v-for="(item, index) in info.softWareNames"
                      :key="index"
                      >{{ item
                      }}<span v-show="!(index + 1 == info.softWareNames.length)"
                        >,
                      </span></span
                    >
                  </div>
                </div>
              </div>
            </van-tab>
            <van-tab title="相似品牌" name="1">
              <div class="sort-item same-brand">
                <van-loading size="24px" vertical v-show="showLoading"
                  >加载中...</van-loading
                >
                <van-empty description="暂无数据" v-if="isEmpty" />
                <div
                  class="brand-item"
                  v-for="(item, index) in info.newSimilarBrandsList"
                  :key="index"
                >
                  {{ item }}
                </div>
              </div>
            </van-tab>
            <van-tab title="同类企业" name="2">
              <div class="sort-item same-enterprise">
                <van-loading size="24px" vertical v-show="showLoading"
                  >加载中...</van-loading
                >
                <van-empty description="暂无数据" v-if="isEmpty" />
                <!-- <div
                class="enterprise-item"
                v-for="(item, index) in enterpriseList"
                :key="index"
              >
                {{ item }}
              </div> -->
              </div></van-tab
            >
          </van-tabs>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script>
import { Button, Toast, NavBar, Tab, Tabs, Empty, Loading, Popup } from "vant";
import { getCustomerInfoDetail, getAllDict } from "api/customerProperty";
export default {
  components: {
    [Button.name]: Button,
    [Loading.name]: Loading,
    [NavBar.name]: NavBar,
    [Empty.name]: Empty,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
    [Toast.name]: Toast,
    [Popup.name]: Popup
  },

  props: {
    value: {
      type: Boolean,
      default: false
    },
    detailId: {
      type: [String, Number]
    }
  },

  data() {
    return {
      show: false,
      activeTab: "0",
      isEmpty: false,
      showLoading: false,
      info: {
        customerName: "",
        saleUserUserName: "",
        shopCount: "",
        enterpriseList: [],
        newSimilarBrandsList: [], //品牌需要处理
        softWareNames: [] //业务联系需要处理
      },
      dictList: [] //字典项
    };
  },
  computed: {},
  watch: {
    value(val) {
      this.show = val;
      if (val) {
        this.getCustomerInfoDetail();
      } else {
        this.info = {
          customerName: "",
          saleUserUserName: "",
          shopCount: "",
          enterpriseList: [],
          newSimilarBrandsList: [],
          softWareNames: []
        };
      }
    },

    show(val) {
      this.$emit("input", val);
    }
  },
  methods: {
    getDictArray(type) {
      return this.dictList.find(item => item.type === type)?.children || [];
    },

    // 获取详情
    async getCustomerInfoDetail(name = "0") {
      this.info.newSimilarBrandsList = [];
      try {
        const customerId = this.detailId;
        this.isEmpty = false;
        this.showLoading = true;
        if (!customerId) {
          throw new Error("Customer ID not found in route query parameters.");
        }
        this.dictList = await getAllDict();
        const res = await getCustomerInfoDetail({
          customerId,
          step: name
        });
        if (!res || (name == 1 && res?.similarBrandsList?.length === 0)) {
          this.isEmpty = true;
          return;
        }

        res.brandMulti = res?.brandMulti?.replace(/,/g, " · ") || "";
        res.cooperationStatus =
          this.getDictArray("cooperation_status").find(
            item => item.value === res.cooperationStatus
          )?.dname || "-";

        res.cluingSource =
          this.getDictArray("cluing_source").find(
            item => item.value === res.cluingSource
          )?.dname || "-";

        res.industry =
          this.getDictArray("customer_industry").find(
            item => item.value === res.industry
          )?.dname || "-";

        // 直接在对象字面量中设置 newSimilarBrandsList
        const newInfo = {
          ...res,
          softWareNames: name === "0" ? res?.softWareNames?.split(",") : [],
          newSimilarBrandsList:
            name === "1"
              ? res?.similarBrandsList
                  ?.map(item =>
                    JSON.parse(item.replace(/'/g, '"').replace(/\\/g, ""))
                  )
                  .flat()
              : []
        };

        if (
          name == 1 &&
          (newInfo?.newSimilarBrandsList?.length === 0 ||
            !newInfo?.newSimilarBrandsList)
        ) {
          this.isEmpty = true;
        }
        this.info = newInfo;
      } catch (error) {
        // 在生产环境中，您可能需要将错误记录到日志服务或发送错误报告
        console.error("Error fetching customer info detail:", error);
        this.isEmpty = true;
      } finally {
        this.showLoading = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.customer-property-detail {
  background: #f7f7f7;
  height: 100%;
  width: 100%;
  .detail-wrap {
    padding: 14px 16px 14px 16px;
    .customer-brief {
      min-height: 100px;
      background: #ffffff;
      border-radius: 6px;
      .txt {
        padding: 16px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #7f7f7f;
        line-height: 18px;
      }
      .logo-name {
        padding: 16px 16px 0 16px;
        display: flex;
        align-items: center;
        .logo {
          width: 40px;
          height: 40px;
          background-image: url("../../../assets/qiye.png");
          border-radius: 4px;
          background-size: cover; /* 背景图片覆盖整个元素区域 */
          background-position: center; /* 背景图片居中 */
          background-repeat: no-repeat; /* 背景图片不重复 */
        }
        .name {
          min-width: 0;
          margin-left: 8px;
          font-weight: 600;
          font-size: 20px;
          color: #000000;
          line-height: 24px;
        }
      }
    }
    .customer-sort {
      margin-top: 8px;
    }
    .brand-item,
    .enterprise-item {
      display: inline-block;
      margin-right: 12px;
      margin-bottom: 12px;
      padding: 0 12px;
      height: 32px;
      line-height: 32px;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      border: 1px solid #e5e5e5;
      border-radius: 16px;
    }

    .sort-item {
      padding: 16px;
      margin-top: 18px;
      background: #ffffff;
      border-radius: 6px;
      padding: 10px;
      .item {
        margin-top: 12px;
        .lable {
          display: inline-block;
          margin-right: 14px;
          width: 98px;
          height: 20px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #7f7f7f;
        }
        .value {
          font-weight: 400;
          font-size: 14px;
          color: #333333;
        }
        .business-wrap {
          display: inline-block;
          width: calc(100% - 112px);
          vertical-align: top;
        }
      }
    }
  }
}

/deep/ .van-nav-bar .van-icon {
  color: #000000;
}
</style>
