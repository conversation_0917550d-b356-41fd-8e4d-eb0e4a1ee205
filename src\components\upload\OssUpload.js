
import API from "/src/api/oss/config";
import axios from "axios";
import EventEmitter from "./EventEmitter.js";
import { generatorFileKey } from "./util.js";

let ossConfig = {
  ossurl: API.uploadUrl,
  partSize: 10 * 1024 * 1024,
  parallel: 3
};

const UploadEvents = {
  WAITING: "waiting",
  START: "start",
  UPLOADING: "uploading",
  PAUSE: "pause",
  COMPLATE: "complate",
  ERROR: "error"
};

const UploadStatus = {
  WAITING: 1,
  START: 2,
  UPLAODING: 3,
  PAUSE: 4,
  COMPLATE: 5,
  ERROR: 6
};

export default class OSSUpload {
  ossParams = {
    businessCode: "ovopark-data-assets"
  };
  /**
   *构造函数
   */
  constructor(file, options) {
    this.fileKey = generatorFileKey();
    this.uploadKey = options && options.uploadKey ? options.uploadKey : "";
    this.emitter = new EventEmitter(); // 订阅发布
    this.file = file;
    this.uploadInfo = { uploadStatus: UploadStatus.WAITING };
    // this.vm = options.vm;
  }

  /**
   *上传文件
   */
  async upload() {
    let { file, client } = this;
    this.uploadInfo.uploadStatus = UploadStatus.START;
    this.emit(UploadEvents.START, this.uploadInfo);
    // 如果oss实例不存在就新建
    if (!client) {
      client = await this._newClient();
    }

    if (file.size <= ossConfig.partSize) {
      return this._singleUplaod(file);
    }

    if (
      this.uploadInfo &&
      this.uploadInfo.checkpoint &&
      this.uploadInfo.progress < 100
    ) {
      return this._multipartUpload(file, client, 2);
    } else {
      return this._multipartUpload(file, client);
    }
  }

  /**
   *暂停上传文件
   */
  pause() {
    this.client.cancel();
    this.uploadInfo = Object.assign({}, this.uploadInfo, {
      uploadStatus: UploadStatus.PAUSE
    });
    localStorage.setItem(this.currentFileKey, JSON.stringify(this.uploadInfo));
    this.emit(UploadEvents.PAUSE, this.uploadInfo);
  }

  /**
   *发布事件
   */
  emit(event, data) {
    this.emitter.emit(event, data);
  }

  /**
   *订阅事件
   */
  on(event, handler) {
    this.emitter.on(event, handler);
  }

  /**
   *初始化ali-oss对象
   */
  async _newClient() {
    let ossParams = this.ossParams;
    this.client = ossParams;
    return this.client;
  }

  put(file, back) {
    let ossParams = this.ossParams;
    let formData = new FormData();
    formData.append("businessCode", ossParams.businessCode);
    formData.append("file", file);
    formData.append('uniqueName', true);

    return new Promise((resolve, reject) => {
      axios({
        url: ossConfig.ossurl,
        headers: {
          "Content-Type": "multipart/form-data"
        },
        method: "post",
        data: formData,
        onUploadProgress: progressEvent => {
          let { loaded, total } = progressEvent;
          let progress = (loaded / total).toFixed(4) * 100;
          back.progress(progress);
        }
      })
        .then(res => {
          resolve(res);
        })
        .catch(err => {
          reject(err);
        });
    });
  }

  async _singleUplaod(file) {
    try {
      // 如果文件大小不超过分片大小则直接上传
      let that = this;
      let back = {
        progress(progress) {
          // 记录上传进度
          that.uploadInfo = Object.assign({}, that.uploadInfo, {
            progress: parseInt(progress), // 上传进度
            uploadStatus: UploadStatus.UPLAODING // 上传状态
          });
          that.emit(UploadEvents.UPLOADING, {
            progress: parseInt(progress),
            uploadStatus: UploadStatus.UPLAODING
          });
        }
      };
      let result = await this.put(file, back);
      //   上传成功后处理
      let fileurl = result.data.data.accessUrl;

      that.uploadInfo = Object.assign({}, that.uploadInfo, {
        uploadStatus: UploadStatus.COMPLATE,
        url: fileurl,
        fileurl,
        size: file.size,
        progress: 100
      });

      that.emit(UploadEvents.COMPLATE, that.uploadInfo);

      return that.uploadInfo;
    } catch (err) {
      // 捕获超时异常
      this._errHandle(err);
    }
  }

  /**
   *分片上传文件
   */
  async _multipartUpload(file, client, type) {
    try {
      // 上传文件
      let that = this;
      let result = null;

      // 初始上传参数
      let requestOptions = {
        parallel: ossConfig.parallel,
        partSize: ossConfig.partSize,
        progress(percentage, checkpoint) {
          // 记录上传进度
          that.uploadInfo = Object.assign({}, that.uploadInfo, {
            progress: parseInt(percentage * 100), // 上传进度
            checkpoint: checkpoint, // 上传断点
            uploadStatus: UploadStatus.UPLAODING // 上传状态
          });

          that.emit(UploadEvents.UPLOADING, {
            progress: parseInt(percentage * 100),
            uploadStatus: UploadStatus.UPLAODING
          });
        }
      };

      // 判断是否是断点续传
      if (type == 2) {
        requestOptions = {
          ...requestOptions,
          checkpoint: this.uploadInfo.checkpoint
        };
      }

      // 上传文件到阿里云
      result = await client.multipartUpload(
        that.uploadKey + that.fileKey + "." + that._getFileType(file),
        file,
        requestOptions
      );

      //   上传成功后处理
      let fileurl = this._getAliOssUrl(result.res.requestUrls[0]);

      that.uploadInfo = Object.assign({}, that.uploadInfo, {
        uploadStatus: UploadStatus.COMPLATE,
        url: fileurl,
        fileurl,
        size: file.size,
        progress: 100
      });

      that.emit(UploadEvents.COMPLATE, that.uploadInfo);

      return that.uploadInfo;

      // progressCallback && progressCallback(that.uploadInfo);
    } catch (err) {
      // 捕获超时异常
      this._errHandle(err);
    }
  }

  /**
   *上传错误
   */
  _errHandle(err) {
    let message = "";
    let errCode = 1;

    if (err.code === "ConnectionTimeoutError") {
      message = "连接超时";
      errCode = 2;
    }
    if (err.code === "RequestError") {
      message = "请求出错";
      errCode = 3;
    }
    // 将上传状态设置为上传错误状态
    if (err && err.name === "cancel") {
      message = "取消上传";
      errCode = 4;
    }

    this.uploadInfo = Object.assign({}, this.uploadInfo, {
      uploadStatus: UploadStatus.ERROR,
      message,
      errCode
    });

    this.emit(UploadEvents.ERROR, this.uploadInfo);
  }

  /**
   *获取文件类型
   */
  _getAliOssUrl(url) {
    let length = url.indexOf("?uploadId");
    if (length === -1) {
      return url;
    } else {
      return url.substring(0, length);
    }
  }

  /**
   *获取文件类型
   */
  _getFileType(file) {
    let fileNameArray = file.name.split(".");
    let fileType = fileNameArray[fileNameArray.length - 1];
    return fileType;
  }
}
