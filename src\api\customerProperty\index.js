import urls from "./config";
import { get, postJson } from "../request";

/**
 * 客户属性详情
 */
export function getCustomerInfoDetail(params = {}) {
  return get(urls.getCustomerInfoDetail, params);
}

/**jsApi */
export function getCustomerInfoAnalysis(params) {
  return postJson(urls.getCustomerInfoAnalysis, params);
}

/*
 * 字典项
 */

export function getAllDict(params = {}) {
  return get(urls.getAllDict, params);
}

// 所有关联软件产品接口
export function getProductLineServiceList(params = {}) {
  return get(urls.getProductLineServiceList, params);
}
