<template>
  <div class="scan-container">
    <van-nav-bar
      title="扫一扫"
      left-arrow
      @click-left="onleftClick"
    ></van-nav-bar>
    <video ref="video" autoplay></video>
    <canvas ref="canvas"></canvas>
    <div v-if="decodedText">
      <p>Scanned QR Code Text: {{ decodedText }}</p>
    </div>
  </div>
</template>

<script>
import { NavBar } from "vant";
import jsQR from "jsqr";
export default {
  components: {
    [NavBar.name]: NavBar
  },
  data() {
    return {
      groupId: "",
      token: "",
      html5QrCode: null,
      decodedText: null,
      codeReader: null,
      instascan: null,
      timer: null
    };
  },
  created() {
    this.groupId = this.$route.query.groupId;
    this.token = this.$route.query.token;
  },
  mounted() {
    this.initializeQrScanner();
  },
  methods: {
    initializeQrScanner() {
      const video = this.$refs.video;
      navigator.mediaDevices
        .getUserMedia({
          video: {
            facingMode: "environment"
          }
        })
        .then(stream => {
          video.srcObject = stream;
          video.onloadedmetadata = () => {
            video.play();
          };
          this.timer = setInterval(this.scanFrame, 1000 / 30);
        })
        .catch(error => {
          console.error("Error accessing camera", error);
        });
    },

    onleftClick() {
      this.$router.go(-1);
    },
    scanFrame() {
      const video = this.$refs.video;
      const canvasElement = this.$refs.canvas;
      const ctx = canvasElement.getContext("2d");
      if (video.readyState == video.HAVE_ENOUGH_DATA) {
        canvasElement.width = video.videoWidth;
        canvasElement.height = video.videoHeight;
        ctx.drawImage(video, 0, 0, canvasElement.width, canvasElement.height);

        const imageData = ctx.getImageData(
          0,
          0,
          canvasElement.width,
          canvasElement.height
        );
        const code = jsQR(imageData.data, imageData.width, imageData.height, {
          inversionAttempts: "dontInvert"
        });
        if (code) {
          console.log("扫描到的二维码内容：", code.data);
        }
      }
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
};
</script>

<style lang="scss" scoped>
.scan-container {
  width: 100vw;
  height: 100vh;
  margin: 0 auto;
}
</style>
