<template>
  <div>
    <van-popup
      v-model="show"
      position="right"
      :style="{ height: '100%', width: '100%' }"
    >
      <div class="add-eduction">
        <!-- title -->
        <div class="title">
          <van-icon name="cross" @click="closePopup" />
          <span class="title-name">{{
            editLink.id ? "编辑联系人" : "联系人"
          }}</span>
          <span v-if="!editLink.id" class="save" @click="save">保存</span>
          <span v-else></span>
        </div>
        <!-- main -->
        <main>
          <van-field
            class="must"
            readonly
            clickable
            name="picker"
            :value="relation"
            label="与本人关系"
            placeholder="请选择"
            @click="clickRelation"
          />
          <van-popup v-model="showRelation" position="bottom">
            <van-picker
              show-toolbar
              :columns="linkManRelationshipList"
              :default-index="linkManIndex"
              @confirm="onRelation"
              @cancel="showRelation = false"
            >
              <template #option="option">
                <div>{{ option.dname }}</div>
              </template>
            </van-picker>
          </van-popup>

          <van-field
            class="must"
            label="姓名"
            v-model="form.name"
            placeholder="请输入"
          />

          <van-field
            class="must"
            label="联系电话"
            v-model="form.phoneNo"
            placeholder="请输入"
          />

          <van-field
            class="no-must"
            readonly
            clickable
            name="area"
            :value="prefixAddress"
            label="联系地址"
            placeholder="请选择"
            @click="clickArea"
          />
          <van-popup v-model="showArea" position="bottom">
            <van-area
              :value="areaCode"
              :area-list="areaList"
              @confirm="onArea"
              @cancel="showArea = false"
              :columns-num="2"
            />
          </van-popup>

          <van-field
            class="no-must"
            label="详细地址"
            v-model="suffixAddress"
            placeholder="请输入"
          />
        </main>

        <!-- footer -->
        <footer v-show="editLink.id">
          <van-button
            type="primary"
            class="last"
            @click="deleteStaffRelationship"
            >删除</van-button
          >
          <van-button
            type="primary"
            class="next"
            @click="updateStaffRelationship"
            >完成</van-button
          >
        </footer>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  NavBar,
  Button,
  Divider,
  CellGroup,
  Field,
  Icon,
  Dialog,
  Picker,
  Popup,
  DatetimePicker,
  Area,
  Toast,
} from "vant";
// api
import {
  getAllAreaInfo,
  deleteStaffRelationship,
  updateStaffRelationship,
  saveStaffRelationship,
} from "../../../../api/hrm";
export default {
  components: {
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Divider.name]: Divider,
    [CellGroup.name]: CellGroup,
    [Field.name]: Field,
    [Icon.name]: Icon,
    [Picker.name]: Picker,
    [Popup.name]: Popup,
    [Dialog.Component.name]: Dialog.Component,
    [DatetimePicker.name]: DatetimePicker,
    [Area.name]: Area,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    linkManRelationshipList: {
      type: Array,
    },
    editLink: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      show: false,
      showRelation: false, //选择与本人关系
      showArea: false, //显示省市区弹框
      areaList: {
        province_list: {},
        city_list: {},
      },
      relation: "", //显示的关系
      form: {
        id: "",
        relation: "", //与本人关系
        phoneNo: "", //手机号
        name: "", //姓名
      },
      prefixAddress: "", //省市区地址
      suffixAddress: "", //详细地址

      linkManIndex:"",
      areaCode:"",
    };
  },
  watch: {
    value(val) {
      this.show = val;
    },
    show(val) {
      this.$emit("input", val);
      if (!val) {
        for (let i in this.form) {
          this.form[i] = "";
        }
        this.prefixAddress = ""; //省市区地址
        this.suffixAddress = ""; //详细地址
        this.relation = ""; //显示的关系清空
      }
    },
    editLink(val) {
      if (val) {
        this.form.relation = val.relation;
        this.form.name = val.name;
        this.form.phoneNo = val.phoneNo;
        this.prefixAddress = val.address.split("/").slice(0, 2).join("/");
        this.suffixAddress = val.address.split("/").slice(2).join("");
        let relationTemp = this.linkManRelationshipList.find((item) => {
          return item.value == val.relation;
        });
        this.relation = relationTemp.dname;
        this.form.id = val.id;
      }
    },

    showRelation(val) {
      if(!val) {
        this.linkManIndex = "";
      }
    },

    showArea(val) {
      if(!val) {
        this.areaCode = "";
      }
    }
  },
  mounted() {
    this.getAllAreaInfo();
  },
  methods: {
    closePopup() {
      this.show = false;
    },
    // 获取省市区地址列表
    async getAllAreaInfo() {
      try {
        const res = await getAllAreaInfo();
        // console.log(res , '地址信息');
        let obj = {};
        res.forEach((res) => {
          obj[res.code] = res.shortName;
        });
        this.areaList.province_list = obj;

        let city = {};
        res.forEach((item) => {
          item.children.forEach((ele) => {
            city[ele.code] = ele.shortName;
          });
        });
        this.areaList.city_list = city;
      } catch (error) {
        console.log(error);
      }
    },

    clickRelation(val) {
      this.linkManIndex = this.linkManRelationshipList.findIndex((item) => {
        return item.dname == val.target.value;
      })
      this.showRelation = true;
    },

    // 点击选择与本人关系
    onRelation(val) {
      // console.log(val);
      this.relation = val.dname;
      this.form.relation = val.value;
      this.showRelation = false;
    },

    clickArea(val) {
      let address = val.target.value;
      let province = address.split('/')[0];
      let city = address.split('/')[1];
      if(city) {
        this.areaCode = this.getObjectKey(this.areaList.city_list , city);
      } else {
        this.areaCode = this.getObjectKey(this.areaList.province_list , province);
      }
      this.showArea = true;
    },

    // 根据对象vlaue 值 去查 key
    getObjectKey (object,value) {
      return Object.keys(object).find(key=>object[key]==value);
    },

    // 点击选择省市区
    onArea(val) {
      this.prefixAddress = val[0].name + "/" + val[1].name;
      this.showArea = false;
    },

    // 保存联系人
    async save() {
      if(!this.form.relation || !this.form.phoneNo || !this.form.name) {
        return Toast.fail({
          duration: 2000,
          message: "请填写完整！",
        }); 
      }
      let obj = {
        ...this.form,
        staffId: this.$route.query.id,
        address: this.prefixAddress + "/" + this.suffixAddress,
      };
      try {
        const res = await saveStaffRelationship(obj);
        this.$emit("linkUpdata");
        console.log(res, "保存联系人");
        this.show = false;
      } catch (error) {
        console.log(error);
      }
    },

    // 更新联系人
    async updateStaffRelationship() {
      if(!this.form.relation || !this.form.phoneNo || !this.form.name) {
        return Toast.fail({
          duration: 2000,
          message: "请填写完整！",
        }); 
      }
      let obj = {
        ...this.form,
        staffId: this.$route.query.id,
        address: this.prefixAddress + "/" + this.suffixAddress,
      };
      try {
        const res = await updateStaffRelationship(obj);
        this.$emit("linkUpdata");
        console.log(res);
        this.show = false;
      } catch (error) {
        console.log(error);
      }
    },

    // 删除联系人
    async deleteStaffRelationship() {
      try {
        const res = await deleteStaffRelationship(this.editLink.id);
        this.$emit("linkUpdata");
        console.log(res);
        this.show = false;
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.add-eduction {
  height: 100%;
  display: flex;
  flex-direction: column;

  .title {
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;

    .title-name {
      // width: 68px;
      height: 24px;
      font-family: PingFangSC-S0pxibold;
      font-weight: 600;
      font-size: 17px;
      color: #333333;
      text-align: center;
    }
    .save {
      // width: 28px;
      height: 20px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #7f7f7f;
    }
    .save:hover {
      // width: 28px;
      height: 20px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #ff9900;
    }
  }

  main {
    flex: 1;
  }

  footer {
    height: 56px;
    background: #ffffff;
    box-shadow: 0 0 0 0 #f0f3fa;
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-top: 1px solid #f0f3fa;

    .last {
      background-color: #f0f0f0;
      border: 1px solid #f0f0f0;
      width: 138px;
      height: 40px;
      color: #7f7f7f;
      font-weight: 500;
      font-size: 14px;
      text-align: center;
      border-radius: 21px;
    }
    .next {
      width: 197px;
      height: 40px;
      background: #ff9900;
      border-radius: 21px;
      border: 1px solid #ff9900;
    }
  }
}

/deep/.van-cell {
  padding: 3.46667vw 4.26667vw;
}

.must {
  &::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
}

.no-must {
  &::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #fff;
  }
}
</style>