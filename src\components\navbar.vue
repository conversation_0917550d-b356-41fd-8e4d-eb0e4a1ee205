<template>
  <div class="nav">
    <img
      class="icon-back"
      src="../assets/service/ico_back.png"
      alt=""
      @click="goBack"
    />
    <P>{{ title }}</P>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    routerLink: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  methods: {
    goBack() {
      // 如果是地址管理页面，返回时记录当前页面
      if(this.$route.name == 'addressManage') {
        sessionStorage.setItem('path', this.$route.name);
      }
      if(this.routerLink) {
        // 抛出一个方法给父组件调用
        this.$emit('goBack');
      } else {
        this.$router.go(-1);
      }
      
    }
  }
};
</script>

<style lang="scss" scoped>
.nav {
  height: 44px;
  background-color: #fff;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  .icon-back {
    position: absolute;
    width: 20px;
    height: 20px;
    left: 16px;
  }

  p {
    height: 24px;
    font-family: PingFangSC-Medium;
    font-weight: 550;
    font-size: 16px;
    color: #000000;
    text-align: center;
    line-height: 24px;
  }
}
</style>