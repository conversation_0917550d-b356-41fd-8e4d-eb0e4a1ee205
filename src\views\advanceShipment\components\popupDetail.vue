<template>
  <van-popup
    v-model="show"
    position="right"
    :style="{ height: '100%', width: '100%' }"
  >
    <div class="popup">
      <div class="header">
        <img src="../../../assets/img/ico_back-t.png" @click="show = false" />
        <div class="title">申请提前发货</div>
        <div></div>
      </div>

      <div class="main">
        <div class="title-apply">
          <div class="title">
            <img
              style="width: 3px; height: 17px; margin-right: 10px"
              src="../../../assets/img/juxing.png"
              alt=""
            />
            申请信息
          </div>

          <div class="title-message">
            <div class="title-item-title">
              <p>申请提前发货原因</p>
              <div class="reason">{{ result.earlyDeliverReason }}</div>
            </div>
            <div class="title-item">
              <label for="">预计回款日期</label>
              <div class="content">{{ result.expectedPayTime }}</div>
            </div>
            <div class="title-item">
              <label for="">创建人</label>
              <div class="content">{{ result.contractUserName }}</div>
            </div>
            <div class="title-item">
              <label for="">创建时间</label>
              <div class="content">{{ result.contractCreateTime }}</div>
            </div>
          </div>
        </div>
        <div class="basic">
          <div class="basic-title">
            <img
              style="width: 3px; height: 17px; margin-right: 10px"
              src="../../../assets/img/juxing.png"
              alt=""
            />
            基础信息
          </div>

          <div class="basic-content">
            <div class="basic-item">
              <label for="">申请编号</label>
              <div class="content">{{ result.orderNo }}</div>
            </div>

            <div class="basic-item">
              <label for="">合同编号</label>
              <div class="content">{{ result.contractNo }}</div>
            </div>

            <div class="basic-item">
              <label for="">销售负责人</label>
              <div class="content">{{ result.contractUserName }}</div>
            </div>

            <div class="basic-item">
              <label for="">甲方名称</label>
              <div class="content">{{ result.customerName }}</div>
            </div>

            <div class="basic-item">
              <label for="">品牌名称</label>
              <div class="content">{{ result.straightClient }}</div>
            </div>

            <div class="basic-item">
              <label for="">签约时间</label>
              <div class="content">{{ result.signDate }}</div>
            </div>

            <div class="basic-item">
              <label for="">甲方代表</label>
              <div class="content">{{ result.signUser }}</div>
            </div>

            <div class="basic-item">
              <label for="">付款方式</label>
              <div class="content">{{ result.payType }}</div>
            </div>

            <div class="basic-item">
              <label for="">合同来源</label>
              <div class="content">{{ result.contractSource }}</div>
            </div>

            <div class="basic-item">
              <label for="">门店名称</label>
              <div class="content">{{ result.customerRemark }}</div>
            </div>

            <div class="basic-item">
              <label for="">保修期</label>
              <div class="content">{{ result.warrantyTime }}</div>
            </div>

            <div class="basic-item">
              <label for="">对应门店数</label>
              <div class="content">{{ result.shopCount }}</div>
            </div>

            <div class="basic-item">
              <label for="">项目情况说明(内部可见)</label>
              <div class="content">{{ result.projectRemark }}</div>
            </div>

            <div class="basic-item">
              <label for="">创建人</label>
              <div class="content">{{ result.contractUserName }}</div>
            </div>

            <div class="basic-item">
              <label for="">创建时间</label>
              <div class="content">{{ result.contractCreateTime }}</div>
            </div>
          </div>
        </div>

        <div class="message">
          <div class="message-title">
            <img
              style="width: 3px; height: 17px; margin-right: 10px"
              src="../../../assets/img/juxing.png"
              alt=""
            />
            审批信息
          </div>

          <div class="message-info" v-if="result.commentList">
            <div
              class="message-list"
              v-for="(item, index) in result.commentList"
              :key="index"
            >
              <div class="message-list-left">
                <img
                  v-if="item.commentList[0] && item.commentList[0].time"
                  src="../../../assets/img/dq.png"
                />
                <img v-else src="../../../assets/img/pass.png" />
                <div
                  v-if="index < result.commentList.length - 1"
                  :class="
                    item.commentList[0] && item.commentList[0].time
                      ? 'active-line'
                      : 'line'
                  "
                ></div>
              </div>

              <div class="message-list-right">
                <!-- 职位 -->
                <div class="job">{{ item.auditSite }}</div>
                <!-- 信息 -->
                <div class="right-message">
                  <div class="right-message-left">
                    <img
                      src="https://ovopark.oss-cn-hangzhou.aliyuncs.com/front/2022/05/31/HSx9RfCSfKFlMEV.png"
                    />
                    <span>{{
                      item.commentList[0] && item.commentList[0].userName
                    }}</span>
                  </div>
                  <div class="right-message-right">
                    <p>{{ item.commentList[0] && item.commentList[0].type }}</p>
                    <span>{{
                      item.commentList[0] && item.commentList[0].time
                    }}</span>
                  </div>
                </div>
                <!-- 审批信息 -->
                <div
                  class="message-text"
                  v-show="
                    item.commentList[0] && item.commentList[0].fullMessage
                  "
                >
                  {{ item.commentList[0] && item.commentList[0].fullMessage }}
                </div>

                <!-- 附件信息 -->
                <div
                  class="file-text"
                  v-show="
                    item.commentList[0] &&
                    item.commentList[0].auditAttachmentList &&
                    item.commentList[0].auditAttachmentList.length > 0
                  "
                  v-for="e in item.commentList[0].auditAttachmentList"
                  :key="e.id"
                  @click="downloadFile(e)"
                >
                  <img
                    class="icon"
                    :src="
                      ['jpg', 'jpeg', 'png', 'bmp'].includes(e.suffix)
                        ? 'https://ovopark.oss-cn-hangzhou.aliyuncs.com/front/2022/06/23/plO5l80N3aUi1Bh.png'
                        : ['pdf', 'PDF', 'pptx'].includes(e.suffix)
                        ? 'https://ovopark.oss-cn-hangzhou.aliyuncs.com/2023/06/29/zxfw_pdf.png'
                        : 'https://ovopark.oss-cn-hangzhou.aliyuncs.com/front/2022/06/23/EEvfcEDNZfvk2Im.png'
                    "
                  />
                  {{ e.filename }}
                </div>
              </div>
            </div>
          </div>

          <div class="message-info" v-else>暂无审批信息</div>
        </div>
      </div>

      <div class="footer">
        <div v-show="result.node && result.node !== 0 && result.node !== 10">
          <div class="message">
            <input
              :value="comment"
              type="text"
              placeholder="请填写审批意见"
              @input="commentChange"
            />
            <div>
              <upload-file
                ref="uploader"
                :isAttachment="true"
                :size="15 * 1024"
                :accept="accept"
                @on-upload-success="(val) => uploadBackSuccess(val)"
              ></upload-file>
            </div>
          </div>

          <div
            v-for="(a, aIndex) in attachmentList"
            :key="aIndex"
            class="fileList"
          >
            <img
              class="icon"
              :src="
                ['jpg', 'jpeg', 'png', 'bmp'].includes(a.fileSuffix)
                  ? 'https://ovopark.oss-cn-hangzhou.aliyuncs.com/front/2022/06/23/plO5l80N3aUi1Bh.png'
                  : ['pdf', 'PDF', 'pptx', 'ppt'].includes(a.fileSuffix)
                  ? 'https://ovopark.oss-cn-hangzhou.aliyuncs.com/2023/06/29/zxfw_pdf.png'
                  : 'https://ovopark.oss-cn-hangzhou.aliyuncs.com/front/2022/06/23/EEvfcEDNZfvk2Im.png'
              "
            />
            <p>{{ a.name }}</p>
            <img
              class="icon"
              src="../../../assets/hrm/ico_close.png"
              @click="deleteFile(aIndex)"
            />
          </div>

          <div class="btn">
            <div class="btn-box" @click="examine(1)">
              <img class="icon" src="../../../assets/img/ico_success.png" />
              同意
            </div>
            <div class="btn-box" @click="examine(0)">
              <img class="icon" src="../../../assets/img/ico_error.png" />
              驳回
            </div>
            <div class="btn-box">
              <van-popover
                v-model="showPopover"
                trigger="click"
                placement="top"
                @select="onSelect"
              >
                <div class="morePop">
                  <div class="countersign">
                    <van-button
                      style="width: 100%"
                      size="small"
                      @click="examine(2)"
                      >加签</van-button
                    >
                  </div>
                  <div class="countersign" v-show="row.status == 1">
                    <van-button
                      style="width: 100%"
                      size="small"
                      @click="revertApply"
                      >撤销审批</van-button
                    >
                  </div>

                  <div class="countersign" v-show="row.status == 4">
                    <van-button
                      style="width: 100%"
                      size="small"
                      @click="deleteApply"
                      >删除变更单</van-button
                    >
                  </div>
                </div>
                <template #reference>
                  <img
                    class="icon-more"
                    src="../../../assets/img/icon_more.png"
                  />
                  更多
                </template>
              </van-popover>
            </div>
          </div>
        </div>

        <div
          v-show="
            (row.status == 1 || row.status == 4) &&
            !(result.node && result.node !== 0 && result.node !== 10)
          "
          class="main-btn"
        >
          <van-button
            color="#FF9900"
            v-show="row.status == 1"
            style="width: 100%; border-radius: 8px"
            @click="revertApply"
            >撤销审批</van-button
          >
          <van-button
            type="danger"
            v-show="row.status == 4"
            style="width: 100%; border-radius: 8px"
            @click="deleteApply"
            >删除变更单</van-button
          >
        </div>
      </div>
    </div>

    <!--  -->
    <popupPerson
      v-model="showPerson"
      @sumbitPerson="sumbitPerson"
      @changePersonTo="changePersonTo"
    />
  </van-popup>
</template>

<script>
import {
  Popup,
  DatetimePicker,
  Field,
  ImagePreview,
  Checkbox,
  CheckboxGroup,
  button,
  Toast,
  Popover
} from "vant";
import {
  getEarlyOrderDetail,
  earlyOrderExamine,
  cancelEarlyApply,
  removeEarlyApply
} from "@/api/advanceShip";
import UploadFile from "../../../components/upload-file/upload-file.vue";
import popupPerson from "./popupPerson.vue";
export default {
  components: {
    [Popup.name]: Popup,
    [DatetimePicker.name]: DatetimePicker,
    [Field.name]: Field,
    [ImagePreview.name]: ImagePreview,
    [Checkbox.name]: Checkbox,
    [CheckboxGroup.name]: CheckboxGroup,
    [button.name]: button,
    [Toast.name]: Toast,
    [Popover.name]: Popover,
    UploadFile,
    popupPerson
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    id: {
      type: [String, Number],
      default: ""
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      showPerson: false,
      isChecked: false,
      showPopover: false,
      result: {},
      attachmentList: [],
      accept:
        "image/jpeg,image/jpg,image/png,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation",
      agree: "", //审核（0拒绝、1通过、2加签）
      comment: "", //审核意见
      copyUserList: [], //抄送人员code集合
      usercode: "" //当前登录人code
    };
  },
  watch: {
    value(val) {
      this.show = val;
    },
    show(val) {
      this.$emit("input", val);
      if (!val) {
        this.attachmentList = [];
        this.comment = "";
        this.agree = "";
        this.copyUserList = [];
        this.$refs.uploader.uploadList = [];
      }
    },
    id(val) {
      this.getEarlyOrderDetail(val);
    }
  },

  mounted() {
    this.usercode = this.$route.query?.usercode;
  },

  methods: {
    onSelect() {},
    // 获取提前发货详情
    async getEarlyOrderDetail(id) {
      try {
        const res = await getEarlyOrderDetail(id);
        this.result = res;
        let tempList = [];
        tempList = res.commentList?.filter((item) => {
          return !(
            item.node === 0 &&
            !item.time &&
            item.taskName === "发货申请"
          );
        });
        tempList &&
          tempList.forEach((v) => {
            !v.time &&
              v.commentList.unshift({
                userName: v.userName,
                time: v.time || "",
                fullMessage: "",
                auditSite: v.auditSite,
                auditAttachmentList: [],
                type: "未审核"
              });
          });
        this.result.commentList = tempList;
      } catch (error) {
        console.log(error);
      }
    },

    // 上传附件回调
    uploadBackSuccess(val) {
      this.attachmentList.push(val);
    },

    // 删除上传附件
    deleteFile(index) {
      this.attachmentList.splice(index, 1);
      this.$refs.uploader.uploadList.splice(index, 1);
    },

    // 下载预览附件
    downloadFile(e) {
      if (["jpg", "jpeg", "png", "bmp"].includes(e.suffix)) {
        ImagePreview({
          images: [e.fileurl],
          startPosition: 0
        });
      } else {
        window.location.href = e.fileurl;
      }
    },

    // 审批内容
    commentChange(e) {
      this.comment = e.target.value;
    },

    // 加签人员回调
    sumbitPerson(val) {
      this.earlyOrderExamine(val);
    },

    // 抄送人员回调
    changePersonTo(val) {
      this.copyUserList = val;
      let str = "";
      val.forEach((item, index) => {
        //将第一个@过滤掉
        if (index === 0) {
          str += item.username;
        } else {
          str += "@" + item.username;
        }
      });
      this.comment = this.comment + str;
    },

    // 提交审核
    examine(type) {
      this.agree = type;
      if (type === 2) {
        this.showPerson = true;
      } else {
        this.earlyOrderExamine();
      }
      this.showPopover = false;
    },

    // 提前发货审核接口
    async earlyOrderExamine(assigns) {
      if (!this.comment) {
        this.$toast("请填写审批意见");
        return;
      }
      // 对附件信息进行处理
      let commentAttachmentList = this.attachmentList?.map((item) => {
        return {
          filename: item.name,
          filetype: item.fileType,
          fileurl: item.fileurl,
          suffix: item.fileSuffix
        };
      });
      const params = {
        id: this.id,
        agree: this.agree,
        assigns: assigns || "", //加签人员code集合
        comment: this.comment,
        node: this.result.node,
        copyUserList: this.copyUserList || [], //抄送人员code集合
        commentAttachmentList: commentAttachmentList || [],
        usercode: this.usercode
      };
      try {
        await earlyOrderExamine(params);
        Toast.success("提交成功！");
        this.show = false;
        this.$emit("update");
      } catch (error) {
        console.log(error);
      }
    },

    // 撤销审批
    async revertApply() {
      try {
        await cancelEarlyApply({
          id: this.id
        });
        this.show = false;
        Toast.success("撤销成功！");
        this.$emit("update");
      } catch (err) {
        console.log(err);
      }
    },

    // 删除变更单
    async deleteApply() {
      try {
        await removeEarlyApply({
          id: this.id
        });
        this.show = false;
        Toast.success("撤销成功！");
        this.$emit("update");
      } catch (err) {
        console.log(err);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>