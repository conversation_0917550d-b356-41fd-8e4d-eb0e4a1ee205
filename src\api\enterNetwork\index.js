import urls from "./config";
import { get, postNoToken } from "../request";

export function grantAuthorization(params) {
  return get(urls.activeAuthorizationCode, params);
}

export function login(params) {
  return postNoToken(urls.login, params);
}

export function getTvWallAuthorizationCodeList(params) {
  return get(urls.getTvWallAuthorizationCodeList, params);
}

export function unbindTvWallAuthorizationCode(params) {
  return get(urls.unbindTvWallAuthorizationCode, params);
}
