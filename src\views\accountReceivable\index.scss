.receivable {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f7f7f7;

  background-image: url("../../assets//img/bg.png");
  background-repeat: no-repeat;
  background-size: 375px 240px;
  overflow: hidden;

  header {
    padding: 0 16px;
    .title {
      height: 44px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #000000;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .icon_back {
        width: 20px;
        height: 20px;
      }
    }

    .collect {
      margin: 12px 0;
      padding: 16px 0;
      background: #ffffff;
      border-radius: 6px;

      .collect_title {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 13px;
        color: #7f7f7f;
        line-height: 16px;
        padding: 0 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .icon_back {
          width: 20px;
          height: 20px;
        }
      }

      .collect_body {
        display: flex;
        justify-content: space-between;
        margin-top: 12px;

        .collect_body_list {
          width: 50%;
          display: flex;
          flex-direction: column;
          padding-left: 16px;

          .amount {
            height: 32px;
            font-family: D-DIN-PRO-SNaNpxiBold;
            font-weight: 600;
            font-size: 24px;
            color: #333333;
            letter-spacing: 0;
            line-height: 32px;

            span {
              width: 12px;
              height: 16px;
              font-family: PingFangSC-Regular;
              font-weight: 400;
              font-size: 12px;
              color: #333333;
              letter-spacing: 0;
              line-height: 16px;
            }
          }

          .content {
            height: 16px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 13px;
            color: #7f7f7f;
            letter-spacing: 0;
            line-height: 16px;
            margin-top: 4px;
          }
        }
      }
    }
  }

  .selected_hierarchy {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px 12px 16px;
    .selected_name {
      flex: 1;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 13px;
      color: #7f7f7f;
      display: flex;
      align-items: center;

      .selected_name_item {
        display: flex;
        align-items: center;
      }

      img {
        width: 12px;
        height: 12px;
        margin: 0 3px;
      }
    }
  }

  main {
    display: flex;
    flex-direction: column;
    padding: 0 16px 16px 16px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .popSearch {
    padding: 0 16px;
    .title {
      height: 44px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #000000;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .icon_back {
        width: 20px;
        height: 20px;
      }
    }
  }
}
