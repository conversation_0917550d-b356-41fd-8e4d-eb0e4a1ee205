<template>
  <div class="receivable">
    <header>
      <!-- title -->
      <!-- <div class="title">
        <img
          class="icon_back"
          src="../../assets/ico_back.png"
          alt=""
          @click="onBackApp"
        />
        <div>应收账款</div>
        <div @click="show = true">
          <img class="icon_back" src="../../assets/ico_filter.png" alt="" />
        </div>
      </div> -->

      <!-- tabs -->
      <van-tabs
        v-if="$route.query.isSuper != 1"
        v-model="type"
        background="rgba(255, 255, 255, 0)"
        color="#FF9900"
        title-active-color="#333333"
        line-width="28"
        line-height="2"
        @change="changeTabs"
      >
        <van-tab title="组织架构" :name="0"></van-tab>
        <!-- <van-tab title="战队" :name="1"></van-tab> -->
        <van-tab title="个人" :name="2"></van-tab>
      </van-tabs>

      <!-- 汇总 -->
      <div class="collect">
        <div class="collect_title">
          应收账款汇总
          <div @click="show = true">
            <img class="icon_back" src="../../assets/ico_filter.png" alt="" />
          </div>
        </div>
        <div class="collect_body">
          <div class="collect_body_list">
            <div class="amount">
              {{ $store.state.arrearsAmount }} <span>万</span>
            </div>
            <div class="content">应收欠款总额</div>
          </div>
          <div class="collect_body_list">
            <div class="amount">
              {{ $store.state.overdueMoney }} <span>万</span>
            </div>
            <div class="content">已逾期</div>
          </div>
        </div>
      </div>
    </header>

    <div
      class="selected_hierarchy"
      v-if="$store.state.selectedHistory.length > 0"
    >
      <div class="selected_name">
        <div
          class="selected_name_item"
          v-for="(e, eIndex) in $store.state.selectedHistory"
          :key="eIndex"
          @click="changeSelected(eIndex, e)"
        >
          <div>
            {{ e.name }}
          </div>
          <img
            v-if="
              $store.state.selectedHistory.length > 1 &&
                eIndex !== $store.state.selectedHistory.length - 1
            "
            src="@/assets/img/ico_rightarrow.png"
            alt=""
          />
        </div>
      </div>
      <img
        style="width: 14px; height: 14px;"
        src="@/assets/img/ico_clean.png"
        @click="cleanHistory"
      />
    </div>

    <main>
      <transition :name="transitionName">
        <router-view :type="type" :isSuper="isSuper" />
      </transition>
    </main>

    <!-- 时间筛选 -->
    <timePop v-model="show" @sureClick="sureClick" />
  </div>
</template>
<script>
import { Tab, Tabs, Toast, Popup } from "vant";
import timePop from "./components/timePop.vue";
export default {
  components: {
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
    [Toast.name]: Toast,
    [Popup.name]: Popup,
    timePop
  },
  props: {},
  data() {
    return {
      type: 0,
      transitionName: "",
      show: false,
      isSuper: ""
    };
  },
  created() {
    if (this.$route.query.isSuper == 1) {
      this.type = 2;
    } else {
      this.type = 0;
    }
    this.isSuper = this.$route.query.isSuper;
  },
  watch: {
    $route(to, from) {
      //实现路由跳转动画
      if (to.meta.index > from.meta.index) this.transitionName = "slide-left";
      if (to.meta.index < from.meta.index) this.transitionName = "slide-right";
    }
  },
  methods: {
    changeTabs() {
      // 切换tabs时候，回到最外层
      this.cleanHistory();
    },

    // 筛选时间回调值
    sureClick(val) {
      this.$store.commit("setAccountDate", val);
    },

    changeSelected(index, item) {
      if (item.index == 1) {
        this.$router.replace({
          name: "accountFirst"
        });
      } else if (item.index == 2) {
        this.$router.replace({
          name: "accountSecond",
          query: {
            type: item.type,
            depId: item.depId
          }
        });
      } else if (item.index == 3) {
        console.log(item);
        this.$router.replace({
          name: "accountThird",
          query: {
            type: item.type,
            depId: item.depId
          }
        });
      }
      // // 点击历史记录，回到对应的层级 且删除掉后面的历史记录
      this.$store.commit("deleteSelectedHistory", index);
    },

    cleanHistory() {
      // 清空历史记录
      this.$store.commit("cleanHistory");
      // 清除后返回到最外层
      this.$router.replace({
        name: "accountFirst"
      });
    },

    onBackApp() {
      // 返回app
      this.backToApp();
    }
  }
};
</script>
<style scoped lang="scss">
@import "./index.scss";

::v-deep .van-tab--active {
  font-family: PingFangSC-SNaNpxibold;
  font-weight: 600;
  font-size: 16px;
  color: #333333;
}

.slide-right-enter-active,
.slide-right-leave-active,
.slide-left-enter-active,
.slide-left-leave-active {
  will-change: transform;
  transition: all 0.5s;
  position: absolute;
}
.slide-right-enter {
  opacity: 0;
  transform: translate(-100%);
}
.slide-right-leave-active {
  opacity: 0;
  transform: translateX(100%);
}
.slide-left-enter {
  opacity: 0;
  transform: translateX(100%);
}
.slide-left-leave-active {
  opacity: 0;
  transform: translateX(-100%);
}
</style>
