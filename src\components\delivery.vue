<template>
  <div id="container" class="delivery" :style="{ height: screenHeight }">
    <van-nav-bar v-if="!isPc" title="物流详情" left-arrow @click-left="onClickLeft" />
    <div class="search">
      <!-- <select class="sel">
                <option value="">客户名称</option>
                <option value="">合同号</option>
            </select> -->
      <van-field ref="ipt" v-model="keyword" rows="1" placeholder="输入客户名称/合同号/物流单号查询物流进度" clearable left-icon="search" @keydown.enter="search" />
    </div>
    <div class="noInfo" v-if="!info || !info.length">
      <img :src="require('../assets/kb.png')" />
      <p>上方搜索关键词，快速查询物流信息～</p>
    </div>

    <p v-if="info && info.length" class="main-tit">{{ packageNumber || '--' }}个包裹已发出</p>
    <main id="box" v-if="info && info.length">
      <div class="main" v-for="item in info" :key="item.id">
        <!-- 信息 -->
        <div class="info" @click="goDetail(item)">
          <div class="info-tit">
            <span class="status">{{ item.logisticsCompany }}</span>
            <span class="number">主单号:{{ item.logisticsNumber | numSplit }}</span>
          </div>
          <div class="receiver-info">
            <label>合同编号</label><span>{{ item.contractNo || '--' }}</span>
          </div>
          <div class="receiver-info">
            <label>客户名称</label><span>{{ item.customerName || '--' }}</span>
          </div>
          <div class="receiver-info">
            <label>收货人</label><span>{{ item.linkMan || '--' }}</span>
          </div>
          <!-- <div class="receiver-info">
                        <label>联系方式</label><span>{{info.linkPhone || '--'}}</span> 
                    </div> -->
          <div class="receiver-info">
            <label>收货地址</label><span>{{ item.receiveAddress || '--' }}</span>
          </div>
        </div>
        <!-- <div class="info no-border">
                    <h3>产品信息</h3>
                    <delivery-table :tableData="tableData"></delivery-table>
                </div>  -->
      </div>
    </main>

    <!-- loaidng -->
    <!-- <van-loading v-if="loading"/> -->

    <!-- back top -->
    <van-icon name="back-top" class="backTop" @click="backTop" />
  </div>
</template>

<script>
import { NavBar, Field, Loading, Icon } from 'vant';
// import deliveryTable from "./deliveryTable";
import { searchLogisticsProcess } from '../api/delivery';

export default {
  name: 'delivery',
  components: {
    [NavBar.name]: NavBar,
    [Field.name]: Field,
    [Loading.name]: Loading,
    [Icon.name]: Icon
    // deliveryTable
  },

  beforeMount() {
    const device = this.$route.query.device;
    this.isPc = device === 'pc' ? true : false;
  },

  mounted() {
    const keyword = this.$route.query.keyword;
    this.keyword = keyword;
    this.getinfo();

    this.screenHeight = window.screen.availHeight + 'px';
    // console.log(window.screen.availHeight, window.screen.height);
    let con = document.getElementById('container');
    con && con.addEventListener('scroll', this.handleScroll);
  },

  destroyed() {
    document.removeEventListener('scroll', this.handleScroll);
  },
  data() {
    return {
      isPc: false,
      packageNumber: 0, //包裹数
      info: [], //信息
      screenHeight: '',
      keyword: '', //搜索参数
      pageSize: 5, //
      loading: false //加载loading
    };
  },
  methods: {
    async getinfo() {
      this.loading = true;
      const params = {
        keyword: this.keyword,
        pageNumber: 1,
        pageSize: this.pageSize
      };
      const res = await searchLogisticsProcess(params);
      this.packageNumber = res.total;
      this.info = res.list;
      this.loading = false;
    },

    search() {
      if (!this.keyword) {
        this.info = [];
      } else {
        this.getinfo();
        this.$refs.ipt.blur();
      }
    },
    /**
     * 查看详情
     */
    goDetail(obj) {
      const query = {
        id: obj.id,
        keyword: this.keyword
      };
      if (obj.logisticsCompany && obj.logisticsCompany.indexOf('顺丰') !== -1) {
        query.customerName = obj.linkPhone.substring(obj.linkPhone.length - 4);
      }
      if (obj.logisticsCompany && obj.logisticsCompany.indexOf('跨越') !== -1) {
        query.customerName = obj.linkPhone.substring(obj.linkPhone.length - 4);
      }
      this.$router.push({ name: 'details', query });
    },

    /**
     * 监听滚动
     */
    handleScroll() {
      // let height = document.getElementById('box').offsetHeight;   //包裹list的盒子
      let scrollTop = document.getElementById('container').scrollTop; //滚动高度
      let screenHeight = window.screen.availHeight; //屏幕高度
      let listHeight = document.getElementsByClassName('main')[0] && document.getElementsByClassName('main')[0].offsetTop; //第一个card的距离顶部距离
      let cardHeight = document.getElementsByClassName('info')[0] && document.getElementsByClassName('info')[0].clientHeight - 15; //第一个card的高度
      let h = cardHeight * 5 + listHeight - screenHeight + (this.pageSize - 5) * cardHeight; //list的高度+距离顶部的高度=全部高度-屏幕高度就是上滚动距离
      console.log(scrollTop, h);
      if (scrollTop > h) {
        // console.log();
        this.pageSize += 5;
        this.getinfo();
      }
    },

    backTop() {
      document.getElementById('container').scrollTop = 0;
    },

    /**
     * 返回app
     */
    onClickLeft() {
      this.backToApp();
    }
  },
  filters: {
    numSplit(val) {
      return val.split(',')[0];
    }
  }
};
</script>

<style lang="scss" scoped>
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
.delivery {
  width: 100%;
  // overflow: auto;
}
.main-tit {
  line-height: 30px;
  background: rgba(255, 153, 0, 0.1);
  text-indent: 10px;
  color: #ff9900;
  font-size: 14px;
}
main {
  // height:  calc( 100vh - 17.5vw);
  padding: 0 12px 0;

  .info {
    background-color: #fff;
    margin-top: 14px;
    padding-bottom: 10px;
    font-size: 14px;
    border-radius: 8px;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.04);

    .info-tit {
      padding: 13px;
      background-color: #f6faff;
      border-radius: 8px;

      color: #8acc47;
      .number {
        float: right;
        color: rgba(0, 0, 0, 0.5);
      }
    }
    .receiver-info {
      margin: 14px 0 12px 0;
      padding-left: 100px;
      position: relative;
      label {
        color: rgba(0, 0, 0, 0.3);
        position: absolute;
        left: 16px;
      }
    }
    h3 {
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      font-size: 16px;
      text-indent: 20px;
    }
    h3::before {
      content: '';
      width: 4px;
      height: 18px;
      background: #ff9900;
      border-radius: 1px;
      position: absolute;
      left: 20px;
    }
  }
}
.search {
  padding: 10px 16px;
  background-color: #fff;
  display: flex;

  // .sel{
  //     background-color: #f0f0f0;
  //     border:none
  // }
  // .sel:focus {
  //     outline:none !important;
  // }
  /deep/ .van-cell {
    height: 36px;
    background-color: #f0f0f0;
    padding: 7px 11px;
    // border-radius:0 6px 6px 0;
    border-radius: 6px;
  }
}
.noInfo {
  width: 100%;
  height: 100%;
  position: relative;
  img {
    width: 214px;
    height: 148px;
    // margin: auto;
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  p {
    width: 100%;
    text-align: center;
    position: absolute;
    top: 45%;
    font-size: 13px;
    color: #b2b2b2;
  }
}
.backTop {
  position: fixed;
  bottom: 50px;
  right: 10px;
  font-size: 24px;
  color: #ccc;
}

/deep/ .van-nav-bar .van-icon {
  color: #333;
  font-size: 18px;
}
</style>
