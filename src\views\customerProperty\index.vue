<template>
  <div class="customer-property">
    <header>
      <van-nav-bar left-arrow @click-left="onClickLeft" title="客户属性分析" />
      <div class="search-bar">
        <van-search
          @search="onSearch"
          v-model.trim="query.searchWord"
          placeholder="请输入企业名称/产品名称/品牌名称"
        />
      </div>
      <van-dropdown-menu active-color="#FF9900">
        <!-- 业务联系 -->
        <van-dropdown-item :title="titleA" ref="itemA">
          <div class="list">
            <div class="main">
              <div
                class="main_item"
                v-for="(item, index) in productList"
                :key="index"
                @click="changeLineService(item.id)"
              >
                <p :class="activeIndexArr.includes(item.id) ? 'active' : ''">
                  {{ item.productName }}
                </p>

                <van-icon
                  v-if="activeIndexArr.includes(item.id)"
                  color="#FF9900"
                  name="success"
                />
              </div>
            </div>
            <div class="footer">
              <van-button class="rect" @click="reset">重置</van-button>
              <van-button class="search" @click="search">确定</van-button>
            </div>
          </div>
        </van-dropdown-item>

        <!-- 行业 -->
        <van-dropdown-item :title="titleB" ref="itemB">
          <div class="list">
            <div class="main">
              <div
                class="main_item"
                v-for="item in industryList"
                :key="item.value"
                @click="changeIndustry(item.value)"
              >
                <p
                  :class="
                    activeIndustryArr.includes(item.value) ? 'active' : ''
                  "
                >
                  {{ item.text }}
                </p>

                <van-icon
                  v-if="activeIndustryArr.includes(item.value)"
                  color="#FF9900"
                  name="success"
                />
              </div>
            </div>
            <div class="footer">
              <van-button class="rect" @click="reset">重置</van-button>
              <van-button class="search" @click="search">确定</van-button>
            </div>
          </div>
        </van-dropdown-item>

        <!-- 区域 -->
        <van-dropdown-item :title="titleC" ref="itemC">
          <div class="list">
            <div class="main">
              <div
                class="main_item"
                v-for="item in areaLists"
                :key="item.value"
                @click="changeArea(item.text)"
              >
                <p :class="activeAreaArr.includes(item.text) ? 'active' : ''">
                  {{ item.text }}
                </p>

                <van-icon
                  v-if="activeAreaArr.includes(item.text)"
                  color="#FF9900"
                  name="success"
                />
              </div>
            </div>
            <div class="footer">
              <van-button class="rect" @click="reset">重置</van-button>
              <van-button class="search" @click="search">确定</van-button>
            </div>
          </div>
        </van-dropdown-item>

        <!-- 来源 -->
        <van-dropdown-item :title="titleD" ref="itemD">
          <div class="list">
            <div class="main">
              <!-- <div class="source">
                <div class="source_right">
                  <div
                    :class="
                      activeSourceArr.includes(e.value)
                        ? 'active_child_name'
                        : 'childrenName'
                    "
                    v-for="e in sourceLists"
                    :key="e.value"
                    @click="changeSourceChild(e.value)"
                  >
                    {{ e.text }}

                    <van-icon
                      v-if="activeSourceArr.includes(e.value)"
                      color="#FF9900"
                      name="success"
                    />
                  </div>
                </div>
              </div> -->

              <van-tree-select
                height="375"
                :items="sourceLists"
                :active-id.sync="activeSourceArr"
                :main-active-index.sync="activeIndex"
              />
            </div>
            <div class="footer">
              <van-button class="rect" @click="reset">重置</van-button>
              <van-button class="search" @click="search">确定</van-button>
            </div>
          </div>
        </van-dropdown-item>

        <!-- 更多 -->
        <van-dropdown-item title="更多" ref="itemE">
          <div class="list">
            <div class="main">
              <p>合作状态</p>
              <div class="main_item_status">
                <div
                  :class="
                    activeCooprateArr.includes(item.value)
                      ? 'active_item_status'
                      : 'item_status'
                  "
                  v-for="item in cooprateStatusList"
                  :key="item.value"
                  @click="changeCooprate(item.value)"
                >
                  {{ item.text }}
                </div>
              </div>
              <p>门店数量</p>
              <div class="main_item_status">
                <div
                  :class="
                    activeupOr == item.value
                      ? 'active_item_status'
                      : 'item_status'
                  "
                  v-for="item in upOrDownList"
                  :key="item.value"
                  @click="changeupOr(item.value)"
                >
                  {{ item.text }}
                </div>
              </div>
            </div>
            <div class="footer">
              <van-button class="rect" @click="reset">重置</van-button>
              <van-button class="search" @click="search">确定</van-button>
            </div>
          </div>
        </van-dropdown-item>
      </van-dropdown-menu>
    </header>

    <!-- main -->
    <van-pull-refresh v-model="loading" @refresh="onRefresh" class="main">
      <van-list
        class="list-wrap"
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        v-if="businessList.length > 0"
      >
        <div
          class="customer-brief"
          v-for="(item, index) in businessList"
          :key="index"
          @click="goDetail(item)"
        >
          <div class="name">{{ item.customerName }}</div>
          <div class="brief_item">
            <div class="label">业务联系</div>
            <div class="buss-txt" v-html="item.softWareNames"></div>
          </div>
          <div class="brief_item">
            <div class="label">门店数量</div>
            <div class="buss-txt">{{ item.shopCount }}</div>
          </div>
        </div>
        <!-- </div> -->
      </van-list>
      <div class="list-wrap" v-else>
        <van-empty description="暂无数据" />
      </div>
    </van-pull-refresh>

    <!-- 详情弹窗 -->
    <detail v-model="showDetail" :detailId="detailId" />
  </div>
</template>

<script>
import {
  Toast,
  NavBar,
  Search,
  DropdownMenu,
  DropdownItem,
  PullRefresh,
  Button,
  TreeSelect,
  Icon,
  Empty,
  list
} from "vant";
import {
  getCustomerInfoAnalysis,
  getAllDict,
  getProductLineServiceList
} from "api/customerProperty";

import detail from "./components/detail.vue";

export default {
  components: {
    [NavBar.name]: NavBar,
    [Search.name]: Search,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    [PullRefresh.name]: PullRefresh,
    [Button.name]: Button,
    [TreeSelect.name]: TreeSelect,
    [Toast.name]: Toast,
    [Icon.name]: Icon,
    [Empty.name]: Empty,
    [list.name]: list,
    detail
  },
  data() {
    return {
      showDetail: false,
      detailId: "",
      activeIndexArr: [], //业务联系选中状态数组
      activeIndustryArr: [], //行业选中状态数组
      activeCooprateArr: [], //合作状态选中状态数组
      activeSourceArr: [], //来源选中状态数组
      activeupOr: "", //门店数量选中状态（为单选）
      activeAreaArr: [], //区域选中状态数组
      titleA: "业务联系",
      titleB: "行业",
      titleC: "区域",
      titleD: "来源",
      loading: false,
      productList: [], //业务联系
      industryList: [], //行业
      areaLists: [
        //区域写死
        { text: "华北", value: 1 },
        { text: "西部", value: 2 },
        { text: "华东", value: 3 },
        { text: "华中", value: 4 },
        { text: "华南", value: 5 },
        { text: "海外", value: 6 }
      ],
      sourceLists: [], //来源
      cooprateStatusList: [],
      upOrDownList: [
        { text: "由低到高", value: "up" },
        { text: "由高到低", value: "down" }
      ],
      query: {
        searchWord: ""
      },

      page: {
        no: 1,
        limit: 10
      },
      total: 0, //总数据量
      businessList: [],
      finished: false,
      activeIndex: 0
    };
  },
  computed: {},
  watch: {
    activeSourceArr: {
      handler(val) {
        let arr = val.reduce((acc, item) => {
          this.sourceLists.forEach(e => {
            if (item === e.id) {
              acc.push(e.text);
            } else if (e.children) {
              e.children.forEach(child => {
                if (item === child.id) {
                  acc.push(child.text);
                }
              });
            }
          });
          return acc;
        }, []);

        this.titleD = arr.join(",") || "来源";
      },
      deep: true
    }
  },

  mounted() {
    this.getAllDict();
    this.getCustomerList();
    this.getProductLineServiceList();
  },
  methods: {
    async getAllDict() {
      const res = await getAllDict();
      this.industryList =
        res
          .find(item => item.type === "customer_industry")
          ?.children?.map(item => {
            return {
              text: item.dname,
              value: item.value
            };
          }) || [];

      this.cooprateStatusList =
        res
          .find(item => item.type === "cooperation_status")
          ?.children?.map(item => {
            return {
              text: item.dname,
              value: item.value
            };
          }) || [];

      let moreSource = { text: "更多来源", id: "more", children: [] };

      let sourceLists =
        res
          .find(item => item.type === "cluing_source")
          ?.children?.reduce((acc, { dname: text, value: id, children }) => {
            if (children) {
              acc.push({
                text,
                id,
                children: children.map(({ dname: text, value: id }) => ({
                  text,
                  id
                }))
              });
            } else {
              moreSource.children.push({ text, id });
            }
            return acc;
          }, []) || [];

      if (moreSource.children.length > 0) {
        sourceLists.push(moreSource);
      }

      this.sourceLists = sourceLists;

      console.log(this.sourceLists);
    },

    // 获取产品线服务列表
    async getProductLineServiceList() {
      try {
        const res = await getProductLineServiceList();
        this.productList = res || [];
      } catch (error) {
        console.log(error);
      }
    },

    onRefresh() {
      this.finished = false;
      this.page.no = 1;
      this.getCustomerList();
    },

    onSearch() {
      this.page.no = 1;
      this.getCustomerList();
    },

    // 返回上一页
    onClickLeft() {
      this.backToApp();
    },

    onLoad() {
      this.page.no++;
      this.getCustomerList();
    },

    // 跳转详情页
    goDetail(item) {
      this.showDetail = true;
      this.detailId = item.id;
    },

    // 重置
    reset() {
      this.activeIndexArr = [];
      this.activeIndustryArr = [];
      this.activeAreaArr = [];
      this.activeSourceArr = []; //来源
      this.activeCooprateArr = [];
      this.activeupOr = "";
      this.titleA = "业务联系";
      this.titleB = "行业";
      this.titleC = "区域";
      this.titleD = "来源";
      this.page.no = 1;
      this.getCustomerList();
      this.close();
    },

    // 查询
    search() {
      this.page.no = 1;
      this.getCustomerList();
      this.close();
    },

    // 关闭下拉框
    close() {
      this.$refs.itemA.toggle(false);
      this.$refs.itemB.toggle(false);
      this.$refs.itemC.toggle(false);
      this.$refs.itemD.toggle(false);
      this.$refs.itemE.toggle(false);
    },

    //获取列表
    async getCustomerList() {
      try {
        this.loading = true;
        const res = await getCustomerInfoAnalysis({
          ...this.page,
          ...this.query,
          softWareLine: this.activeIndexArr?.join(","), //业务联系
          industry: this.activeIndustryArr?.join(","), //  行业
          areas: this.activeAreaArr?.join(","), // 区域
          cluingSource: this.activeSourceArr?.join(","), //来源
          upOrDown: this.activeupOr, //门店数量
          cooperationStatus: this.activeCooprateArr?.join(",") // 合作状态
        });
        let list = [];
        const newList = this.processRecords(res.records, this.query.searchWord); //处理数据
        if (this.page.no == 1) {
          list = newList;
        } else {
          list = this.businessList.concat(
            this.processRecords(res.records, this.query.searchWord)
          );
        }
        this.total = parseInt(res.total) || 0;
        this.businessList = list;
        this.loading = false;
        if (this.businessList.length >= this.total) {
          this.finished = true;
        } else {
          this.finished = false;
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },

    //处理shuju
    processRecords(records, searchWord) {
      return records.map(item => {
        let softWareNames;
        if (!searchWord) {
          softWareNames = `<span>${item.softWareNames}</span>`;
        } else {
          let keywords = searchWord;
          let escapedKeywords = this.escapeRegExp(keywords);
          let regex = new RegExp(escapedKeywords, "g");
          let result = item.softWareNames.replace(
            regex,
            match => `<b class="high-light">${match}</b>`
          );
          softWareNames = `<span>${result}</span>`;
        }
        return { ...item, softWareNames };
      });
    },

    escapeRegExp(string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"); // $& 表示整个匹配的字符串
    },

    // 选择业务联系
    changeLineService(id) {
      if (this.activeIndexArr.includes(id)) {
        this.activeIndexArr = this.activeIndexArr.filter(item => item !== id);
      } else {
        this.activeIndexArr.push(id);
      }
      // 勾选时候改变标题 多选时候拼接起来(根据id找到对应的名称)
      let productNames = this.productList
        .filter(item => this.activeIndexArr.includes(item.id))
        .map(item => item.productName);

      this.titleA =
        productNames.length > 0 ? productNames.join(",") : "业务联系";
    },

    // 选择行业
    changeIndustry(val) {
      if (this.activeIndustryArr.includes(val)) {
        this.activeIndustryArr = this.activeIndustryArr.filter(
          item => item !== val
        );
      } else {
        this.activeIndustryArr.push(val);
      }

      let industryNames = this.industryList
        .filter(item => this.activeIndustryArr.includes(item.value))
        .map(item => item.text);

      this.titleB = industryNames.length > 0 ? industryNames.join(",") : "行业";
    },

    // 选择区域
    changeArea(text) {
      if (this.activeAreaArr.includes(text)) {
        this.activeAreaArr = this.activeAreaArr.filter(item => item !== text);
      } else {
        this.activeAreaArr.push(text);
      }

      let areaNames = this.areaLists
        .filter(item => this.activeAreaArr.includes(item.text))
        .map(item => item.text);

      this.titleC = areaNames.length > 0 ? areaNames.join(",") : "区域";
    },

    // 选择来源
    // changeSourceChild(val) {
    //   if (this.activeSourceArr.includes(val)) {
    //     this.activeSourceArr = this.activeSourceArr.filter(
    //       item => item !== val
    //     );
    //   } else {
    //     this.activeSourceArr.push(val);
    //   }
    // },

    // 选择合作状态
    changeCooprate(val) {
      if (this.activeCooprateArr.includes(val)) {
        this.activeCooprateArr = this.activeCooprateArr.filter(
          item => item !== val
        );
      } else {
        this.activeCooprateArr.push(val);
      }
    },

    // 选择门店数量
    changeupOr(val) {
      this.activeupOr = val;
    }
  }
};
</script>

<style lang="scss" scoped>
.customer-property {
  background: #f7f7f7;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .main {
    flex: 1;
    overflow: auto;
  }

  .list-wrap {
    .customer-brief {
      background: #ffffff;
      border-radius: 6px;
      margin: 12px 16px;
      padding: 12px 16px;
    }
    .name {
      // height: 24px;
      font-family: PingFangSC-Medium;
      font-weight: 550;
      font-size: 16px;
      color: #000000;
      margin-bottom: 8px;
    }
    .brief_item {
      display: flex;
      margin-bottom: 8px;

      .label {
        width: 52px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 13px;
        color: #7f7f7f;
        line-height: 20px;
        margin-right: 8px;
      }

      .buss-txt {
        flex: 1;
        min-width: 0;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 13px;
        color: #333333;
      }
    }
  }
}

/deep/ .van-nav-bar .van-icon {
  color: #000000;
}

/deep/ .van-dropdown-item__content {
  position: absolute;
  max-height: 80%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/deep/ .van-dropdown-menu__title {
  font-size: 14px;
  color: #000000;
  font-family: PingFangSC-Regular;
}

/deep/ .van-sidebar-item--select::before {
  background-color: #fff;
}

/deep/ .van-sidebar-item--select {
  color: #ff9900;
}

/deep/ .van-tree-select__item--active {
  color: #ff9900;
}

.list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .main {
    flex: 1;
    overflow-y: auto;
    padding: 8px 16px;

    .main_item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      p {
        width: 308px;
        height: 40px;
        line-height: 40px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #000000;
        // 超出部分显示。。。
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .active {
        width: 308px;
        height: 40px;
        line-height: 40px;
        font-family: PingFangSC-Medium;
        font-weight: bold;
        font-size: 14px;
        color: #ff9900;
        // 超出部分显示。。。
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      img {
        width: 20px;
        height: 20px;
      }
    }

    p {
      height: 48px;
      line-height: 48px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #000000;
    }

    .main_item_status {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .item_status {
        width: 165px;
        height: 36px;
        background: #f7f7f7;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        font-size: 12px;
      }
      .active_item_status {
        width: 166px;
        height: 36px;
        background: #ff99001a;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        font-size: 12px;
        color: #ff9900;
      }
    }

    // 来源
    .source {
      display: flex;
      justify-content: space-between;
      overflow: hidden;
      .source_right {
        flex: 1;
        background-color: #fff;

        .childrenName {
          height: 40px;
          background: #fff;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #000000;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 16px;
        }

        .active_child_name {
          height: 40px;
          background: #fff;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #ff9900;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 16px;
        }
      }
    }
  }

  .footer {
    padding: 7px 16px;
    height: 52px;
    background: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .rect {
      width: 116px;
      height: 38px;
      border: 0.5px solid #e5e5e5;
      border-radius: 6px;
    }

    .search {
      width: 215px;
      height: 38px;
      background: #ff9900;
      border-radius: 6px;
      color: #fff;
    }
  }
}
</style>
<style>
.high-light {
  color: #ff9900 !important;
  font-weight: normal;
}
</style>
