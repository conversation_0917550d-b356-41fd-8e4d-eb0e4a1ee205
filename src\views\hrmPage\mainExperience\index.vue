<template>
  <div class="experience-page">
    <!-- 教育经历 -->
    <div class="education">
      <div class="education-title">
        <p class="must">教育经历</p>
        <van-icon name="plus" @click="addEduct" />
      </div>
      <p class="annotation">若最高学历非全日制教育，还需填写全日制教育学历</p>
      <!-- 教育内容 -->
      <ul>
        <li v-for="(item, index) in eductData" :key="index">
          <div class="educt-content">
            <p class="educt-name">{{ item.graduationSchool }}</p>
            <div class="educt-arrow">
              <p class="educt-year">
                {{ item.admissionDate }} - {{ item.graduationDate }}
              </p>
              <van-icon name="arrow" @click="onEditEduction(item)" />
            </div>
          </div>
          <div class="educt-info">
            <span>{{
              item.educationType &&
              educationTypeList.find((ele) => ele.value == item.educationType)
                ? educationTypeList.find(
                    (ele) => ele.value == item.educationType
                  ).dname
                : ""
            }}</span>
            <span>{{
              item.education &&
              staffEducationList.find((ele) => ele.value == item.education)
                ? staffEducationList.find((ele) => ele.value == item.education)
                    .dname
                : ""
            }}</span>
            <span>{{
              item.academicDegree &&
              academicDegreeList.find((ele) => ele.value == item.academicDegree)
                ? academicDegreeList.find(
                    (ele) => ele.value == item.academicDegree
                  ).dname
                : ""
            }}</span>
            <span>{{ item.major }}</span>
          </div>
        </li>
      </ul>
    </div>

    <!-- 工作经历 -->
    <div class="work">
      <div class="work-title">
        <p class="must">工作经历</p>
        <van-icon name="plus" @click="addWorkClick" />
      </div>
      <!-- 工作内容 -->
      <ul>
        <li v-for="(item, index) in workData" :key="index">
          <div class="work-content">
            <p class="work-name">{{ item.companyName }}</p>
            <div class="work-arrow">
              <p class="work-year">{{ item.startTime }} - {{ item.endTime }}</p>
              <van-icon name="arrow" @click="onEditWork(item)" />
            </div>
          </div>
          <div class="work-info">
            <div>
              <span>{{ item.post }}</span>
              <span>{{ item.deptName }}</span>
            </div>
            <div class="work-main">
              {{ item.workContent }}
            </div>
          </div>
        </li>
      </ul>
    </div>

    <!-- 联系人 -->
    <div class="linkMan">
      <div class="linkMan-title">
        <p class="must">联系人</p>
        <van-icon name="plus" @click="addLinkManClick" />
      </div>
      <!-- 工作内容 -->
      <ul>
        <li v-for="(item, index) in dataLinkMan" :key="index">
          <div class="linkMan-content">
            <p class="linkMan-name">{{ item.name }}</p>
            <div class="linkMan-arrow">
              <van-icon name="arrow" @click="onEditLink(item)" />
            </div>
          </div>
          <div class="linkMan-info">
            <div>
              <span>{{
                item.relation &&
                linkManRelationshipList.find(
                  (ele) => ele.value == item.relation
                )
                  ? linkManRelationshipList.find(
                      (ele) => ele.value == item.relation
                    ).dname
                  : ""
              }}</span>
              <span>{{ item.phoneNo }}</span>
            </div>
            <div class="linkMan-main">{{ item.address }}</div>
            <!-- <div class="linkMan-user">
              <p>
                <span>创建人：{{ item.createBy }}</span>
                <span style="margin-left: 5px"
                  >创建时间：{{ item.createTime }}</span
                >
              </p>
              <p>
                <span>修改人：{{ item.updateBy }}</span>
                <span style="margin-left: 5px"
                  >修改时间：{{ item.updateTime }}</span
                >
              </p>
            </div> -->
          </div>
        </li>
      </ul>
    </div>

    <!-- 新增教育经历 -->
    <add-eduction
      v-model="showAddEduction"
      :educationTypeList="educationTypeList"
      :staffEducationList="staffEducationList"
      :academicDegreeList="academicDegreeList"
      @eductionUpdata="getEduInfo"
      :editData="editData"
    ></add-eduction>

    <!-- 新增工作经历 -->
    <add-work
      v-model="showAddWork"
      @workUpdata="getJobList"
      :editWork="editWork"
    ></add-work>

    <!-- 新增联系人 -->
    <add-link-man
      v-model="showAddLinkMan"
      :linkManRelationshipList="linkManRelationshipList"
      @linkUpdata="getLinkManList"
      :editLink="editLink"
    ></add-link-man>
  </div>
</template>

<script>
// api
import {
  getAllDict,
  getEducationList,
  getStaffWorkHistoryList,
  getStaffRelationshipList,
} from "../../../api/hrm";
// vant
import { Popup, NavBar, Loading, Button, Icon, Overlay ,Toast} from "vant";
import addEduction from "./compontents/addEduction.vue";
import addWork from "./compontents/addWork.vue";
import addLinkMan from "./compontents/addLinkMan.vue";
export default {
  components: {
    addEduction,
    addWork,
    addLinkMan,
    [NavBar.name]: NavBar,
    [Overlay.name]: Overlay,
    [Popup.name]: Popup,
    [Loading.name]: Loading,
    [Button.name]: Button,
    [Icon.name]: Icon,
  },
  data() {
    return {
      showAddEduction: false, //新增教育经历
      showAddWork: false, //新增工作经历
      showAddLinkMan: false, //新增联系人
      dictData: [],
      eductData: [], //教育列表数据
      workData: [], //工作列表数据
      dataLinkMan: [], //联系人列表数据
      //字典项
      educationTypeList: [], //学历类型
      staffEducationList: [], //学历
      academicDegreeList: [], //学位
      linkManRelationshipList: [], //父子关系

      editData: {}, //编辑教育经历对象
      editWork: {}, //工作经历对象
      editLink: {}, //联系人对象
    };
  },
  mounted() {
    this.getAllDict(); //字典项
    this.getEduInfo(); //教育列表数据
    this.getJobList(); //工作列表数据
    this.getLinkManList(); //联系人列表数据
  },
  watch: {
    showAddEduction(val) {
      if (!val) {
        setTimeout(() => {
          for (let i in this.editData) {
            this.editData[i] = "";
          }
        }, 500);
      }
    },
    showAddWork(val) {
      if (!val) {
        setTimeout(() => {
          for (let i in this.editWork) {
            this.editWork[i] = "";
          }
        }, 500);
      }
    },
    showAddLinkMan(val) {
      if (!val) {
        setTimeout(() => {
          for (let i in this.editLink) {
            this.editLink[i] = "";
          }
        }, 500);
      }
    },
  },
  methods: {
    //查询员工详细信息
    async getEduInfo() {
      this.eductData = [];
      let res = await getEducationList({ staffId: this.$route.query.id });
      this.eductData = res;
    },

    //获取工作经历列表
    async getJobList() {
      this.workData = [];
      let res = await getStaffWorkHistoryList({
        staffId: this.$route.query.id,
      });
      this.workData = res;
    },

    // 获取联系人列表
    async getLinkManList() {
      this.dataLinkMan = [];
      let res = await getStaffRelationshipList({
        staffId: this.$route.query.id,
      });
      this.dataLinkMan = res;
      console.log(res);
    },

    addEduct() {
      this.showAddEduction = true;
    },

    addWorkClick() {
      this.showAddWork = true;
    },

    addLinkManClick() {
      this.showAddLinkMan = true;
    },

    //加载数据字典
    async getAllDict() {
      let res = await getAllDict();
      this.dictData = res;
      this.educationTypeList =
        this.dictData.find((item) => item.type === "education_type").children ||
        [];
      this.staffEducationList =
        this.dictData.find((item) => item.type === "staff_education")
          .children || [];
      this.academicDegreeList =
        this.dictData.find((item) => item.type === "academic_degree")
          .children || [];

      this.linkManRelationshipList =
        res.find((item) => item.type === "linkman_relationship").children || [];
    },

    // 点击箭头进入编辑页面
    onEditEduction(val) {
      this.editData = JSON.parse(JSON.stringify(val));
      this.showAddEduction = true;
    },

    // 编辑工作经历
    onEditWork(val) {
      this.editWork = JSON.parse(JSON.stringify(val));
      this.showAddWork = true;
    },

    // 编辑联系人
    onEditLink(val) {
      this.editLink = JSON.parse(JSON.stringify(val));
      this.showAddLinkMan = true;
    },

    saveEduct() {
      if(this.eductData.length == 0) {
        return Toast.fail({
          duration: 2000,
          message: "教育经历不能为空！",
        });
      } else if(this.workData.length == 0) {
        return Toast.fail({
          duration: 2000,
          message: "工作经历不能为空！",
        });
      } else if(this.dataLinkMan.length == 0) {
        return Toast.fail({
          duration: 2000,
          message: "联系人不能为空！",
        });
      }else {
        this.$router.push({
          name: 'bankAccount',
          query: {
            id:this.$route.query.id,
          },
        });
      }
    }
  },
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
.must {
  &::after {
    content: "*";
    display: inline-block;
    margin-left: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
}
</style>