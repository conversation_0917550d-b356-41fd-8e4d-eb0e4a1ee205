.popup {
  display: flex;
  flex-direction: column;
  height: 100%;
  .header {
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    flex-shrink: 0;

    img {
      width: 20px;
      height: 20px;
    }

    .title {
      width: 96px;
      height: 24px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #000000;
      text-align: center;
      line-height: 24px;
    }
  }

  .main {
    flex: 1;
    background: #f7f7f7;
    overflow: auto;

    .title-apply {
      .title {
        display: flex;
        align-items: center;
        height: 45px;
        font-family: PingFangSC-Medium;
        font-weight: 550;
        font-size: 15px;
        color: #333333;
        letter-spacing: 0;
        padding: 0 16px;
      }

      .title-message {
        background-color: #fff;
        .title-item-title {
          margin: 0 16px;
          min-height: 70px;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          justify-content: center;
          border-bottom: 0.5px solid #e5e5e5;

          p {
            width: 112px;
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #7f7f7f;
            margin-bottom: 6px;
          }
          .reason {
            min-height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
          }
        }
        .title-item {
          height: 44px;
          margin: 0 16px;
          border-bottom: 0.5px solid #e5e5e5;
          display: flex;
          justify-content: space-between;
          align-items: center;

          label {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #7f7f7f;
          }

          .content {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 15px;
            color: #333333;
            text-align: right;
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }

    .basic {
      .basic-title {
        display: flex;
        align-items: center;
        height: 44px;
        font-family: PingFangSC-Medium;
        font-weight: 550;
        font-size: 15px;
        color: #333333;
        letter-spacing: 0;
        padding: 0 16px;
      }

      .basic-content {
        background-color: #fff;
        .basic-item {
          height: 44px;
          margin: 0 16px;
          border-bottom: 0.5px solid #e5e5e5;
          display: flex;
          justify-content: space-between;
          align-items: center;

          label {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #7f7f7f;
          }

          .content {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 15px;
            color: #333333;
            text-align: right;
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }

    .message {
      margin-bottom: 12px;
      .message-title {
        display: flex;
        align-items: center;
        height: 44px;
        font-family: PingFangSC-Medium;
        font-weight: 550;
        font-size: 15px;
        color: #333333;
        letter-spacing: 0;
        padding: 0 16px;
      }

      .message-info {
        background-color: #fff;
        padding: 16px;
        .message-list {
          display: flex;
          margin-bottom: 15px;

          &:last-child {
            margin-bottom: 0;
          }

          .message-list-left {
            margin-right: 13px;
            img {
              width: 30px;
              height: 30px;
            }

            .line {
              width: 1px;
              height: calc(100% - 33px);
              background: #e5e5e5;
              margin: 0 15px;
            }

            .active-line {
              width: 1px;
              height: calc(100% - 33px);
              background: #ff9900;
              margin: 0 15px;
            }
          }

          .message-list-right {
            flex: 1;
            .job {
              height: 21px;
              font-family: PingFangSC-Medium;
              font-weight: 550;
              font-size: 15px;
              color: #333333;
              margin-bottom: 9px;
            }

            .right-message {
              display: flex;
              justify-content: space-between;

              .right-message-left {
                display: flex;
                align-items: center;
                margin-bottom: 5px;

                img {
                  width: 28px;
                  height: 28px;
                  margin-right: 6px;
                }

                span {
                  height: 20px;
                  font-family: PingFangSC-Regular;
                  font-weight: 400;
                  font-size: 14px;
                  color: #7f7f7f;
                }
              }

              .right-message-right {
                display: flex;
                align-items: center;
                p {
                  font-size: 11px;
                  background: #ffa6001f;
                  border-radius: 4px;
                  color: #ffa600;
                  padding: 4.5px 3px;
                }

                span {
                  font-size: 12px;
                  color: #b2b2b2;
                  margin-left: 6px;
                }
              }
            }

            .message-text {
              display: flex;
              align-items: center;
              background: #f7f7f7;
              border-radius: 4px;
              padding: 6px 9px;
              font-size: 12px;
              color: #b2b2b2;
              margin-bottom: 8px;
            }

            // 附件
            .file-text {
              display: flex;
              align-items: center;
              background: #f7f7f7;
              border-radius: 4px;
              padding: 6px 9px;
              font-size: 12px;
              color: #b2b2b2;
              margin-bottom: 8px;

              &:last-child {
                margin-bottom: 0;
              }

              .icon {
                width: 14px;
                height: 14px;
                margin-right: 8px;
              }
            }
          }
        }
      }
    }
  }

  .footer {
    .message {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px 0 16px;

      input {
        height: 33px;
        width: 88%;
        background: #f7f7f7;
        border: none;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 15px;
        color: #7f7f7f;
        border-radius: 4px;
        padding-left: 12px;
      }

      input::-webkit-input-placeholder {
        color: #a1a1a1;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 15px;
        color: #b2b2b2;
        letter-spacing: 0;
        text-align: left;
      }
    }

    // 附件样式
    .fileList {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 12px 16px;
      background: #f7f7f7;
      color: #b2b2b2;
      padding: 6px 9px;
      font-size: 12px;
      border-radius: 4px;

      img {
        width: 14px;
        height: 14px;
      }

      p {
        flex: 1;
        text-align: left;
        margin-left: 8px;
      }
    }

    // 按钮
    .btn {
      padding: 0 16px;
      height: 52px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .btn-box {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        border-right: 1px solid #e5e5e5;

        .icon {
          width: 18px;
          height: 18px;
          margin-right: 5px;
        }

        .icon-more {
          width: 24px;
          height: 24px;
          margin-right: 5px;
        }

        &:last-child {
          border-right: none;
        }
      }

      .btn-box:first-child {
        font-family: PingFangSC-Medium;
        font-weight: 550;
        font-size: 15px;
        color: #1cbb61;
      }

      .btn-box:nth-child(2) {
        font-family: PingFangSC-Medium;
        font-weight: 550;
        font-size: 15px;
        color: #ff1111;
      }

      .btn-box:nth-child(3) {
        font-family: PingFangSC-Medium;
        font-weight: 550;
        font-size: 15px;
        color: #333333;
      }
    }

    .main-btn {
      padding: 12px 16px;
    }
  }
}

::v-deep .van-popover__wrapper {
  display: flex;
  align-items: center;
}

.morePop {
  width: 106px;
  max-height: 120px;
  padding: 16px 18px;
  font-size: 15px;
  color: #333333;

  .countersign {
    width: 100%;
    display: flex;
    align-items: center;
    height: 35px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
  .icon-more {
    width: 18px;
    height: 18px;
    margin-right: 14px;
  }
}
