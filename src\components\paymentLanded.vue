<template>
  <div class="landed-page">
    <!-- header -->
    <!-- <van-nav-bar title="付款"></van-nav-bar> -->
    <!-- body -->
    <div class="landed-body">
      <div class="land-title">
        <p class="text">付款给苏州万店掌网络科技有限公司</p>
        <p class="serialNumber">流水号：{{ form.seqNo }}</p>
        <div class="wx-box" v-show="payType == 4">
          <img src="../assets/img/ico_wechat.png" alt="" />
          <!-- <p>微信支付</p> -->
        </div>
        <div class="wx-box" v-show="payType == 5">
          <img src="../assets/img/ico_zfb.png" alt="" />
          <!-- <p>支付宝支付</p> -->
        </div>
      </div>
    </div>
    <div class="land-input">
      <div class="input-w money-text">
        <p class="money">
          <img src="../assets/img/rmb2.png" alt="" />
        </p>
        <van-field
          inputmode="none"
          @click-input="focusAmount"
          id="name"
          v-model="amount"
          @blur="changeAmount"
          type="number"
          placeholder="请输入付款金额"
        />
        <!-- <van-field @input="filterNumberAndDot" @focus="focusAmount" v-model="amount" @blur="changeAmount" :readonly="form.amount != 0"   type="number" placeholder="请输入付款金额" /> -->
      </div>
      <div class="input-w phone-txt">
        <p class="phone">
          <img src="../assets/img/ico_mobile.png" alt="" />
        </p>
        <van-field
          inputmode="none"
          @click-input="focusPhone"
          v-model="phone"
          type="number"
          placeholder="请输入手机号"
        />
      </div>

      <!-- <div class="mobile" v-show="phone && address">
        <img src="../assets/img/ico_pipei@2x (1).jpg" alt="" />
        <p class="text">该手机号归属</p>
        <p class="text-two">{{ address }}</p>
      </div>
      <div class="mobile" v-show="phone && !address">
        <img src="../assets/img/ico_yiwen.jpg" alt="" />
        <p class="text">账号未关联手机号或为我们的新客户，请继续</p>
      </div> -->

      <!-- 备注 -->
      <div class="remark" @click="onAddRemark" v-show="remark.length == 0">
        添加备注
      </div>

      <div class="remark-content" v-show="remark.length > 0">
        {{ remark }}
        <span id="edit" @click="editClick(remark)">修改</span>
      </div>
    </div>

    <!-- footer -->
    <div class="footer" v-show="showQrpay">
      <van-button
        size="large"
        :loading="loading"
        loading-text="付款中..."
        @click="qrPay"
        color="#FF9900"
        >确认付款</van-button
      >
    </div>

    <van-dialog
      v-model="show"
      :show="show"
      title="添加备注"
      show-cancel-button
      @confirm="confirm"
      @cancel="cancel"
    >
      <van-field
        v-model="tempRemark"
        rows="2"
        autosize
        type="textarea"
        maxlength="100"
        placeholder="请输入备注"
        show-word-limit
      />
    </van-dialog>

    <!-- 金额输入 -->
    <van-number-keyboard
      :show="showAmount"
      theme="custom"
      :extra-key="['.']"
      close-button-text="付款"
      @blur="showAmount = false"
      @close="closeAmount"
      @input="onInput"
      @delete="deleteAmount"
    />

    <!-- 手机号输入 -->
    <van-number-keyboard
      :maxlength="11"
      :show="showPhone"
      theme="custom"
      close-button-text="付款"
      @blur="showPhone = false"
      @close="closePhone"
      @input="onInputNumber"
      @delete="deletePhone"
    />

    <!-- 直接登录弹窗 -->
    <dialogLogin :showDialog="showLogin" :sourcePage="'paymentLandedPage'" />

    <!-- 认证弹窗 -->
    <dialogAttest :showDialog="showDialog" :sourcePage="'paymentLandedPage'" />
  </div>
</template>

<script>
import {
  NavBar,
  Button,
  Divider,
  CellGroup,
  Field,
  Icon,
  Toast,
  Dialog,
  NumberKeyboard
} from "vant";
import { getUserByPhone, getCollectionByOrderNo, qrPay } from "../api/delivery";
import { getUserRealName } from "../api/realNameAuth";
import dialogAttest from "../components/dialogAttest.vue";
import dialogLogin from "../components/dialogLogin.vue";

export default {
  components: {
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Divider.name]: Divider,
    [CellGroup.name]: CellGroup,
    [Field.name]: Field,
    [Icon.name]: Icon,
    [Dialog.Component.name]: Dialog.Component,
    [NumberKeyboard.name]: NumberKeyboard,
    dialogAttest,
    dialogLogin
  },
  data() {
    return {
      show: false,
      phone: "",
      //showRemark: false,
      form: {},
      payType: 4,
      remark: "", //真备注
      tempRemark: "", //临时备注
      loading: false,
      deptList: {}, //门店、企业信息
      shopList: {},
      code: "",
      weixiList: {},
      openList: {},
      shopAddress: "", //门店地址
      groupAddress: "", //企业地址
      address: "", //展示地址
      // showHint:false,
      amount: "", //实际输入金额
      showPhone: false,
      showAmount: false,
      showQrpay: true,
      readAmountOnly: true,
      readPhoneOnly: true,
      showDialog: false,
      authObj: {}, //实名认证信息
      showLogin: true
    };
  },
  created() {
    const { orderNo } = this.$route?.query;
    if (this.$route.query?.isLogin == "1") {
      this.showLogin = false;
    }

    if (orderNo) {
      localStorage.setItem("orderNo", orderNo);
      this.getCollectionByOrderNo(orderNo);
    }
    if (this.IsWeixinOrAlipay() == "false") {
      return this.$router.push({
        name: "otherScan"
      });
    }
    this.getUserRealName();
    this.phone = sessionStorage.getItem("mobilePhoneAuth");
  },
  mounted() {
    document.querySelector("#name").focus();
  },
  watch: {
    phone(val) {
      if (val) {
        this.getUserByPhone(val);
        // this.address = '';
      }
    },

    // 显示手机号键盘
    showPhone(val) {
      this.showQrpay = !val;
    },

    // 显示金额键盘
    showAmount(val) {
      this.showQrpay = !val;
    }
  },
  methods: {
    onAddRemark() {
      this.show = true;
    },
    // 确认弹窗内容
    confirm() {
      this.remark = this.tempRemark;
    },
    // 取消弹窗
    cancel() {
      this.tempRemark = "";
    },

    // 修改
    editClick(val) {
      this.show = true;
      if (val) {
        this.tempRemark = val;
      }
    },

    IsWeixinOrAlipay() {
      var ua = window.navigator.userAgent.toLowerCase();
      //判断是不是微信
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        this.payType = 4;
        return "WeiXIN";
      }
      //判断是不是支付宝
      if (ua.match(/AlipayClient/i) == "alipayclient") {
        this.payType = 5;
        return "Alipay";
      }
      //哪个都不是
      return "false";
    },

    // 查询当前用户是否已经实名认证通过（仅登录查询）
    async getUserRealName() {
      try {
        const res = await getUserRealName();
        // cardStatus 认证状态(0未认证，1已认证， 2已拒绝，3认证中)
        if (
          (res?.length == 0 ||
            !res ||
            res[0]?.cardStatus == 0 ||
            res[0]?.cardStatus == 2) &&
          this.$route.query?.isLogin == "1"
        ) {
          this.showDialog = true;
        } else {
          this.authObj = res[0];
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 根据手机号查询门店。企业
    async getUserByPhone(phone) {
      try {
        const res = await getUserByPhone({ phone });
        if (res && res.departments) {
          this.deptList = res;
          this.shopList =
            this.deptList &&
            this.deptList.departments.find((item) => {
              return (item.phone = this.phone);
            });

          if (this.deptList && this.deptList.name) {
            if (this.deptList.name.length <= 6) {
              this.groupAddress = this.deptList.name;
            } else {
              this.groupAddress = this.deptList.name.slice(0, 6);
            }
          }
          // 门店地址
          if (this.shopList && this.shopList.name) {
            if (this.shopList.name.length <= 6) {
              this.shopAddress = this.shopList.name;
            } else {
              this.shopAddress = this.shopList.name.substr(-6);
            }
          }

          // 截取的企业地址前6位 + 门店地址后6位
          this.address = this.groupAddress + "..." + this.shopAddress;
        } else {
          this.shopList = "";
          this.deptList = "";
          this.address = "";
        }
        // console.log(this.address);
      } catch (error) {
        console.log(error);
      }
    },

    // 手机号聚焦调起键盘
    focusPhone() {
      this.showPhone = true;
    },

    // 金额聚焦调起键盘
    focusAmount() {
      this.showAmount = true;
    },

    // 调用支付
    closePhone() {
      this.qrPay();
    },
    // 调用支付
    closeAmount() {
      this.qrPay();
    },

    // 删除手机号
    deletePhone() {
      if (this.phone.length == 0) return;
      this.phone = this.phone.substring(0, this.phone.length - 1);
    },

    // 删除金额
    deleteAmount() {
      if (this.amount.length == 0 || this.form.amount != 0) return;
      this.amount = this.amount.substring(0, this.amount.length - 1);
    },

    // 输入手机号
    onInputNumber(val) {
      if (this.phone.length >= 11) {
        return;
      } else {
        this.phone += val;
      }
    },

    // 输入金额
    onInput(op) {
      if (this.form.amount != 0) {
        return;
      }
      if (this.amount.length <= 9) {
        this.amount += op;
        if (+this.amount > 999999.99) {
          this.amount = this.amount.substring(0, this.amount.length - 1);
        } else if (this.amount.charAt(0) == 0) {
          // 第一位是0后边必须是.
          if (this.amount.length > 1 && this.amount.charAt(1) != ".") {
            this.amount = this.amount.substring(0, this.amount.length - 1);
          }
          // 第一位是0后边必须是. .后边必须跟数字
          if (this.amount.length > 1 && this.amount.charAt(2) == ".") {
            console.log(this.amount);
            this.amount = this.amount.substring(0, this.amount.length - 1);
          }
          //最小为0.001
          if (this.amount == "0.00") {
            this.amount = this.amount.substring(0, this.amount.length - 1);
          }
          //两位小数
          if (this.amount.length > 4) {
            this.amount = this.amount.substring(0, this.amount.length - 1);
          }
          if (this.amount == "0.0.") {
            this.amount = this.amount.substring(0, this.amount.length - 1);
          }
        } else if (this.amount.charAt(0) == ".") {
          this.amount = "0.";
        } else {
          // 两位小数
          for (let x = 0; x < this.amount.length; x++) {
            if (this.amount.charAt(x) == ".") {
              this.amount = this.amount.substring(0, x + 3);
              if (
                this.amount.charAt(x + 1) == "." ||
                this.amount.charAt(x + 2) == "."
              ) {
                this.amount = this.amount.substring(0, this.amount.length - 1);
              }
            }
          }
        }
      }
    },

    changeAmount(val) {
      if (
        val.target.value == "0" ||
        val.target.value == "0." ||
        val.target.value == "0.0" ||
        val.target.value == "0.00"
      ) {
        return Toast.fail({
          duration: 2000,
          message: "输入金额最小为0.01"
        });
      }
    },

    // 根据id查询收款码  SKM311259650871365 SKM311251635249221
    async getCollectionByOrderNo(orderNo) {
      // const orderNo = this.$route.query.orderNo;
      try {
        const res = await getCollectionByOrderNo({ orderNo: orderNo });
        this.form = res;
        // 如果扫码金额为0 ，手动置空 ，因为后端 0 就是空
        if (this.form && this.form.amount != 0) {
          this.amount = this.form.amount;
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 动态二维码（发起支付）
    async qrPay() {
      if (!this.phone || !this.amount) {
        return Toast.fail({
          duration: 2000,
          message: "请输入金额和手机号"
        });
      }
      let strRegex =
        /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/;
      if (strRegex.test(this.phone) == false) {
        return Toast.fail({
          duration: 2000,
          message: "请输入正确的手机号"
        });
      }
      if (Number(this.amount) == 0) {
        return Toast.fail({
          duration: 2000,
          message: "输入金额最小为0.01"
        });
      }
      let wxMessage = JSON.parse(sessionStorage.getItem("wxMessage"));
      this.loading = true;
      const obj = {
        payId: this.form.id, //收款码id
        seqNo: this.form.seqNo, //流水号
        mobilePhone: this.phone, //手机号码
        deptId: this.shopList ? this.shopList.id : "", //门店id
        deptName: this.shopList ? this.shopList.name : "", //门店名称
        amount: Number(this.amount), //金额
        paymentMode: this.payType, //付款类型（4微信，5支付宝）
        groupId: this.deptList ? this.deptList.id : "", //企业id
        groupName: this.deptList ? this.deptList.name : "", //企业名称
        mobileRemark: this.remark || "", //备注
        openId: wxMessage?.openId || this.authObj?.openId, //微信openid
        nickName: wxMessage?.nickName,
        oppositeAccountName: this.authObj?.accountName //实名认证姓名
      };
      console.log(obj);
      try {
        const res = await qrPay(obj);
        if (res) {
          this.loading = false;
          let u = navigator.userAgent;
          let isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1; //android终端
          let isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
          // console.log(isAndroid , isiOS);
          if (isAndroid) {
            window.open(res);
          } else if (isiOS) {
            window.location.href = res;
          }
        }
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    }
  }
};
</script>

<style scoped lang="scss">
.landed-page {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  box-sizing: border-box;
  padding-top: 24px;
  display: flex;
  flex-direction: column;

  .landed-body {
    // margin-top: 24px;
    height: 90px;
    padding: 0 16px;
    display: flex;
    // justify-content: center;
    // align-items: center;
    flex-direction: column;
    .land-title {
      text-align: left;
      position: relative;
      .text {
        // height: 24px;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        font-size: 17px;
        color: #333333;
        margin-bottom: 8px;
      }
      .serialNumber {
        // height: 18px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 13px;
        color: #7f7f7f;
        margin-bottom: 8px;
      }
      .wx-box {
        // display: flex;
        // justify-content: center;
        // align-items: center;
        // margin-bottom: 35px;
        position: absolute;
        right: 0;
        top: 5px;
        img {
          width: 36px;
          height: 36px;
        }
      }
    }
  }
  .land-input {
    padding: 16px;
    flex: 1;
    background-color: #fff;
    border-radius: 16px 16px 0 0;
  }
  .input-w {
    display: flex;
    position: relative;
    margin-bottom: 16px;
    .money {
      // width: 18px;
      // height: 18px;
      width: 36px;
      height: 36px;
      position: absolute;
      left: 0;
      top: 7px;
      z-index: 1;

      img {
        width: 100%;
        height: 100%;
      }
    }
    .phone {
      // width: 18px;
      // height: 18px;
      width: 36px;
      height: 36px;
      position: absolute;
      left: 0;
      // top: 13px;
      z-index: 1;
      // background-image: url("../assets/img/ico_mobile.png");
      // background-repeat: no-repeat;
      // background-size: 100%;

      img {
        width: 100%;
        height: 100%;
      }
    }
    .edit {
      width: 24px;
      height: 24px;
      position: absolute;
      left: 0;
      top: 9px;
      z-index: 1;
      background-image: url("../assets/img/ico_edit2.jpg");
      background-repeat: no-repeat;
      background-size: 100%;
    }
    /deep/v-deep.input-w {
      flex: 1;
    }
    /deep/.van-field__control {
      padding-left: 29px;
    }
  }
  .mobile {
    display: flex;
    margin-top: 8px;
    margin-bottom: 24px;
    img {
      width: 14px;
      height: 14px;
    }
    .text {
      // width: 72px;
      // height: 17px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #b2b2b2;
      margin: 0 8px;
    }
    .text-two {
      // width: 192px;
      height: 17px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #333333;
    }
  }
  .money-text {
    /deep/.van-cell {
      font-weight: 600;
      font-size: 32px;
    }
    /deep/ .van-field__control::-webkit-input-placeholder {
      font-size: 16px;
      font-weight: 400;
    }
  }

  .phone-txt {
    /deep/.van-cell {
      font-weight: 400;
      font-size: 24px;
    }

    /deep/ .van-field__control::-webkit-input-placeholder {
      font-size: 16px;
      font-weight: 400;
    }
  }
  .remark {
    // height: 22px;
    font-family: PingFangSC-Semibold;
    font-weight: 400;
    font-size: 13px;
    color: #ff9900;
    // text-align: center;
    width: 130px;
    // margin: 0 auto;
    cursor: pointer;
  }

  .footer {
    background-color: #fff;
    padding: 0 16px 16px 16px;
    // margin-top: 58px;
  }
  /deep/.van-button {
    height: 44px;
    border-radius: 5px;
  }
  /deep/.van-cell-group--inset {
    margin: 0 0;
  }
  /deep/.van-cell {
    border-bottom: 1px solid #ebedf0;
  }
  .remark-content {
    font-size: 15px;
    color: #7f7f7f;
    #edit {
      color: #f90;
      margin-left: 5px;
    }
  }
}
/deep/.van-key--blue {
  color: #fff;
  background-color: #ff9900;
}
</style>