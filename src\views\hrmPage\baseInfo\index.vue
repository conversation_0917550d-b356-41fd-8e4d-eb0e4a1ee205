<template>
  <div class="baseInfo-page">
    <van-field
      class="no-must"
      label="姓名"
      readonly
      v-model="form.name"
      placeholder="请输入用户名"
    />

    <van-field
      label="花名"
      class="must"
      v-model="form.aliasName"
      placeholder="请输入花名"
    />

    <div class="uploadPhoto">
      <label class="must">证件照上传</label>
      <div class="txt">需要白底照片</div>
    </div>

    <div class="id-box">
      <div class="bg-head">
        <upload-file :max="1" v-model="iconUrlArr"></upload-file>
      </div>
    </div>

    <van-field name="radio" class="must" label="性别">
      <template #input>
        <van-radio-group direction="horizontal" v-model="form.sex">
          <van-radio name="1">男</van-radio>
          <van-radio name="2">女</van-radio>
        </van-radio-group>
      </template>
    </van-field>

    <van-field
      class="must"
      readonly
      clickable
      name="picker"
      :value="maritalStatus"
      label="婚姻"
      placeholder="点击选择婚姻"
      @click="clickMaritalStatus"
    />
    <van-popup v-model="showMaritalStatus" position="bottom">
      <van-picker
        show-toolbar
        :columns="maritalStatusList"
        :default-index="maritalStatusIndex"
        @confirm="onMaritalStatus"
        @cancel="showMaritalStatus = false"
      >
        <template #option="option">
          <div>{{ option.dname }}</div>
        </template>
      </van-picker>
    </van-popup>

    <!-- 籍贯 -->
    <van-field
      class="must"
      readonly
      clickable
      name="picker"
      :value="form.nativePlace"
      label="籍贯"
      placeholder="点击选择籍贯"
      @click="clickArea"
    />
    <van-popup v-model="showArea" position="bottom">
      <van-area
        :value="nativeValue"
        :area-list="areaList"
        @confirm="onArea"
        @cancel="showArea = false"
        :columns-num="2"
      />
    </van-popup>

    <!-- 民族 -->
    <van-field
      class="must"
      readonly
      clickable
      name="picker"
      :value="nationality"
      label="民族"
      placeholder="点击选择民族"
      @click="clickNationality"
    />
    <van-popup v-model="showNationality" position="bottom">
      <van-picker
        show-toolbar
        :columns="staffNationalityList"
        :default-index="nationalityIndex"
        @confirm="onNationality"
        @cancel="showNationality = false"
      >
        <template #option="option">
          <div>{{ option.dname }}</div>
        </template>
      </van-picker>
    </van-popup>

    <!-- 员工状态 -->
    <van-field class="no-must" readonly v-model="state" label="员工状态" />

    <van-field
      class="must"
      readonly
      v-model="form.mobilePhone"
      label="手机号"
      placeholder=""
    />

    <van-field
      class="no-must"
      readonly
      v-model="form.joinDate"
      label="入职时间"
      placeholder="请输入"
    />

    <!-- 政治面貌 -->
    <van-field
      class="must"
      readonly
      clickable
      name="picker"
      :value="polityStatus"
      label="政治面貌"
      placeholder="请选择政治面貌"
      @click="clickPolityStatus"
    />
    <van-popup v-model="showPolityStatus" position="bottom">
      <van-picker
        show-toolbar
        :columns="polityStatusList"
        :default-index="polityStatusIndex"
        @confirm="onPolityStatus"
        @cancel="showPolityStatus = false"
      >
        <template #option="option">
          <div>{{ option.dname }}</div>
        </template>
      </van-picker>
    </van-popup>

    <van-field
      v-model="form.mail"
      class="no-must"
      label="个人邮箱"
      placeholder="请输入个人邮箱"
    />

    <van-field class="no-must" readonly v-model="postMain" label="岗位" />

    <van-field class="no-must" readonly v-model="dutyMain" label="职务" />

    <van-field class="no-must" readonly v-model="deptMain" label="组织架构" />

    <van-field
      class="no-must"
      readonly
      v-model="directLeader"
      label="直属上级"
    />

    <!-- 在校期间 -->
    <div class="uploadPhoto">
      <label class="must">身份证上传</label>
    </div>

    <div class="id-card">
      <div class="id-card-bg">
        <div class="id-img-box">
          <!-- 上传 -->
          <upload-file
            :max="1"
            v-model="iconFrontUrlArr"
            @on-upload-success="val => uploadSuccess(val)"
          ></upload-file>
          <!-- <p v-show="iconFrontUrlArr.length <= 0">点击上传人像面</p> -->
        </div>
      </div>
    </div>

    <div class="id-card-back">
      <div class="id-card-bg">
        <div class="id-img-box">
          <upload-file
            :max="1"
            v-model="iconBackUrlArr"
            @on-upload-success="val => uploadSuccessBack(val)"
          ></upload-file>
          <!-- <p v-show="iconBackUrlArr.length <= 0">点击上传国徽面</p> -->
        </div>
      </div>
    </div>

    <van-field
      class="must"
      v-model="form.idCard"
      label="身份证号"
      placeholder="请输入身份证号"
    />

    <van-field
      v-model="form.birthday"
      @click="clickBirthday"
      class="must"
      label="生日"
      placeholder="请输入生日"
    />
    <van-popup v-model="showBirthday" position="bottom">
      <van-datetime-picker
        v-model="birthday"
        type="date"
        title="选择年月日"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onBirthday"
        @cancel="showBirthday = false"
      />
    </van-popup>

    <!-- 身份证有效期 -->
    <div class="duringSchool">
      <label class="must">身份证有效期</label>
      <div class="time">
        <p
          :class="this.form.idCardBeginDate ? 'txtTime' : ''"
          @click="clickAdmissionDate"
        >
          {{
            this.form.idCardBeginDate ? this.form.idCardBeginDate : "开始日期"
          }}
        </p>
        <span>至</span>
        <p
          :class="this.form.idCardExpireDate ? 'txtTime' : ''"
          @click="clickGraduationDate"
        >
          {{
            this.form.idCardExpireDate ? this.form.idCardExpireDate : "结束日期"
          }}
        </p>
      </div>
    </div>

    <!-- 身份证开始日期 -->
    <van-popup v-model="showAdmissionDate" position="bottom">
      <van-datetime-picker
        v-model="idCardBeginDate"
        type="date"
        title="选择年月日"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onAdmissionDate"
        @cancel="showAdmissionDate = false"
      />
    </van-popup>

    <!-- 身份证结束日期 -->
    <van-popup v-model="showGraduationDate" position="bottom">
      <van-datetime-picker
        v-model="idCardExpireDate"
        type="date"
        title="选择年月日"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onGraduationDate"
        @cancel="showGraduationDate = false"
      />
    </van-popup>

    <van-field
      class="must"
      v-model="form.idCardAddress"
      label="身份证地址"
      placeholder="请输入身份证地址"
    />

    <van-field
      class="must"
      readonly
      clickable
      name="area"
      :value="prefixAddress"
      label="居住地址"
      placeholder="请选择"
      @click="clickAreaList"
    />
    <van-popup v-model="showAreaList" position="bottom">
      <van-area
        :value="areaListValue"
        :area-list="areaList"
        @confirm="onAreaList"
        @cancel="showAreaList = false"
        :columns-num="2"
      />
    </van-popup>

    <van-field
      class="must"
      label="详细地址"
      v-model="suffixAddress"
      placeholder="请输入"
    />
  </div>
</template>

<script>
import {
  NavBar,
  Button,
  Divider,
  CellGroup,
  Field,
  Icon,
  Dialog,
  NumberKeyboard,
  RadioGroup,
  Radio,
  Picker,
  Popup,
  Area,
  Toast,
  DatetimePicker
} from "vant";
// api
import {
  getAllDict,
  getOcrInfo,
  updateStaff,
  getStaffInfo,
  getAllAreaInfo
} from "../../../api/hrm";
import UploadFile from "../../../components/upload-file/upload-file.vue";

export default {
  components: {
    UploadFile,
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Divider.name]: Divider,
    [CellGroup.name]: CellGroup,
    [Field.name]: Field,
    [Icon.name]: Icon,
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio,
    [Picker.name]: Picker,
    [Popup.name]: Popup,
    [Dialog.Component.name]: Dialog.Component,
    [NumberKeyboard.name]: NumberKeyboard,
    [Area.name]: Area,
    [DatetimePicker.name]: DatetimePicker
  },
  data() {
    return {
      showAreaList: false,
      showAdmissionDate: false, //身份证开始选择框
      showGraduationDate: false, //身份证结束选择日期框
      idCardBeginDate: new Date(),
      idCardExpireDate: new Date(),

      polityStatusList: [], //政治面貌
      maritalStatusList: [], //婚姻状况
      staffStateList: [], //员工状态
      staffNationalityList: [], //民族数据

      showNationality: false, //民族弹窗
      showPolityStatus: false, //政治面貌弹窗
      showArea: false, //显示籍贯地址
      showMaritalStatus: false, //婚姻
      showBirthday: false,

      // 省市地址list
      areaList: {
        province_list: {},
        city_list: {}
      },
      // 用于显示的文本
      state: "", //员工状态显示
      nationality: "", //民族
      polityStatus: "", //政治面貌
      postMain: "", //显示的主岗位
      dutyMain: "", //显示的主职务
      deptMain: "", //显示的组织架构
      maritalStatus: "", //婚姻状态
      directLeader: "", //直属上级 + 直属上级工号
      birthday: new Date(),

      // 传值的字段
      form: {
        name: "", //姓名
        aliasName: "", //花名
        state: "", // 员工状态
        age: "", //年龄
        sex: "", //性别
        nativePlace: "", //籍贯
        nationality: "", //民族
        maritalStatus: "", //婚姻状况
        mobilePhone: "", //手机号码
        mail: "", //邮箱
        polityStatus: "", //政治面貌
        joinDate: "", //入职日期
        positiveDate: "", //转正日期
        contractSignDate: "", //合同签约日期
        contractExpireDate: "", //合同到期日期
        contractType: "", //合同类型
        birthday: "", //生日
        idCard: "", //身份证号
        idCardAddress: "", //身份证地址
        currentAddress: "", // 居住地址
        idCardFrontUrl: "", //身份证正面照片
        idCardBackUrl: "", //身份证背面照片
        headPhotoUrl: "", //证件照
        idCardBeginDate: "", //身份证有效期开始
        idCardExpireDate: "", //身份证有效期结算
        deptRelationList: [], //组织架构
        dutyRelationList: [], //职务
        postRelationList: [], //岗位
        directLeader: "", //直属上级
        directLeaderNum: "", //直属上级工号
        directLeaderId: ""
      },

      iconUrlArr: [],
      iconFrontUrlArr: [],
      iconBackUrlArr: [],
      prefixAddress: "", //省市区地址
      suffixAddress: "", //详细地址
      minDate: new Date(1979, 0, 1),
      maxDate: new Date(2099, 10, 1),
      nationalityIndex: "", //民族的index
      polityStatusIndex: "",
      maritalStatusIndex: "", //婚姻
      nativeValue: "", //地址籍贯
      areaListValue: "" //省市 居住地址
    };
  },
  watch: {
    iconUrlArr(val) {
      if (val.length > 0) {
        this.form.headPhotoUrl = val[0].fileurl;
      } else {
        this.form.headPhotoUrl = "";
      }
    },
    iconFrontUrlArr(val) {
      if (val.length > 0) {
        this.form.idCardFrontUrl = val[0].fileurl;
      } else {
        this.form.idCardFrontUrl = "";
      }
    },

    iconBackUrlArr(val) {
      if (val.length > 0) {
        this.form.idCardBackUrl = val[0].fileurl;
      } else {
        this.form.idCardBackUrl = "";
      }
    },

    // 民族下拉关闭时清空默认选择的index , 点击时候再次赋值index
    showNationality(val) {
      if (!val) {
        this.nationalityIndex = "";
      }
    },
    // 政治面貌
    showPolityStatus(val) {
      if (!val) {
        this.polityStatusIndex = "";
      }
    },
    // 籍贯
    showArea(val) {
      if (!val) {
        this.nativeValue = "";
      }
    },

    // 居住地址
    showAreaList(val) {
      if (!val) {
        this.areaListValue = "";
      }
    },

    // 婚姻
    showMaritalStatus(val) {
      if (!val) {
        this.maritalStatusIndex = "";
      }
    }
  },
  created() {
    this.getAllDict();
    this.getAllAreaInfo();
  },
  methods: {
    // 选择生日回显
    clickBirthday(val) {
      this.birthday = new Date(val.target.value);
      this.showBirthday = true;
    },

    // 选择生日
    onBirthday(val) {
      if (val) {
        this.form.birthday = this.$moment(val).format("YYYY-MM-DD");
      }
      this.showBirthday = false;
    },

    // 点击婚姻回显数据
    clickMaritalStatus(val) {
      this.maritalStatusIndex = this.maritalStatusList.findIndex(item => {
        return item.dname == val.target.value;
      });
      this.showMaritalStatus = true;
    },

    //选择婚姻
    onMaritalStatus(val) {
      this.form.maritalStatus = val.value;
      this.maritalStatus = val.dname;
      this.showMaritalStatus = false;
    },
    // 回显身份证开始日期
    clickAdmissionDate(val) {
      if (val.target.innerText != "开始日期") {
        this.idCardBeginDate = new Date(val.target.innerText);
      } else {
        this.idCardBeginDate = new Date();
      }
      this.showAdmissionDate = true;
    },
    // 点击选择身份证开始日期
    onAdmissionDate(val) {
      if (val) {
        let idCardBeginDateTemp = this.$moment(val).format("YYYY/MM/DD");
        this.form.idCardBeginDate = idCardBeginDateTemp;
      }
      this.showAdmissionDate = false;
    },

    // 回显
    clickGraduationDate(val) {
      if (val.target.innerText != "结束日期") {
        this.idCardExpireDate = new Date(val.target.innerText);
      } else {
        this.idCardExpireDate = new Date();
      }
      this.showGraduationDate = true;
    },

    // 点击选择身份证结束日期
    onGraduationDate(val) {
      if (val) {
        let idCardExpireDateTemp = this.$moment(val).format("YYYY/MM/DD");
        this.form.idCardExpireDate = idCardExpireDateTemp;
      }
      this.showGraduationDate = false;
    },

    clickNationality(val) {
      // 回显数据时点击默认到当前数据
      this.nationalityIndex = this.staffNationalityList.findIndex(item => {
        return item.dname == val.target.value;
      });
      this.showNationality = true;
    },
    // 点击选择民族
    onNationality(val) {
      this.form.nationality = val.value;
      this.nationality = val.dname;
      this.showNationality = false;
    },

    // 获取当前的地址 picker自动定位到当前地址位置
    clickArea(val) {
      let address = val.target.value;
      let province = address.split("/")[0];
      let city = address.split("/")[1];
      if (city) {
        this.nativeValue = this.getObjectKey(this.areaList.city_list, city);
      } else {
        this.nativeValue = this.getObjectKey(
          this.areaList.province_list,
          province
        );
      }
      this.showArea = true;
    },

    // 根据对象vlaue 值 去查 key
    getObjectKey(object, value) {
      return Object.keys(object).find(key => object[key] == value);
    },

    // 点击选择籍贯地址
    onArea(val) {
      this.form.nativePlace = val[0].name + "/" + val[1].name;
      this.showArea = false;
    },

    // 身份证地址省市区
    clickAreaList(val) {
      let address = val.target.value;
      let province = address.split("/")[0];
      let city = address.split("/")[1];
      if (city) {
        this.areaListValue = this.getObjectKey(this.areaList.city_list, city);
      } else {
        this.areaListValue = this.getObjectKey(
          this.areaList.province_list,
          province
        );
      }
      this.showAreaList = true;
    },

    // 选择省市地址
    onAreaList(val) {
      this.prefixAddress = val[0].name + "/" + val[1].name;
      this.showAreaList = false;
    },

    // 点击政治面貌
    clickPolityStatus(val) {
      this.polityStatusIndex = this.polityStatusList.findIndex(item => {
        return item.dname == val.target.value;
      });
      this.showPolityStatus = true;
    },

    // 点击选择政治面貌
    onPolityStatus(val) {
      this.form.polityStatus = val.value;
      this.polityStatus = val.dname;
      this.showPolityStatus = false;
    },

    // 获取省市区地址列表
    async getAllAreaInfo() {
      try {
        const res = await getAllAreaInfo();
        let obj = {};
        res.forEach(res => {
          obj[res.code] = res.shortName;
        });
        this.areaList.province_list = obj;

        let city = {};
        res.forEach(item => {
          item.children.forEach(ele => {
            city[ele.code] = ele.shortName;
          });
        });
        this.areaList.city_list = city;
      } catch (error) {
        console.log(error);
      }
    },

    // 上传身份证正面成功
    uploadSuccess(val) {
      this.getOcrInfo(val.fileurl, 1);
    },

    // 上传身份证背面
    uploadSuccessBack(val) {
      this.getOcrInfo(val.fileurl, 2);
    },

    // OCR获取文字信息 type 1 正面，2 反面
    async getOcrInfo(fileurl, type) {
      const obj = {
        type: "idCard",
        url: fileurl
      };
      try {
        const res = await getOcrInfo(obj);
        let result = JSON.parse(res);
        console.log(result);
        if (type == 1) {
          const idCardRegex = /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}(?:\d|X|x)$/;
          let resultData = result.data.face.data;
          if (!idCardRegex.test(resultData.idNumber)) {
            Toast.fail({
              duration: 2000,
              message: "身份证号识别有误，请重新识别或手动添加！"
            });
          }
          this.form.idCard = resultData.idNumber;
          this.form.birthday = this.countAge(resultData.idNumber).birthday;
          this.form.age =
            this.form.idCard && this.countAge(this.form.idCard).age;
          this.form.idCardAddress = resultData.address; //身份证地址
          this.$forceUpdate();
        } else {
          let resultData = result.data.back.data;
          let datetimerange = resultData.validPeriod.split("-");
          this.form.idCardBeginDate = datetimerange[0].replace(/\./g, "/");
          this.form.idCardExpireDate = datetimerange[1].replace(/\./g, "/"); //身份证到期时间

          console.log(this.form.idCardBeginDate, this.form.idCardExpireDate);
          this.$forceUpdate();
        }
      } catch (error) {
        console.log(error);
      }
    },

    //计算生日与年龄
    countAge(data) {
      //获取出生年月日
      const yearBirth = data.substring(6, 10);
      const monthBirth = data.substring(10, 12);
      const dayBirth = data.substring(12, 14);
      //获取当前年月日并计算年龄
      const myDate = new Date();
      const monthNow = myDate.getMonth() + 1;
      const dayNow = myDate.getDate();
      var age = myDate.getFullYear() - yearBirth;
      if (
        monthNow < monthBirth ||
        (monthNow == monthBirth && dayNow < dayBirth)
      ) {
        age--;
      }
      const age_birth = {
        age: age,
        birthday: yearBirth + "-" + monthBirth + "-" + dayBirth
      };
      //返回年龄
      return age_birth;
    },

    // 字典项
    async getAllDict() {
      try {
        const res = await getAllDict();
        // console.log(res);
        this.staffNationalityList =
          res.find(item => item.type === "nationality").children || []; //民族
        this.polityStatusList =
          res.find(item => item.type === "polity_status").children || []; //政治面貌
        this.maritalStatusList =
          res.find(item => item.type === "marital_status").children || []; //婚姻状况
        this.staffStateList =
          res.find(item => item.type === "staff_state").children || []; //员工状态

        this.getStaffInfo(); //调完字典项后调详情，保证字典项渲染
      } catch (error) {
        console.log(error);
      }
    },

    // 查询员工信息
    async getStaffInfo() {
      try {
        const res = await getStaffInfo({ id: this.$route.query.id });
        this.form = { ...res };
        // 头像
        let iconUrl = [];
        let iconFrontUrl = [];
        let iconBacktUrl = [];

        iconUrl.push({
          fileUrl: this.form.headPhotoUrl || "",
          fileurl: this.form.headPhotoUrl || "",
          progress: 100
        });
        iconFrontUrl.push({
          fileUrl: this.form.idCardFrontUrl || "",
          fileurl: this.form.idCardFrontUrl || "",
          progress: 100
        });
        iconBacktUrl.push({
          fileUrl: this.form.idCardBackUrl || "",
          fileurl: this.form.idCardBackUrl || "",
          progress: 100
        });
        this.iconUrlArr = this.form.headPhotoUrl ? iconUrl : [];
        this.iconFrontUrlArr = this.form.idCardFrontUrl ? iconFrontUrl : [];
        this.iconBackUrlArr = this.form.idCardBackUrl ? iconBacktUrl : [];
        // 身份证

        // 直属上级
        this.directLeader = this.form.directLeaderNum
          ? `${this.form.directLeaderNum} - ${this.form.directLeader}`
          : this.form.directLeader;

        this.form.idCardBeginDate =
          this.form.idCardBeginDate &&
          this.form.idCardBeginDate.replace(/-/g, "/");
        this.form.idCardExpireDate =
          this.form.idCardExpireDate &&
          this.form.idCardExpireDate.replace(/-/g, "/"); //身份证到期时间

        // 地址
        this.prefixAddress = res.currentAddress
          ? res.currentAddress
              .split("/")
              .slice(0, 2)
              .join("/")
          : "";
        this.suffixAddress = res.currentAddress
          ? res.currentAddress
              .split("/")
              .slice(2)
              .join("")
          : "";

        // 民族
        let nationalityTemp = this.staffNationalityList.find(item => {
          return item.value == res.nationality;
        });
        this.nationality = nationalityTemp && nationalityTemp.dname;

        // 员工状态
        let stateTemp = this.staffStateList.find(item => {
          return item.value == res.state;
        });
        this.state = stateTemp && stateTemp.dname;

        //婚姻状态
        let maritalStatusTemp = this.maritalStatusList.find(item => {
          return item.value == res.maritalStatus;
        });
        this.maritalStatus = maritalStatusTemp && maritalStatusTemp.dname;

        // 政治面貌
        let polityStatusTemp = this.polityStatusList.find(item => {
          return item.value == res.polityStatus;
        });
        this.polityStatus = polityStatusTemp && polityStatusTemp.dname;

        // 岗位
        res &&
          res.postList.forEach(item => {
            item.postId = item.id;
          });
        this.form.postRelationList = res.postList;
        // 职务
        res &&
          res.dutyList.forEach(item => {
            item.dutiesId = item.id;
          });
        this.form.dutyRelationList = res.dutyList;
        // 组织架构
        res &&
          res.deptList.forEach(item => {
            item.departmentId = item.id;
          });
        this.form.deptRelationList = res.deptList;

        // 处理岗位
        this.postMain = this.form.postRelationList.find(
          item => item.isMain == 1
        )
          ? this.form.postRelationList.find(item => item.isMain == 1).postName
          : "";
        // 处理职务
        this.dutyMain = this.form.dutyRelationList.find(
          item => item.isMain == 1
        )
          ? this.form.dutyRelationList.find(item => item.isMain == 1).dutiesName
          : "";
        // 处理组织架构
        this.deptMain =
          this.form.deptRelationList.length > 0
            ? this.form.deptRelationList[0].fullName
            : "";
      } catch (error) {
        console.log(error);
      }
    },

    // 保存
    async updateStaff() {
      this.form.joinDate = this.$moment(this.form.joinDate).format(
        "YYYY-MM-DD HH:mm:ss"
      ); //入职日期
      this.form.birthday = this.$moment(this.form.birthday).format(
        "YYYY-MM-DD"
      ); //生日

      this.form.idCardBeginDate =
        this.form.idCardBeginDate &&
        this.form.idCardBeginDate.replace(/\//g, "-");
      this.form.idCardExpireDate =
        this.form.idCardExpireDate &&
        this.form.idCardExpireDate.replace(/\//g, "-"); //身份证到期时间

      this.form.currentAddress = this.prefixAddress + "/" + this.suffixAddress;

      // 基础信息校验
      if (
        !this.form.aliasName ||
        !this.form.sex ||
        !this.form.nativePlace ||
        !this.form.nationality ||
        !this.form.polityStatus ||
        !this.form.idCard ||
        !this.form.birthday ||
        !this.form.idCardBeginDate ||
        !this.form.idCardExpireDate ||
        !this.form.idCardAddress
      ) {
        return Toast.fail({
          duration: 2000,
          message: "请按要求填写完整信息！"
        });
      }

      if (!this.form.maritalStatus) {
        return Toast.fail({
          duration: 2000,
          message: "婚姻状况不能为空！"
        });
      }

      // 证件照
      if (!this.form.headPhotoUrl) {
        return Toast.fail({
          duration: 2000,
          message: "证件照不能为空！"
        });
      }
      // 居住地址
      if (!this.prefixAddress || !this.suffixAddress) {
        return Toast.fail({
          duration: 2000,
          message: "居住地址不能为空！"
        });
      }

      let params = {
        id: this.$route.query.id,
        ...this.form
      };
      try {
        const res = await updateStaff(params);
        console.log(res);
        this.$router.push({
          name: "mainExperience",
          query: {
            id: this.$route.query.id
          }
        });
      } catch (error) {
        console.log(error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.baseInfo-page {
  height: 100%;
  width: 100%;
  background: #fff;

  .uploadPhoto {
    position: relative;
    height: 54px;
    display: flex;
    // justify-content: space-between;
    align-items: center;

    label {
      display: inline-block;
      font-size: 3.7234vw;
      width: 120px;
      box-sizing: border-box;
      margin-right: 5.1vw;
      color: #646566;
      text-align: left;
      word-wrap: break-word;
      padding: 0 4.26667vw;
    }

    .txt {
      width: 72px;
      height: 17px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #b2b2b2;
      text-align: center;
    }
  }

  .id-box {
    // width: 343px;
    height: 162px;
    background: #f7f7f7;
    border-radius: 8px;
    margin: 0 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    .bg-head {
      width: 100px;
      height: 130px;
      background: #fff;
      background-image: url("../../../assets/hrm/img_head.png");
      background-repeat: no-repeat;
      background-size: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;

      img {
        margin-bottom: 8.66667vw;
      }
    }
  }

  .id-card {
    // width: 343px;
    margin: 0 16px;
    height: 204px;
    background: #f7f7f7;
    border-radius: 8px;
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
    align-items: center;

    .id-card-bg {
      width: 289px;
      height: 172px;
      background: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      background-image: url("../../../assets/hrm/ico_a.png");
      background-repeat: no-repeat;
      background-size: 100%;
      position: relative;
      border-radius: 8px;

      .id-img-box {
        // display: flex;
        // flex-direction: column;
        // justify-content: center;
        // align-items: center;
        width: 100%;
        height: 100%;
        position: absolute;

        p {
          width: 84px;
          height: 20px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #333333;
          text-align: center;
          line-height: 20px;
          margin-top: 8px;
        }
      }

      img {
        width: 42px;
        height: 42px;
      }
    }
  }

  .id-card-back {
    margin: 0 16px;
    height: 204px;
    background: #f7f7f7;
    border-radius: 8px;
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
    align-items: center;

    .id-card-bg {
      width: 289px;
      height: 172px;
      background: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      background-image: url("../../../assets/hrm/ico_b.png");
      background-repeat: no-repeat;
      background-size: 100%;
      position: relative;
      border-radius: 8px;

      .id-img-box {
        width: 100%;
        height: 100%;
        position: absolute;

        p {
          width: 84px;
          height: 20px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #333333;
          text-align: center;
          line-height: 20px;
          margin-top: 8px;
        }
      }

      img {
        width: 42px;
        height: 42px;
      }
    }
  }
}
/deep/.van-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
}

.duringSchool {
  position: relative;
  height: 54px;
  display: flex;
  // justify-content: space-between;
  align-items: center;

  label {
    display: inline-block;
    // width: 95.8px;
    font-size: 3.7234vw;
    box-sizing: border-box;
    margin-right: 5.1vw;
    color: #646566;
    text-align: left;
    word-wrap: break-word;
    padding: 0 4.26667vw;
  }

  .time {
    flex: 1;
    display: flex;
    align-items: center;
    span {
      width: 41.5px;
      color: #646566;
    }

    p {
      width: 83.5px;
      color: #c8c9cc;
      font-size: 3.7234vw;
    }

    .txtTime {
      color: #333333;
    }
  }
}

.must {
  &::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1.5;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
}
.no-must {
  &::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #fff;
  }
}
/deep/.van-field__control {
  min-width: 200px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
</style>
