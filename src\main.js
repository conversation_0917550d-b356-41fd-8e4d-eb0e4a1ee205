import Vue from "vue";
import App from "./index.vue";
import router from "./router";
import store from "./store";
// import VConsole from "vconsole/dist/vconsole.min.js";
import "./webview";
import "./plugins/moment";
import <PERSON>ie from "js-cookie";
import i18n from "./i18n";
import InfiniteLoading from "vue-infinite-loading";
import VueClipboard from "vue-clipboard2";
import { Toast } from "vant";

window.ovopark.ready(() => {
  Vue.use(InfiniteLoading, {
    /* options */
  });
  Vue.use(Toast);

  // require('/node_modules/video.js/dist/video-js.css')

  // let vConsole = new VConsole(); // eslint-disable-line no-unused-vars

  Vue.config.productionTip = false;

  router.beforeEach((to, from, next) => {
    const {
      token,
      nToken,
      groupId,
      mac,
      service,
      remark,
      deptld,
      sameEnterprise
    } = to.query;
    let defaultToken = nToken || token;
    if (defaultToken) {
      Cookie.set("token", defaultToken);
    }
    if (to.meta.title) {
      document.title = to.meta.title;
    }

    if (groupId && deptld && groupId != deptld) {
      Toast.fail({
        duration: 2000,
        message: "请使用客户端同一企业账号登录激活"
      });
      next({
        name: "enterNetworkLogin",
        query: {
          groupId,
          mac,
          service,
          remark,
          deptId: deptld,
          sameEnterprise: true
        }
      });
    }

    if (
      to.name == "enterNetworkLogin" &&
      defaultToken &&
      groupId &&
      !sameEnterprise
    ) {
      next({
        name: "enterNetworkRegister",
        query: {
          activeRemark: remark,
          macAddress: mac,
          productCode: service,
          groupId: groupId,
          deptId: deptld
        }
      });
    }
    next();
  });

  // app主动刷新token
  window.setNewToken = (
    token,
    tokenExpirationTimestamp,
    refreshExpirationTimestamp
  ) => {
    Cookie.set("token", token);
    Cookie.set("tokenExpirationTimestamp", tokenExpirationTimestamp);
    Cookie.set("refreshExpirationTimestamp", refreshExpirationTimestamp);
  };

  // Vue.use(VideoPlayer)
  Vue.use(VueClipboard);

  new Vue({
    render: h => h(App),
    router,
    store,
    i18n
  }).$mount("#app");
});
