<template>
  <div class="receivable_list">
    <!-- table header -->
    <div class="table_header">
      <div>{{ title }}</div>
      <div @click="sortArrearsSort()">
        欠款(万)
        <img style="width: 12px; height: 12px;" :src="activeIcon" alt="" />
      </div>
      <div @click="sortOverdueSortSort()">
        逾期(万)
        <img style="width: 12px; height: 12px;" :src="activeOverIcon" alt="" />
      </div>
    </div>
    <!-- content -->
    <div class="table_body" v-if="showContent">
      <div
        class="table_content"
        :class="{ active: activeIndex === index }"
        v-for="(item, index) in detail.list"
        :key="index"
        @click="changeCell(index, item)"
      >
        <div class="table_title">
          {{ item.showName }}
        </div>

        <div class="table_arrears">
          {{ item.arrearsAmount }}
          <span style="display: inline-block; width: 12px;"></span>
        </div>
        <div class="table_overdue">
          {{ item.overdueMoney }}
          <img
            style="width: 12px; height: 12px;"
            src="../../../assets/img/ico_rightarrow.png"
            alt=""
          />
        </div>
      </div>
    </div>
    <van-empty description="暂无数据" v-else />
  </div>
</template>
<script>
import { getCountOverdueBillByUser } from "@/api/delivery";
import { Empty, Toast } from "vant";
export default {
  components: {
    [Empty.name]: Empty,
    [Toast.name]: Toast
  },

  props: {
    type: {
      type: [String, Number],
      default: ""
    },
    isSuper: {
      type: [String, Number],
      default: ""
    }
  },
  data() {
    return {
      activeIcon:
        "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/05/ico_sort1.png",
      activeOverIcon:
        "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/05/ico_sort1.png",
      activeIndex: -1,
      detail: {},
      showContent: false,
      arrearsSort: "", //欠款排序 (1欠款金额正序，2欠款金额倒序)
      overdueSort: "" //逾期排序 (1逾期金额正序，2逾期金额正序)
    };
  },
  watch: {
    type(val) {
      console.log(val, "type");
      this.getCountOverdueBillByUser();
    },
    // 改为深度监听
    "$store.state.accountDate": {
      handler: function() {
        this.getCountOverdueBillByUser();
      },
      deep: true //深度监听设置为 true
    },
    overdueSort(val) {
      if (!val) {
        this.activeOverIcon =
          "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/05/ico_sort1.png";
      }
    },
    arrearsSort(val) {
      if (!val) {
        this.activeIcon =
          "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/05/ico_sort1.png";
      }
    }
  },

  computed: {
    title() {
      let name = "";
      if (this.isSuper == 1 && this.type == 2) {
        name = "客户名称";
      } else if (this.isSuper == 2 && this.type == 0) {
        name = "组织架构";
      } else if (this.isSuper == 2 && this.type == 2) {
        name = "名称";
      }
      return name;
    }
  },
  mounted() {
    this.getCountOverdueBillByUser();
  },
  methods: {
    async getCountOverdueBillByUser() {
      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        loadingType: "spinner"
      });
      try {
        const res = await getCountOverdueBillByUser({
          type: this.type,
          startDate: this.$store.state.accountDate.startDate,
          endDate: this.$store.state.accountDate.endDate,
          arrearsSort: this.arrearsSort,
          overdueSort: this.overdueSort,
          isSuper: this.isSuper
        });
        res?.list.forEach(item => {
          if (this.isSuper == 1 && this.type == 2) {
            item.showName = item.customerName;
          } else if (this.isSuper == 2 && this.type == 0) {
            item.showName = item.depName;
          } else if (this.isSuper == 2 && this.type == 2) {
            item.showName = item.userName;
          }
        });
        this.detail = res;

        this.showContent = res?.list.length > 0;
        this.$store.commit("setArrearsAmount", res.arrearsAmount);
        this.$store.commit("setOverdueMoney", res.overdueMoney);
        Toast.clear();
      } catch (error) {
        console.log(error);
      }
    },

    changeCell(index, item) {
      this.activeIndex = index;
      if (this.type == 0 && this.isSuper == 2) {
        // 获取当前路由的全部参数，包括动态路由和查询参数 存到vuex中，用来做历史记录
        let params = {
          name: item.depName,
          index: this.$route.meta.index
        };
        this.$store.commit("setSelectedHistory", params);

        this.$router.replace({
          name: "accountSecond",
          query: {
            depId: item.depId,
            type: this.type
          }
        });
      } else if (this.type == 2 && this.isSuper == 2) {
        console.log(item, index);
        let params = {
          name: item.userName,
          index: this.$route.meta.index
        };
        this.$store.commit("setSelectedHistory", params);

        this.$router.replace({
          name: "accountFourth",
          query: {
            userCode: item.userCode,
            type: this.type
          }
        });
      }
    },

    // 欠款排序
    sortArrearsSort() {
      this.overdueSort = "";
      if (this.arrearsSort == "") {
        this.arrearsSort = 1;
      } else if (this.arrearsSort == 1) {
        this.arrearsSort = 2;
      } else if (this.arrearsSort == 2) {
        this.arrearsSort = 1;
      }
      if (this.arrearsSort == 1) {
        this.activeIcon =
          "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/09/ico_sort2.png";
      } else if (this.arrearsSort == 2) {
        this.activeIcon =
          "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/09/ico_sort3.png";
      }

      this.getCountOverdueBillByUser();
    },

    // 逾期排序
    sortOverdueSortSort() {
      this.arrearsSort = "";
      if (this.overdueSort == "") {
        this.overdueSort = 1;
      } else if (this.overdueSort == 1) {
        this.overdueSort = 2;
      } else if (this.overdueSort == 2) {
        this.overdueSort = 1;
      }
      if (this.overdueSort == 1) {
        this.activeOverIcon =
          "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/09/ico_sort2.png";
      } else if (this.overdueSort == 2) {
        this.activeOverIcon =
          "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/09/ico_sort3.png";
      }

      this.getCountOverdueBillByUser();
    }
  }
};
</script>
<style scoped lang="scss">
@import "../account.scss";
</style>
