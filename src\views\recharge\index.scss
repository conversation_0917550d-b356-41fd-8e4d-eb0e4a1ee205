.recharge-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-image: url("../../assets/recharge-bg.png");
  background-size: 100%;
  background-repeat: no-repeat;
  header {
    padding: 0 16px;
    // padding-top: 48px;
    .status-bar {
      height: 44px;
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      img {
        width: 20px;
        height: 20px;
      }
    }

    .recharge_account {
      width: 343px;
      height: 108px;
      background-image: linear-gradient(180deg, #ff6a00 0%, #ff9900 100%);
      border-radius: 6px 6px 0 0;
      padding: 16px 12px;
      box-sizing: border-box;

      p {
        width: 56px;
        height: 22px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #ffffffcc;
        line-height: 22px;
        margin-bottom: 16px;
      }

      .account_message {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .amount {
          height: 32px;
          font-family: D-DIN-PRO-SemiBold;
          font-weight: 600;
          font-size: 28px;
          color: #ffffff;
          line-height: 32px;
          // 超出部分显示。。。
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .fade-line {
            position: relative;
            padding-bottom: 10px;
          }

          .fade-line::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(
              90deg,
              #fff 20%,
              rgba(255, 255, 255, 0.2) 80%
            );
          }
        }
      }
      .icon {
        width: 20px;
        height: 20px;
      }
    }
  }

  main {
    flex: 1;
    padding: 16px;
    border-radius: 16px 16px 0 0;
    background-color: #ffffff;

    .input_box {
      height: 56px;
      background: #ffffff;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      margin-bottom: 17px;

      p {
        height: 24px;
        font-family: PingFangSC-Medium Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        line-height: 24px;
      }

      .r_input_box {
        display: flex;
        align-items: flex-end;
        // 可以添加以下样式来调整对齐
        line-height: normal; // 确保行高正常
        vertical-align: bottom; // 垂直对齐到底部

        .r_input {
          flex: 1;
          margin-right: 5px;
          // 添加以下样式来调整输入框的内边距
          padding-bottom: 0; // 确保输入框底部没有额外的内边距
          line-height: inherit; // 继承父元素的行高
          vertical-align: inherit; // 继承父元素的垂直对齐方式
        }

        span {
          width: 14px;
          height: 20px;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          font-size: 14px;
          color: #333333;
          // 添加以下样式来调整span的垂直对齐
          line-height: inherit; // 继承父元素的行高
          vertical-align: inherit; // 继承父元素的垂直对齐方式
        }
      }
    }

    .item_box {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-bottom: 16px;

      .item {
        width: 109px;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ffffff;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        margin-bottom: 8px;
        font-family: D-DIN-PRO-Medium;
        font-size: 20px;
        box-sizing: border-box;
        color: #333333;

        span {
          width: 12px;
          font-family: PingFangSC-Medium Microsoft YaHei;
          font-weight: 500;
          font-size: 12px;
          color: #333333;
          margin-left: 2px;
        }
      }

      .active {
        border: 2px solid #ff9900;
        color: #ff9900;

        span {
          color: #ff9900;
        }
      }
    }

    .payment {
      .title {
        width: 64px;
        height: 24px;
        font-family: PingFangSC-Medium Microsoft YaHei;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 24px;
        margin-bottom: 21px;
      }

      .wx {
        font-family: PingFangSC-Medium Microsoft YaHei;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 22px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        div {
          display: flex;
          align-items: center;

          span {
            margin-left: 8px;
            font-family: PingFangSC-Medium Microsoft YaHei;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            line-height: 22px;
          }
        }

        img {
          width: 20px;
          height: 20px;
        }
      }
    }
  }

  .footer {
    margin-top: 48px;
    height: 48px;
    font-family: PingFangSC-Medium Microsoft YaHei;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    line-height: 24px;

    .btn {
      width: 343px;
      height: 48px;
      background: #ff9900;
      border-radius: 6px;
      color: #fff;
    }
  }

  //   去除原生的input框样式
  input {
    -webkit-appearance: none;
    appearance: none;
    outline: none;
    border: none;

    font-family: D-DIN-PRO-SemiBold;
    font-weight: 600;
    font-size: 20px;
    color: #ff9900;
    text-align: right;
  }

  // 去除原生的input框的placeholder样式
  input::-webkit-input-placeholder {
    height: 32px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 20px;
    color: #cccccc;
    text-align: right;
  }
}

.popChange {
  display: flex;
  flex-direction: column;
  align-items: center;
  .header {
    width: 100%;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    box-sizing: border-box;

    p {
      height: 24px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: #000000;
      line-height: 24px;
      text-align: center;
      flex: 1;
    }
    img {
      width: 16px;
      height: 16px;
    }
  }

  .item {
    position: relative;
    width: 343px;
    height: 56px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    padding: 17px 16px;
    box-sizing: border-box;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 22px;
    margin-bottom: 16px;

    .item_footer {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 24px;
      height: 24px;
      background: #ff9900;
      border-radius: 6px 0 6px 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .active {
    border: 1px solid #ff9900;
    color: #ff9900;
  }
}

// 引入字体文件
@font-face {
  font-family: "D-DIN-PRO-Bold";
  src: url("../../assets/fonts/D-DIN-PRO-Bold.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "D-DIN-PRO-ExtraBold";
  src: url("../../assets/fonts/D-DIN-PRO-ExtraBold.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "D-DIN-PRO-Heavy";
  src: url("../../assets/fonts/D-DIN-PRO-Heavy.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "D-DIN-PRO-Medium";
  src: url("../../assets/fonts/D-DIN-PRO-Medium.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "D-DIN-PRO-Regular";
  src: url("../../assets/fonts/D-DIN-PRO-Regular.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "D-DIN-PRO-SemiBold";
  src: url("../../assets/fonts/D-DIN-PRO-SemiBold.otf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
