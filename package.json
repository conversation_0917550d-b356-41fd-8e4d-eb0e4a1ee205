{"name": "delivery-notice", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "build:testing": "vue-cli-service build --mode testing"}, "dependencies": {"@vant/area-data": "^1.5.1", "ali-oss": "^6.17.1", "axios": "^0.21.1", "babel-plugin-import": "^1.13.0", "core-js": "^3.6.5", "echarts": "^5.6.0", "js-cookie": "^3.0.1", "jsqr": "^1.4.0", "moment": "^2.29.1", "qs": "^6.9.6", "quasar": "^2.10.1", "vant": "^2.12.4", "vue": "^2.6.11", "vue-clipboard2": "^0.3.3", "vue-i18n": "^8.27.1", "vue-infinite-loading": "^2.4.5", "vue-router": "^3.4.9", "vue-video-player": "^6.0.0", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-plugin-eslint": "~4.4.0", "@vue/cli-plugin-router": "~4.4.0", "@vue/cli-service": "~4.4.0", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "node-sass": "^4.12.0", "postcss": "^7.0.32", "postcss-px-to-viewport": "^1.1.1", "prettier": "^1.19.1", "sass-loader": "^8.0.2", "vconsole": "^3.14.6", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended", "prettier"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}