.contract-repayment {
    background-color: #f5f7f9;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  
    .header {
      flex: 0 0 auto;
  
      ::v-deep .van-nav-bar .van-icon {
        color: #333;
        font-size: 18px;
      }
    }
  
    .content-container {
      flex: 1;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      padding-bottom: 20px;
    }
  
    :deep(.van-nav-bar) {
      background-color: #fff;
  
      .van-nav-bar__arrow {
        color: #333333;
        font-size: 24px;
      }
    }
  
    .section-title {
      padding: 0 16px;
      height: 48px;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      font-size: 16px;
      color: #000000;
      line-height: 48px;
    }
  
    .personal-data {
      background: #fff;
      margin-bottom: 12px;
    }
  
    .filter-tabs {
      padding: 0 16px 16px;
  
      .tab-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
      }
  
      .tab-item {
        display: flex;
        align-items: center;
        padding: 5px 15px;
        font-size: 14px;
        color: #323233;
        background: #f7f8fa;
        border-radius: 20px;
        cursor: pointer;
        border: 1px solid transparent;
  
        &.active {
          color: #ff9900;
          border: 1px solid #ff9900;
          background: rgba(255, 153, 0, 0.05);
        }
  
        .van-icon {
          margin-left: 5px;
        }
      }
    }
  
    .table-container {
      background: #fff;
      margin-bottom: 12px;
      position: relative;
      overflow: hidden;

      // 主页面固定表格样式
      .fixed-table-container {
        display: flex;
        flex-direction: column;
        background: #fff;
        overflow: hidden;
      }

      .fixed-table-header {
        flex: 0 0 auto;
        background: #f9f9f9;
        border-bottom: 1px solid #ebedf0;
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;

        &::-webkit-scrollbar {
          height: 0;
          width: 0;
          display: none;
        }

        .table-header-row {
          display: flex;
          min-width: 780px;
          height: 44px;
          align-items: center;
          font-size: 14px;
          color: #323233;
          font-weight: 500;

          .column-amount {
            flex: 1;
            min-width: 100px;
            padding: 0 12px;
            text-align: center;
            cursor: pointer;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            .column-title {
              flex: 1;
              white-space: nowrap;
            }

            .sort-icons {
              display: flex;
              flex-direction: column;
              margin-left: 4px;

              .sort-icon {
                font-size: 12px;
                color: #c8c9cc;
                line-height: 8px;
                height: 8px;

                &.active {
                  color: #ff9900;
                }
              }
            }

            &.column-name {
              min-width: 180px;
              max-width: 180px;
              width: 180px;
              flex: none;
              padding: 0 12px;
              text-align: left;
              justify-content: flex-start;
              cursor: default;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              box-sizing: border-box;
            }
          }
        }
      }

      .scrollable-table-body {
        flex: 1;
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        max-height: 220px; // 限制主页面表格高度

        &::-webkit-scrollbar {
          width: 0;
          height: 0;
          display: none;
        }

        .table-row {
          display: flex;
          min-width: 780px;
          height: 44px;
          align-items: center;
          border-bottom: 1px solid #ebedf0;
          font-size: 14px;
          color: #646566;
          cursor: pointer;
          transition: background-color 0.2s;

          &:active {
            background-color: #f7f8fa;
          }

          .column-amount {
            flex: 1;
            min-width: 100px;
            padding: 0 12px;
            text-align: center;
            white-space: nowrap;

            &.column-name {
              min-width: 180px;
              max-width: 180px;
              width: 180px;
              flex: none;
              padding: 0 12px;
              text-align: left;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              box-sizing: border-box;
            }
          }
        }
      }

      .fixed-table-footer {
        flex: 0 0 auto;
        background: #f9f9f9;
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;

        &::-webkit-scrollbar {
          height: 0;
          width: 0;
          display: none;
        }

        .table-footer-row {
          display: flex;
          min-width: 780px;
          height: 44px;
          align-items: center;
          font-size: 14px;
          color: #323233;
          font-weight: 600;

          .column-amount {
            flex: 1;
            min-width: 100px;
            padding: 0 12px;
            text-align: center;
            white-space: nowrap;

            &.column-name {
              min-width: 180px;
              max-width: 180px;
              width: 180px;
              flex: none;
              padding: 0 12px;
              text-align: left;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              box-sizing: border-box;
            }
          }
        }
      }
  
      .table-main {
        position: relative;
        width: 100%;
      }
  
      .table-scroll-container {
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch; /* 提升移动端滚动体验 */
        scrollbar-width: none; /* Firefox */
  
        /* 隐藏滚动条但保留滚动功能 */
        &::-webkit-scrollbar {
          height: 0;
          width: 0;
          display: none;
        }
      }
  
      .table-content {
        min-width: 780px; /* 确保足够的内容宽度触发滚动 */
        width: max-content;
      }
  
      .table-header,
      .table-row {
        display: flex;
        border-bottom: 1px solid #ebedf0;
        padding: 0;
        font-size: 14px;
        align-items: center;
        height: 44px;
        box-sizing: border-box;
        width: 100%;
      }
  
      .table-header {
        color: #323233;
        font-weight: 500;
        background: #f9f9f9;
      }
  
            .column-amount {
        flex: 1;
        min-width: 100px;
        padding: 0 12px;
        text-align: center;
        white-space: nowrap;
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        // 表头排序样式
        .column-title {
          flex: 1;
          white-space: nowrap;
        }

        .sort-icons {
          display: flex;
          flex-direction: column;
          margin-left: 4px;

          .sort-icon {
            font-size: 12px;
            color: #c8c9cc;
            line-height: 8px;
            height: 8px;

            &.active {
              color: #ff9900;
            }
          }
        }
      }
  
      // 客户名称列的特殊样式
      .column-name {
        min-width: 180px;
        max-width: 180px;
        width: 180px;
        flex: none;
        padding: 0 12px;
        text-align: left;
        justify-content: flex-start;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        box-sizing: border-box;
      }
  
      .column-qty,
      .column-percent {
        flex: 1;
        min-width: 100px;
        padding: 0 12px;
        text-align: center;
        white-space: nowrap;
      }
  
      .column-op {
        width: 50px;
        flex: none;
        display: flex;
        align-items: center;
        justify-content: center;
  
        .arrow-icon {
          color: #969799;
          font-size: 16px;
        }
      }
  
      .table-fixed-right {
        position: absolute;
        top: 0;
        right: 0;
        width: 50px;
        height: 100%;
        background-color: #fff;
        z-index: 10;
        box-shadow: -4px 0 6px -4px rgba(0, 0, 0, 0.08);
  
        .table-header,
        .table-row {
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 44px;
          box-sizing: border-box;
          border-bottom: 1px solid #ebedf0;
        }
  
        .column-op {
          width: 100%;
          text-align: center;
  
          .arrow-icon {
            color: #969799;
            font-size: 16px;
          }
        }
  
        /* 添加一个渐变边缘效果 */
        &:before {
          content: "";
          position: absolute;
          top: 0;
          left: -10px;
          width: 10px;
          height: 100%;
          z-index: 11;
        }
      }
  
      .table-row {
        color: #646566;
  
        &:last-child {
          font-weight: normal;
          background-color: transparent;
        }
  
        &:not(:last-child) {
          cursor: pointer;
          transition: background-color 0.2s;
  
          &:active {
            background-color: #f7f8fa;
          }
        }
      }
  
      // 添加查看更多按钮样式
      .view-more-row {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 44px;
        width: 100%;
        color: #ff9900;
        font-size: 14px;
        background-color: #fff;
        border-top: 1px dashed #ebedf0;
        cursor: pointer;
  
        span {
          margin-right: 4px;
        }
  
        &:active {
          background-color: #f7f8fa;
        }
      }
  
      .view-more-row-placeholder {
        height: 44px;
        width: 100%;
        border-top: 1px dashed #ebedf0;
      }
  
      // 添加查看更多按钮样式
      .view-more-btn-container {
        width: 100%;
        background-color: #fff;
        position: relative;
        z-index: 5;
      }
  
      .view-more-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 44px;
        width: 100%;
        color: #999999;
        font-size: 14px;
        cursor: pointer;
  
        span {
          margin-right: 4px;
        }
  
        &:active {
          background-color: #f7f8fa;
        }
      }
    }
  
    .chart-section {
      .chart-container {
        margin-bottom: 12px;
        background: #fff;
        padding: 16px;
  
        &:last-child {
          margin-bottom: 0;
        }
      }
  
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20px;
  
        .title-area {
          .chart-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 12px;
          }
  
          .unit-text {
            font-size: 12px;
            color: #999;
          }
        }
  
        .legend-area {
          display: flex;
          align-items: center;
  
          .legend-item {
            display: flex;
            align-items: center;
            margin-left: 16px;
  
            &:first-child {
              margin-left: 0;
            }
  
            .legend-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              margin-right: 4px;
  
              &.contract-dot {
                background-color: #ff9900;
              }
  
              &.repayment-dot {
                background-color: #4ecb73;
              }
  
              &.prev-year-dot {
                background-color: #ff9900;
              }
  
              &.current-year-dot {
                background-color: #ffd9a0;
              }
            }
  
            span {
              font-size: 12px;
              color: #666;
            }
          }
        }
      }
  
      .chart {
        height: 300px;
        margin-bottom: 0;
      }
    }
  
    // 弹窗样式
    .date-popup {
      padding: 0;
      position: relative;
      height: 100%;
      display: flex;
      flex-direction: column;
  
      .popup-header {
        position: relative;
        padding: 20px 0;
      }
  
      .date-popup-title {
        text-align: center;
        font-size: 16px;
        font-weight: 500;
      }
  
      .close-icon {
        position: absolute;
        top: 20px;
        right: 16px;
        font-size: 16px;
        color: #323233;
      }
  
      .date-type-tabs {
        display: flex;
        flex-wrap: wrap;
        padding: 0 16px;
        // flex: 1;
  
        .date-type-item {
          width: calc(25% - 12px);
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 6px 12px;
          border-radius: 4px;
          font-size: 14px;
          background: #f2f3f5;
          color: #323233;
  
          &.active {
            background-color: #ff9900;
            color: #fff;
          }
        }
      }
    }
  
    .custom-date-popup {
      padding: 0;
      position: relative;
      display: flex;
      flex-direction: column;
      background: #fff;
      overflow-x: hidden; // 禁止横向滚动
  
      .popup-header {
        position: relative;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #ebedf0;
      }
  
      .date-popup-title {
        text-align: center;
        font-size: 16px;
        font-weight: 500;
        color: #000;
      }
  
      .close-icon {
        position: absolute;
        top: 50%;
        right: 16px;
        transform: translateY(-50%);
        font-size: 18px;
        color: #323233;
        padding: 8px;
        margin-right: -8px;
      }
  
      .date-input-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        margin-bottom: 0;
        background: #fff;
  
        .date-input {
          flex: 1;
          max-width: 155px;
          height: 36px;
  
          .custom-date-input {
            width: 100%;
            height: 36px;
            padding: 0;
            border: 1px solid #dcdcdc;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            background: #fff;
            text-align: center;
            outline: none;
            box-sizing: border-box;
            
            &.active {
              border-color: #ff9900;
            }
            
            &.selected {
              color: #ff9900;
            }

            &.disabled {
              opacity: 0.6;
              pointer-events: none;
              background-color: #f5f5f5;
              border: 1px solid #dcdcdc;
              color: #999;
            }

            &::placeholder {
              color: #999;
            }
          }
        }
  
        .date-separator {
          padding: 0 8px;
          color: #969799;
          flex: 0 0 auto;
        }
      }
  
      .date-picker-container {
        display: flex;
        justify-content: space-between;
        height: 250px;
        margin: 0;
        border-top: 1px solid #f2f3f5;
        overflow-x: hidden; // 禁止横向滚动
  
        .date-picker-column {
          flex: 1;
          overflow-y: auto;
          overflow-x: hidden; // 禁止横向滚动
          -webkit-overflow-scrolling: touch;
          text-align: center;
  
          &::-webkit-scrollbar {
            display: none;
          }
  
          &:not(:last-child) {
            border-right: 1px solid #f2f3f5;
          }
  
          .date-picker-item {
            height: 44px;
            line-height: 44px;
            color: #999;
            font-size: 14px;
            padding: 0 4px;
            margin: 0 auto;
            width: 80%;
  
            &.active {
              color: #333;
              font-weight: 500;
              position: relative;
              
              &:after {
                content: '';
                position: absolute;
                left: 50%;
                bottom: 7px;
                width: 4px;
                height: 4px;
                background-color: #ff9900;
                border-radius: 50%;
                transform: translateX(-50%);
              }
            }
          }
        }
      }
  
      .date-confirm-btn {
        margin: 16px;
  
        .van-button {
          height: 44px;
          background: #ff9900;
          border-color: #ff9900;
          font-size: 16px;
          border-radius: 4px;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          letter-spacing: 2px;
          
          &:active {
            background: #e68a00;
            border-color: #e68a00;
          }
        }
      }
    }
  
    // 日期选择器样式调整
    :deep(.van-picker) {
      flex: 1;
    }
  
    :deep(.van-picker-column__item) {
      font-size: 17px;
  
      &--selected {
        color: #323233;
        font-weight: 500;
      }
    }
  
    :deep(.van-picker__mask) {
      background-image: linear-gradient(
          180deg,
          hsla(0, 0%, 100%, 0.9),
          hsla(0, 0%, 100%, 0.4)
        ),
        linear-gradient(0deg, hsla(0, 0%, 100%, 0.9), hsla(0, 0%, 100%, 0.4));
    }
  
    // 添加所有数据弹窗样式
    .all-data-popup {
      display: flex;
      flex-direction: column;
      height: 100%;
      background-color: #fff;
  
      .popup-title {
        text-align: center;
        padding: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #323233;
        border-bottom: 1px solid #ebedf0;
      }
  
      .table-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: relative; /* 为固定列添加定位上下文 */
        
        // 添加表格主体样式，与外层表格保持一致
        .table-main {
          position: relative;
          width: 100%;
          flex: 1;
        }
  
        .table-scroll-container {
          overflow-x: auto;
          overflow-y: hidden;
          -webkit-overflow-scrolling: touch;
          scrollbar-width: none; /* Firefox */
  
          /* 隐藏滚动条但保留滚动功能 */
          &::-webkit-scrollbar {
            height: 0;
            width: 0;
            display: none;
          }
        }
  
        .table-content {
          min-width: 680px; /* 确保足够的内容宽度触发滚动 */
          width: max-content;
          margin-bottom: 12px;
          padding-right: 50px; /* 为固定操作列预留空间 */
        }
  
        .table-header,
        .table-row {
          display: flex;
          border-bottom: 1px solid #ebedf0;
          padding: 0;
          font-size: 14px;
          align-items: center;
          height: 44px;
          box-sizing: border-box;
          width: 100%;
        }
  
        .table-header {
          color: #323233;
          font-weight: 500;
          background: #f9f9f9;
  
          .column-amount {
            flex: 1;
            min-width: 70px;
            max-width: 114px;
            padding: 0 12px;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
  
            // 表头排序样式
            .column-title {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
  
            .sort-icons {
              display: flex;
              flex-direction: column;
              margin-left: 4px;
  
              .sort-icon {
                font-size: 12px;
                color: #c8c9cc;
                line-height: 8px;
                height: 8px;
  
                &.active {
                  color: #ff9900;
                }
              }
            }
  
            &:hover {
              background-color: #f7f8fa;
            }
          }
  
                    .column-name {
            min-width: 180px;
            max-width: 180px;
            width: 180px;
            flex: none;
            padding: 0 12px;
            text-align: left;
            justify-content: flex-start;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            box-sizing: border-box;
          }

          .column-op {
            width: 50px;
            flex: none;
            text-align: center;
          }
        }
  
        .table-body {
          flex: 1;
          overflow-y: auto;
          overflow-x: hidden;
          -webkit-overflow-scrolling: touch;
          scrollbar-width: none; /* Firefox */
  
          /* 隐藏滚动条但保留滚动功能 */
          &::-webkit-scrollbar {
            height: 0;
            width: 0;
            display: none;
          }
        }
  
        .table-row {
          display: flex;
          border-bottom: 1px solid #ebedf0;
          padding: 0;
          font-size: 14px;
          align-items: center;
          height: 44px;
          box-sizing: border-box;
          width: 100%;
          color: #646566;
  
          &:not(.summary-row) {
            cursor: pointer;
            transition: background-color 0.2s;
  
            &:active {
              background-color: #f7f8fa;
            }
          }
  
          &.summary-row {
            font-weight: 600;
            background-color: #f5f7f9;
            color: #323233;
          }
  
          .column-amount {
            flex: 1;
            min-width: 70px;
            max-width: 114px;
            padding: 0 12px;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
  
                    .column-name {
            min-width: 180px;
            max-width: 180px;
            width: 180px;
            flex: none;
            padding: 0 12px;
            text-align: left;
            justify-content: flex-start;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            box-sizing: border-box;
          }

          .column-op {
            width: 50px;
            flex: none;
            display: flex;
            align-items: center;
            justify-content: center;
  
            .arrow-placeholder {
              width: 16px;
              height: 16px;
            }
          }
  
          .arrow-icon {
            color: #969799;
            font-size: 16px;
          }
        }
  
        .table-fixed-right {
          position: absolute;
          top: 0;
          right: 0;
          width: 50px;
          height: 100%;
          background-color: #fff;
          z-index: 10;
          box-shadow: -4px 0 6px -4px rgba(0, 0, 0, 0.08);
  
          .table-header,
          .table-row {
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 44px;
            box-sizing: border-box;
            border-bottom: 1px solid #ebedf0;
          }
  
          .table-header {
            background: #f9f9f9;
          }
  
          .table-row.summary-row {
            background-color: #f5f7f9;
          }
  
          .column-op {
            width: 100%;
            text-align: center;
  
            .arrow-icon {
              color: #969799;
              font-size: 16px;
            }
          }
  
          /* 添加一个渐变边缘效果 */
          &:before {
            content: "";
            position: absolute;
            top: 0;
            left: -10px;
            width: 10px;
            height: 100%;
            z-index: 11;
          }
        }
      }
  
      // 设置van-list组件样式
      :deep(.van-list) {
        height: 100%;
        overflow-y: auto;
  
        .van-list__loading,
        .van-list__finished-text {
          padding: 16px 0;
          color: #969799;
          font-size: 14px;
        }
      }
    }
  
      // 弹出层样式优化
  :deep(.van-popup--bottom) {
    border-radius: 16px 16px 0 0;

    &.van-popup--round {
      overflow: hidden;
    }
  }

  // 弹窗样式
  .all-data-popup {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #f5f7f9;

    .popup-title {
      padding: 20px 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #000;
      text-align: center;
    }

    .popup-filter-tabs {
      padding: 16px;
      background: #fff;
      margin: 16px 16px 0;
      border-radius: 8px;

      .tab-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
      }

      .tab-item {
        display: flex;
        align-items: center;
        padding: 5px 15px;
        font-size: 14px;
        color: #323233;
        background: #f7f8fa;
        border-radius: 20px;
        cursor: pointer;
        border: 1px solid transparent;

        &.active {
          color: #ff9900;
          border: 1px solid #ff9900;
          background: rgba(255, 153, 0, 0.05);
        }

        .van-icon {
          margin-left: 5px;
        }
      }
    }

    .fixed-table-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin: 12px 16px 16px;
      background: #fff;
      border-radius: 8px;
      overflow: hidden;
    }

    .fixed-table-header {
      flex: 0 0 auto;
      background: #f9f9f9;
      border-bottom: 1px solid #ebedf0;
      overflow-x: auto;
      overflow-y: hidden;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none;

      &::-webkit-scrollbar {
        height: 0;
        width: 0;
        display: none;
      }

      .table-header-row {
        display: flex;
        min-width: 730px;
        height: 44px;
        align-items: center;
        font-size: 14px;
        color: #323233;
        font-weight: 500;

        .column-amount {
          flex: 1;
          min-width: 100px;
          padding: 0 12px;
          text-align: center;
          cursor: pointer;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;

          .column-title {
            flex: 1;
            white-space: nowrap;
          }

          .sort-icons {
            display: flex;
            flex-direction: column;
            margin-left: 4px;

            .sort-icon {
              font-size: 12px;
              color: #c8c9cc;
              line-height: 8px;
              height: 8px;

              &.active {
                color: #ff9900;
              }
            }
          }

          &.column-name {
            min-width: 150px;
            max-width: 150px;
            width: 150px;
            flex: none;
            padding: 0 15px;
            text-align: left;
            justify-content: flex-start;
            cursor: default;
          }
        }
      }
    }

    .scrollable-table-body {
      flex: 1;
      overflow-x: auto;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none;

      &::-webkit-scrollbar {
        width: 0;
        height: 0;
        display: none;
      }

      .table-row {
        display: flex;
        min-width: 750px;
        height: 44px;
        align-items: center;
        border-bottom: 1px solid #ebedf0;
        font-size: 14px;
        color: #646566;
        cursor: pointer;
        transition: background-color 0.2s;

        &:active {
          background-color: #f7f8fa;
        }

        .column-amount {
          flex: 1;
          min-width: 100px;
          padding: 0 12px;
          text-align: center;
          white-space: nowrap;

          &.column-name {
            min-width: 150px;
            max-width: 150px;
            width: 150px;
            flex: none;
            padding: 0 15px;
            text-align: left;
          }
        }
      }

      .empty-state-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        width: 100%;
      }
    }

    .fixed-table-footer {
      flex: 0 0 auto;
      background: #fff;
      // border-top: 2px solid #ff9900;
      overflow-x: auto;
      overflow-y: hidden;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none;
      background: #F9F9F9;


      &::-webkit-scrollbar {
        height: 0;
        width: 0;
        display: none;
      }

      .table-footer-row {
        display: flex;
        min-width: 750px;
        height: 44px;
        align-items: center;
        font-size: 14px;
        color: #323233;
        font-weight: 600;

        .column-amount {
          flex: 1;
          min-width: 100px;
          padding: 0 12px;
          text-align: center;
          white-space: nowrap;

          &.column-name {
            min-width: 150px;
            max-width: 150px;
            width: 150px;
            flex: none;
            padding: 0 15px;
            text-align: left;
          }
        }
      }
    }
  }
}