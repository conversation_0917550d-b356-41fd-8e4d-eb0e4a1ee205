<template>
  <div class="gl-box">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getTvWallAuthorizationCodeList"
      >
        <div class="code-item" v-for="item in tvList" :key="item.id">
          <div class="title">
            <span class="contract">合同号：{{ item.contractNo }}</span>
            <span
              class="status"
              :class="{
                active: item.activeState == 1
              }"
              >{{ item.activeState == 0 ? "未激活" : "已激活" }}</span
            >
          </div>
          <div class="line"></div>
          <div class="desc">
            <label class="label">软件产品</label>
            <span class="value">{{ item.softwareName }}</span>
          </div>
          <div class="desc">
            <label class="label">有效月数</label>
            <span class="value">{{ item.activeMonthNum }}</span>
          </div>
          <div class="desc">
            <label class="label">到期日期</label>
            <span class="value">{{ item.expirationTime || "-" }}</span>
          </div>
          <div class="auth-code">
            <p>授权码</p>
            <div class="auth-box">
              <span class="value">{{ item.authorizationCode }}</span>
              <img
                src="@/assets/<EMAIL>"
                alt=""
                class="copy"
                @click="handleCopy(item.authorizationCode)"
              />
            </div>
          </div>
          <div class="operate" v-if="item.activeState == 1">
            <van-button class="unbind-device btn" @click="handleUnbind(item)"
              >解绑设备</van-button
            >
            <van-button class="renewal btn" @click="handleRenewal"
              >前往续费</van-button
            >
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import { Button, Toast, Dialog, List, PullRefresh } from "vant";
import {
  getTvWallAuthorizationCodeList,
  unbindTvWallAuthorizationCode
} from "../../api/enterNetwork";
export default {
  components: {
    [Button.name]: Button,
    [Dialog.name]: Dialog,
    [Toast.name]: Toast,
    [List.name]: List,
    [PullRefresh.name]: PullRefresh
  },
  props: {
    groupId: {
      type: [String, Number],
      default: ""
    }
  },
  data() {
    return {
      tvList: [],
      refreshing: false,
      loading: false,
      finished: false,
      total: 0,
      page: {
        no: 1,
        limit: 50
      }
    };
  },
  mounted() {
    // this.getTvWallAuthorizationCodeList();
  },
  methods: {
    async getTvWallAuthorizationCodeList() {
      const params = {
        groupId: this.groupId,
        ...this.page
      };
      if (this.refreshing) {
        this.tvList = [];
        this.refreshing = false;
      }
      this.loading = true;
      try {
        const res = await getTvWallAuthorizationCodeList(params);
        this.tvList = this.tvList.concat(res.records || []);
        this.total = res?.total || 0;
        this.loading = false;
        if (this.tvList.length >= this.total) {
          this.finished = true;
        } else {
          this.page.no++;
        }
      } catch (error) {
        this.loading = false;
        Toast.fail({
          duration: 2000,
          message: error.result
        });
      }
    },
    handleUnbind(row) {
      Dialog.confirm({
        title: "解绑确认",
        message: "解绑成功后可以到【电视墙注册】页面，注册新设备，确认解绑？",
        confirmButtonText: "解绑",
        messageAlign: "left",
        beforeClose: async (action, done) => {
          if (action === "confirm") {
            await unbindTvWallAuthorizationCode({
              groupId: this.groupId,
              id: row.id
            });
            Toast.success("解绑成功");
            this.tvList = [];
            this.getTvWallAuthorizationCodeList();
            done();
          } else {
            done();
          }
        }
      });
    },
    handleRenewal() {
      // 跳转到费用中心
      if (window.ovopark.browser.ovopark) {
        // 不需要跳转到中间页，直接跳费用中心
        if (window.ovopark.browser.android) {
          window.ovopark.action("openAppPage", {
            module: "CRM_EXPENSE_CENTER"
          });
        } else {
          window.location.href =
            "ioswdzforappstore://push/crm?type=ivan_view&page=ExpenseCenterTopViewController";
        }
      } else {
        // 跳转到中间页
        this.$router.push({
          name: "urgebill"
        });
      }
    },
    handleCopy(code) {
      this.$copyText(code).then(() => {
        Toast("复制成功");
      });
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.page.no = 1;
      this.getTvWallAuthorizationCodeList();
    }
  }
};
</script>

<style lang="scss" scoped>
.gl-box {
  padding: 12px 16px;
  .code-item {
    width: 343px;
    background: #ffffff;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 12px;
    box-sizing: border-box;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .contract {
        font-size: 15px;
        color: #1e232e;
        font-weight: 700;
      }
      .status {
        font-size: 14px;
        color: #898fa3;
        &.active {
          color: #1cbb61;
        }
      }
    }
    .line {
      width: 312px;
      height: 1px;
      background-color: #e4e6f0;
      margin: 12px 0;
    }
    .desc {
      font-size: 14px;
      color: #1e232e;
      line-height: 22px;
      margin-bottom: 12px;
      .label {
        margin-right: 12px;
      }
    }
    .auth-code {
      p {
        font-size: 14px;
        color: #898fa3;
        line-height: 22px;
        margin-bottom: 8px;
      }
      .auth-box {
        height: 36px;
        border: 1px solid #e4e6f0;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 8px;
        .value {
          color: #1e232e;
        }
        .copy {
          width: 16px;
          height: 16px;
        }
      }
    }
    .operate {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .btn {
        height: 36px;
        border: 1px solid #e4e6f0;
        border-radius: 18px;
        font-size: 12px;
        color: #4a4e69;
        &.renewal {
          background: #ff9900;
          color: #ffffff;
          margin-left: 12px;
        }
      }
    }
  }
}
</style>
