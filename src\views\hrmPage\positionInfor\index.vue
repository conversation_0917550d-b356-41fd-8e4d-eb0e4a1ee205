<template>
  <div class="information-page">
    <!--附件资料-->
    <div
      class="information"
      v-for="(item, index) in entryAttachments"
      :key="index"
    >
      <div class="information-title">
        <p class="must">{{ item.name }}</p>
        <!-- <van-icon name="plus" /> -->
        <upload-file
          v-model="item.attachments"
          :size="10485760"
          :max="5"
          :accept=" accept"
          :multiple="true"
          @on-upload-success="(val) => uploadSuccess(val, item)"
        ></upload-file>
      </div>
      <div class="prompt">{{ item.prompt }}</div>
      <!-- 教育内容 -->
      <ul>
        <li v-for="(ele, idx) in item.attachments" :key="idx">
          <div class="img-box" @click="viewImg(ele)">
            <img
              :src="
                ['jpg', 'jpeg', 'png', 'bmp'].includes(ele.suffix) ? 
                'https://ovopark.oss-cn-hangzhou.aliyuncs.com/front/2022/06/23/plO5l80N3aUi1Bh.png' : 
                ['pdf','PDF'].includes(ele.suffix) ?
                  'https://ovopark.oss-cn-hangzhou.aliyuncs.com/2023/06/29/zxfw_pdf.png': 
                  'https://ovopark.oss-cn-hangzhou.aliyuncs.com/front/2022/06/23/EEvfcEDNZfvk2Im.png'
              "
              alt=""
              />
            <span>{{ ele.filename }}</span>
          </div>
          <div class="ico-close" @click="deleteAttach(ele, index, idx)">
            <img src="../../../assets/hrm/ico_close.png" alt="" />
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import {
  Popup,
  NavBar,
  Loading,
  Button,
  Icon,
  Overlay,
  Toast,
  ImagePreview,
  Dialog,
} from "vant";
import uploadFile from "../../../components/upload/upload-file.vue";
import {
  saveOrUpdateAttachment,
  getAttachmentInfo,
  submitEntryForm,
} from "../../../api/hrm";
export default {
  components: {
    uploadFile,
    [NavBar.name]: NavBar,
    [Overlay.name]: Overlay,
    [Popup.name]: Popup,
    [Loading.name]: Loading,
    [Button.name]: Button,
    [Icon.name]: Icon,
    [Toast.name]: Toast,
    [Dialog.name]: Dialog,
    [ImagePreview.Component.name]: ImagePreview.Component,
  },
  data() {
    return {
      accept:
        "application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,image/jpeg,image/jpg,image/png,image/gif",
      // acceptImg: "image/jpeg,image/jpg,image/png,image/gif",
      entryAttachments: [
        {
          name: "简历",
          prompt: "只能上传 pdf/doc/jpg/jpeg/png 文件，至多上传5项，且不超过 10M",
          type: 1,
          attachments: [],
        },
        {
          name: "毕业证书",
          prompt: "只能上传 pdf/doc/jpg/jpeg/png 文件，至多上传5项，每项不超过 10M",
          type: 2,
          attachments: [],
        },
        {
          name: "学位证书",
          prompt: "只能上传 pdf/doc/jpg/jpeg/png 文件，至多上传5项，每项不超过 10M",
          type: 3,
          attachments: [],
        },
        {
          name: "体检报告",
          prompt: "只能上传 pdf/doc/jpg/jpeg/png 文件，至多上传5项，每项不超过 10M",
          type: 4,
          attachments: [],
        },
        {
          name: "离职证明",
          prompt: "只能上传 pdf/doc/jpg/jpeg/png 文件，至多上传5项，每项不超过 10M",
          type: 5,
          attachments: [],
        },
      ],
      // wordImg:
      //   "https://ovopark.oss-cn-hangzhou.aliyuncs.com/front/2022/06/23/plO5l80N3aUi1Bh.png",
      // pngImg:
      //   "https://ovopark.oss-cn-hangzhou.aliyuncs.com/front/2022/06/23/EEvfcEDNZfvk2Im.png",
    };
  },
  watch: {},
  mounted() {
    this.getAttachmentList();
  },
  methods: {
    //获取附件
    async getAttachmentList() {
      this.entryAttachments = [
        {
          name: "简历",
          prompt: "只能上传 pdf/doc/jpg/jpeg/png 文件，至多上传5项，且不超过 10M",
          type: 1,
          attachments: [],
        },
        {
          name: "毕业证书",
          prompt: "只能上传 pdf/doc/jpg/jpeg/png 文件，至多上传5项，每项不超过 10M",
          type: 2,
          attachments: [],
        },
        {
          name: "学位证书",
          prompt: "只能上传 pdf/doc/jpg/jpeg/png 文件，至多上传5项，每项不超过 10M",
          type: 3,
          attachments: [],
        },
        {
          name: "体检报告",
          prompt: "只能上传 pdf/doc/jpg/jpeg/png 文件，至多上传5项，每项不超过 10M",
          type: 4,
          attachments: [],
        },
        {
          name: "离职证明",
          prompt: "只能上传 pdf/doc/jpg/jpeg/png 文件，至多上传5项，每项不超过 10M",
          type: 5,
          attachments: [],
        },
      ];
      let res = await getAttachmentInfo({ id: this.$route.query.id });
      if (res.entryAttachments)
        this.entryAttachments.forEach((item) => {
          res.entryAttachments.forEach((el) => {
            el.attachments.forEach((ale) => {
              ale.name = ale.filename;
            })
            if (item.type == el.type) {
              item.attachments = el.attachments;
            }
          });
        });
      // console.log(this.entryAttachments, "获取附件资料");
    },

    // 上传
    uploadSuccess(val, item) {
      let obj = {
        filename: val.name,
        name: val.name,
        fileurl: val.fileurl,
        filetype: val.fileType,
        suffix: val.fileSuffix,
        id: "",
      };
      let idx = this.entryAttachments.findIndex((ele) => {
        return ele.type == item.type;
      });
      this.entryAttachments[idx].attachments.push(obj);
      this.entryAttachments[idx].attachments = this.entryAttachments[idx].attachments.filter((item) => {
        return  !item.client;
      });
    },

    // 删除
    deleteAttach(ele, index, idx) {
      this.entryAttachments[index].attachments.splice(idx, 1);
    },

    // 保存
    async saveOrUpdateAttachment(val) {
      //去除name字段
      let tempArr = JSON.parse(JSON.stringify(this.entryAttachments));
      tempArr.forEach((item) => {
        delete item.name;
      });

      // 必填校验
      let resume = tempArr.filter((item) => {
        return item.attachments.length == 0;
      });
      if (resume.length > 0) {
        return Toast.fail({
          duration: 2000,
          message: "请按要求上传入职资料！",
        });
      }
      let obj = {
        entryAttachments: tempArr,
        staffId: this.$route.query.id,
      };
      try {
        const res = await saveOrUpdateAttachment(obj);
        console.log(res);
        this.getAttachmentList(); //成功后调附件列表
        if(val) {
          this.submitEntryForm();
        }
      } catch (error) {
        console.log(error);
      }
    },

    //
    submit() {
      Dialog.confirm({
        title: "提交",
        message: "提交后不可修改,是否确认提交？",
      })
      .then(() => {
        this.saveOrUpdateAttachment(true);
      })
      .catch(() => {
        
      });
    },

    // 提交入职单
    async submitEntryForm() {
      try {
        const res = await submitEntryForm({ id: this.$route.query.id });
        console.log(res);
        Toast.fail({
          duration: 2000,
          message: "提交入职单成功！",
        });
        this.$router.push({
          name: "successPage",
          query: {
            id: this.$route.query.id,
          },
        });
      } catch (error) {
        console.log(error);
      }
    },

    // 预览图片
    viewImg(val) {
      if (val.suffix == "doc" || val.suffix == "pdf") {
        window.open(val.fileurl);
      } else {
        ImagePreview([val.fileurl]);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.information-page {
  height: 100%;
  padding: 0 16px;

  .information {
    width: 100%;
    min-height: 77px;
    border-bottom: 1px solid #ebedf0;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    padding-top: 16px;

    .information-title {
      display: flex;
      justify-content: space-between;

      p {
        // width: 64px;
        height: 22px;
        font-family: PingFangSC-Medium;
        font-weight: 550;
        font-size: 16px;
        color: #333333;
      }
      .must {
        &::before {
          content: "*";
          display: inline-block;
          margin-right: 4px;
          line-height: 1;
          font-family: SimSun;
          font-size: 14px;
          color: #ed4014;
        }
      }
    }
    .prompt {
      // width: 298px;
      margin-top: 8px;
      // height: 18px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 10px;
      color: #b2b2b2;
      line-height: 18px;
      margin-left: 11px;
    }

    // 内容
    ul {
      margin-top: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-bottom: 15px;

      li {
        // width: 343px;
        height: 32px;
        background: #f0f0f0;
        border-radius: 4px;
        margin-bottom: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .img-box {
          display: flex;
          justify-content: center;
          align-items: center;
          span {
            // width: 46px;
            // height: 17px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #333333;
          }
          img {
            width: 16px;
            height: 16px;
            margin: 0 12px;
          }
        }

        .ico-close {
          width: 14px;
          height: 14px;
          // background: #b2b2b2;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 8px;
          img {
            width: 8px;
            height: 8px;
          }
        }
      }

      li:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>