<template>
  <div class="refund-container">
    <van-nav-bar title="收款记录" left-arrow @click-left="onClickLeft" />
    <div class="refund-list">
      <van-sticky>
        <div class="sticky">
          <div class="date-box" @click="openTimePopup">
            <span class="date">{{
              $moment(currentMonth).format("YYYY年MM月")
            }}</span>
            <img src="../../assets/<EMAIL>" alt="" class="arrow" />
          </div>
          <div class="money">
            <div class="text">共收款{{ total || 0 }}笔</div>
            <!-- <p class="total">￥{{ name.split("-")[2] || 0 }}</p> -->
          </div>
        </div>
      </van-sticky>
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="getRefundList"
        >
          <div class="refund-box">
            <div class="record" v-for="item in refundList" :key="item.id">
              <div class="top">
                <div>
                  <span class="time-wrap"
                    >{{ $moment(item.payTime2).format("MM月DD日") }}
                    <span class="time">{{
                      $moment(item.payTime2).format("HH:mm")
                    }}</span></span
                  >
                </div>
                <van-button class="relate" @click="goRelated(item)"
                  >关联</van-button
                >
              </div>
              <div class="content">
                <div class="left">
                  <img src="../../assets/<EMAIL>" alt="" class="wechat" />
                  <div class="remark">{{ item.mobileRemark }}</div>
                </div>
                <div class="bonus">+{{ item.amount }}</div>
              </div>
              <div class="bottom">
                <div class="text">
                  已关联金额
                  <span class="nums">{{ item.alreadyRelatedAmount }}</span>
                </div>
                <div class="text">
                  待审批金额
                  <span class="nums">{{ item.pendingAmount }}</span>
                </div>
              </div>
              <div class="line"></div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <van-popup v-model="showTimePopup" position="bottom">
      <van-datetime-picker
        v-model="currentMonth"
        type="year-month"
        :formatter="formatter"
        @cancel="showTimePopup = false"
        @confirm="handleMonthChange"
      />
    </van-popup>
  </div>
</template>

<script>
import {
  NavBar,
  Button,
  List,
  PullRefresh,
  Toast,
  Sticky,
  DatetimePicker,
  Popup
} from "vant";
import { payMessageCenter } from "../../api/water";
export default {
  components: {
    [NavBar.name]: NavBar,
    [Button.name]: Button,
    [List.name]: List,
    [PullRefresh.name]: PullRefresh,
    [Toast.name]: Toast,
    [Sticky.name]: Sticky,
    [DatetimePicker.name]: DatetimePicker,
    [Popup.name]: Popup
  },
  data() {
    return {
      refreshing: false,
      finished: false,
      page: {
        no: 1,
        limit: 20
      },
      total: 0,
      loading: false,
      showTimePopup: false,
      currentMonth: new Date(),
      group: {},
      payStartTime: "",
      payEndTime: "",
      refundList: []
    };
  },
  methods: {
    onClickLeft() {
      this.backToApp();
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.page.no = 1;
      this.getRefundList();
    },
    async getRefundList() {
      if (this.refreshing) {
        this.refundList = [];
        this.refreshing = false;
      }
      const params = {
        isPayType: 0,
        ...this.page,
        payStatuses: "1",
        relatedStatus: "2,3",
        payStartTime: this.$moment()
          .startOf("month")
          .format("YYYY-MM-DD"),
        payEndTime: this.$moment()
          .endOf("month")
          .format("YYYY-MM-DD")
      };
      if (this.payStartTime && this.payEndTime) {
        params.payStartTime = this.payStartTime;
        params.payEndTime = this.payEndTime;
      }
      try {
        const res = await payMessageCenter(params);
        this.total = res.total || 0;
        this.refundList = this.refundList.concat(res.records || []);
        this.loading = false;
        if (this.refundList.length >= this.total) {
          this.finished = true;
        } else {
          this.page.no++;
        }
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.data || error.result
        });
      }
    },
    openTimePopup() {
      this.showTimePopup = true;
    },
    formatter(type, val) {
      if (type === "year") {
        return `${val}年`;
      } else if (type === "month") {
        return `${val}月`;
      }
      return val;
    },
    handleMonthChange(value) {
      this.currentMonth = value;
      this.payStartTime = this.$moment(value)
        .startOf("month")
        .format("YYYY-MM-DD");
      this.payEndTime = this.$moment(value)
        .endOf("month")
        .format("YYYY-MM-DD");
      this.refundList = [];
      this.page.no = 1;
      this.getRefundList();
      this.showTimePopup = false;
    },
    goRelated(item) {
      this.$router.push({
        name: "relatedWater",
        query: {
          id: item.id,
          customerId: item.customerId
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
