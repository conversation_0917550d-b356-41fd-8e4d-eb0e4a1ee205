import urls from "./config";
import { get } from "../request";

export function getSalesContractPage(params = {}) {
  return get(urls.getSalesContractPage, params);
}

export function getSalesContractTotal(params = {}) {
  return get(urls.getSalesContractTotal, params);
}

export function getSalesContractEchars(params = {}) {
  return get(urls.getSalesContractEchars, params);
}

export function getSalesContractTeam(params = {}) {
  return get(urls.getSalesContractTeam, params);
}

export function getPersonCountList(params = {}) {
  return get(urls.getPersonCountList, params);
}

export function getOrganizeCountList(params = {}) {
  return get(urls.getOrganizeCountList, params);
}

export function getYearCountListEchars(params = {}) {
  return get(urls.getYearCountListEchars, params);
}

export function getPaymentDetailForH5(params = {}) {
  return get(urls.getPaymentDetailForH5, params);
}