export default class EventEmitter {
  /**
   *构造函数
   */
  constructor() {
    this.events = {};
  }

  /**
   *获取事件对象
   */
  getEvents() {
    return this.events || (this.events = {});
  }

  /**
   *获取监听器
   */
  getListeners(event) {
    let events = this.getEvents();
    return events[event] || (events[event] = []);
  }

  /**
   *监听事件
   */
  on(event, handler, context) {
    let listeners = this.getListeners(event);

    let listener = {
      handler: handler,
      context: context
    };

    listeners.push(listener);
  }

  /**
   *移除事件
   */
  off(event, handler) {
    let events = this.getEvents();

    if (handler === undefined) {
      events[event] = [];
      return;
    }

    let listeners = this.getListeners(event);
    let size = listeners.length;

    for (let i = 0; i < size; i++) {
      if (listeners[i].handler === handler) {
        listeners.splice(i, 1);
        return;
      }
    }
  }

  /**
   *触发事件
   */
  emit(event, ...args) {
    let listeners = this.getListeners(event);
    let size = listeners.length;

    for (let i = 0; i < size; i++) {
      listeners[i].handler.call(listeners[i].context || this, ...args);
    }
  }
}
