<template>
  <div class="business-detail">
    <!-- 头部导航栏 -->
    <div class="nav-bar">
      <img src="../../assets/ico_back_r.png" alt="" @click="onClickLeft" />
      <div class="nav-bar-title">谍报</div>
      <div class="nav-bar-right" @click="handlecluingDetail">线索</div>
    </div>

    <!-- 内容区域 -->
    <div class="detail-content">
      <!-- 日期和标签 -->
      <div class="detail-info">
        <span class="detail-time">{{ data.createTime }}</span>
        <span class="detail-divider">|</span>
        <span class="detail-tag">{{ data.title }}</span>
      </div>

      <!-- 文章内容 -->
      <div class="detail-body">
        <p class="detail-paragraph">
          {{ data.text }}
        </p>
      </div>
    </div>

    <!-- 内容来源 -->
    <div class="detail-source">
      <div class="source-label">
        <img src="../../assets/nry.png" alt="" />
        内容源
      </div>
      <div class="source-scroll-container">
        <div
          class="source-item"
          v-for="(item, index) in sourceList"
          :key="index"
          @click="handleSourceDetail(item)"
        >
          <div class="source-item-content">{{ item.title }}</div>

          <div class="source-text">
            <img :src="item.logo ? item.logo : defaultIcon" />
            {{ item.source }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  updateReadStatus,
  getInformationDetail
} from "../../api/businessOpportunity";
export default {
  name: "BusinessDetail",
  data() {
    return {
      sourceList: [],
      data: {},
      defaultIcon: require("../../assets/nry2.png")
    };
  },
  mounted() {
    this.getInformationDetail();
    this.updateReadStatus();
  },
  methods: {
    onClickLeft() {
      if (this.$route.query.type === "news") {
        this.$router.back();
      } else {
        this.backToApp();
      }
    },

    async getInformationDetail() {
      try {
        const res = await getInformationDetail(this.$route.query.id);
        this.data = res;
        const textReferences = res.textReferences.split(";");
        const textSource = res.textSource.split(";");
        const textTitle = res.textTitle.split(";");
        const textLogo = res.textLogo.split(";");
        // 将四个数组组合成对象数组
        this.sourceList = textReferences.map((url, index) => ({
          url: url, // 跳转链接
          source: textSource[index], // 来源网站
          title: textTitle[index], // 标题
          logo: textLogo[index] // logo图片
        }));
      } catch (error) {
        console.error("获取资讯详情失败:", error);
      }
    },

    // 更新阅读状态
    async updateReadStatus() {
      try {
        const res = await updateReadStatus(this.$route.query.id);
        console.log("更新阅读状态成功:", res);
      } catch (error) {
        console.error("更新阅读状态失败:", error);
      }
    },

    // 判断当前平台
    getPlatform() {
      const u = navigator.userAgent;
      const isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1;
      const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
      return isiOS ? "ios" : isAndroid ? "android" : "other";
    },

    // 跳转到源内容详情
    handlecluingDetail() {
      let cluingId = this.$route.query.cluingId;
      const platform = this.getPlatform();
      if (platform === "ios") {
        window.location.href = `ioswdzforappstore://push/crm?type=ivan_view&page=CrmClueDetailViewController&itemId=${cluingId}`;
      } else if (platform === "android") {
        window.ovopark.action("openAppPage", {
          module: "CRM_CLUE_DETAIL",
          callback: cluingId,
          clueId: cluingId
        });
      }
    },

    // 跳转到源内容详情
    handleSourceDetail(item) {
      const platform = this.getPlatform();
      if (platform === "ios") {
        let url = encodeURIComponent(item.url);
        window.location.href = `ioswdzforappstore://push/web?type=ivan_view&page=WDZBusinessWebViewController&urlSting=${url}`;
      } else if (platform === "android") {
        window.ovopark.action("openAppPage", {
          module: "CRM_SPY_CONTENT_SOURCE",
          url: item.url
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.business-detail {
  background-color: #fff;
  height: 100%;
  display: flex;
  padding: 0 16px;
  flex-direction: column;
  overflow: hidden;

  // 导航栏样式
  .nav-bar {
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;

    img {
      width: 20px;
      height: 20px;
    }

    .nav-bar-title {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #000000;
    }

    .nav-bar-right {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #ff9900;
      text-align: center;
      line-height: 24px;
    }
  }

  // 内容区域
  .detail-content {
    flex: 1;
    padding: 16px 0;
    overflow: auto;

    .detail-title {
      font-size: 22px;
      font-weight: bold;
      line-height: 1.4;
      color: #000;
      margin-bottom: 8px;
    }

    .detail-info {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      font-size: 12px;
      color: #999;

      .detail-divider {
        margin: 0 8px;
      }

      .detail-tag {
        color: #666;
      }
    }

    .detail-body {
      margin-bottom: 24px;

      .detail-paragraph {
        font-size: 14px;
        line-height: 1.6;
        color: #333;
        margin-bottom: 16px;
      }
    }
  }

  .detail-source {
    margin-bottom: 16px;

    .source-label {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #999;
      margin-bottom: 10px;

      img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }

    .source-scroll-container {
      display: flex;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch; /* 提升iOS滚动体验 */
      gap: 12px; /* 各项之间的间距 */
      padding-bottom: 5px; /* 为滚动条留出空间 */

      &::-webkit-scrollbar {
        display: none; /* 隐藏滚动条 */
      }
    }

    .source-item {
      box-sizing: border-box;
      min-width: 282px; /* 确保卡片有最小宽度 */
      height: 102px;
      padding: 12px 12px;
      background: #fff9f3;
      border-radius: 8px;
      flex-shrink: 0; /* 防止项目被压缩 */
      border: 1px solid #ffeccb;
      border-radius: 8px;

      .source-item-content {
        width: 258px;
        height: 48px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #000000;
        text-align: justify;
        line-height: 24px;
        // 换行，超过两行显示省略号
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 8px;
      }

      .source-text {
        height: 22px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #7f7f7f;
        text-align: justify;
        line-height: 22px;
        display: flex;
        align-items: center;

        img {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
