import urls from "./config";
import {
    post,
    get,
    postJson,
    putReq,
    deleteReq
} from "../request";



//确认入职
export function confirmEntry(params) {
    return postJson(urls.confirmEntry + "?id=" + params);
}

//新增教育经历
export function saveEducation(params) {
    return postJson(urls.saveEducation, params);
}

//职务管理列表
export function saveDuties(params) {
    return postJson(urls.saveDuties, params);
}


//添加岗位接口
export function savePost(params) {
    return postJson(urls.savePost, params);
}


//新增员工合同
export function saveStaffContract(params) {
    return postJson(urls.saveStaffContract, params);
}



export function saveStaffRelationship(params) {
    return postJson(urls.saveStaffRelationship, params);
}

export function saveStaffBankAccount(params) {
    return postJson(urls.saveStaffBankAccount, params);
}

export function saveStaff(params) {
    return postJson(urls.saveStaff, params);
}

export function updateStaff(params) {
    return postJson(urls.updateStaff, params);
}

export function saveOrUpdateAttachment(params) {
    return postJson(urls.saveOrUpdateAttachment, params);
}


export function sendToNew(params) {
    return postJson(urls.sendToNew + "?id=" + params);
}


export function saveStaffWorkHistory(params) {
    return postJson(urls.saveStaffWorkHistory, params);
}

//编辑职务
export function updateDuties(params) {
    return putReq(urls.updateDuties, params);
}

//编辑岗位
export function updatePost(params) {
    return putReq(urls.updatePost, params);
}

//跟新员工合同
export function updateStaffContract(params) {
    return putReq(urls.updateStaffContract, params);
}

//更新教育经历
export function updateEducation(params) {
    return putReq(urls.updateEducation, params);
}

export function updateStaffBankAccount(params) {
    return putReq(urls.updateStaffBankAccount, params);
}

export function updateStaffRelationship(params) {
    return putReq(urls.updateStaffRelationship, params);
}

export function updateStaffWorkHistory(params) {
    return putReq(urls.updateStaffWorkHistory, params);
}

//岗位管理列表
export function getPostPage(params) {
    return get(urls.getPostPage, params);
}

//职务管理列表
export function getDutiesPage(params) {
    return get(urls.getDutiesPage, params);
}

//查询员工合同列表
export function getStaffContractList(params) {
    return get(urls.getStaffContractList, params);
}

//查询教育经历
export function getEducation(params) {
    return get(urls.getEducation, params);
}

//获取教育经历详情
export function getEducationList(params) {
    return get(urls.getEducationList, params);
}

// 查看员工列表
export function getStaffList(params) {
    return get(urls.getStaffList, params);
}

//数据字典
export function getAllDict(params) {
    return get(urls.getAllDict, params);
}



export function getAttachmentInfo(params) {
    return get(urls.getAttachmentInfo, params);
}

//查询联系人
export function getStaffRelationshipList(params) {
    return get(urls.getStaffRelationshipList, params);
}

//查询银行
export function getStaffBankAccountList(params) {
    return get(urls.getStaffBankAccountList, params);
}

//获取经历
export function getStaffWorkHistoryList(params) {
    return get(urls.getStaffWorkHistoryList, params);
}



export function getStaffContractDetail(params) {
    return get(urls.getStaffContractDetail + "/" + params);
}


export function getStaffAccountDetail(params) {
    return get(urls.getStaffAccountDetail + "/" + params);
}


//删除员工合同
export function deleteStaffContract(params) {
    return deleteReq(urls.deleteStaffContract + "/" + params);
}

//删除职务
export function deleteDuties(params) {
    return deleteReq(urls.deleteDuties, params);
}

//删除岗位
export function deletePost(params) {
    return deleteReq(urls.deletePost, params);
}


//删除联系人
export function deleteStaffRelationship(params) {
    return deleteReq(urls.deleteStaffRelationship + "/" + params);
}

//删除银行
export function deleteStaffBankAccount(params) {
    return deleteReq(urls.deleteStaffBankAccount + "/" + params);
}

//删除教育经历
export function deleteEducation(params) {
    return deleteReq(urls.deleteEducation, params);
}

//删除工作经历
export function deleteStaffWorkHistory(params) {
    return deleteReq(urls.deleteStaffWorkHistory + "/" + params);
}

// 统计员工数量
export function getStaffCount(params) {
    return get(urls.getStaffCount, params);
}

// 查看员工详情
export function getStaffInfo(params) {
    return get(urls.getStaffInfo, params);
}

// OCR获取文字信息
export function getOcrInfo(params) {
    return get(urls.getOcrInfo, params);
}

//获取结构化地址
export function getStructAddr(params) {
    return get(urls.getStructAddr, params);
}

//提交入职单
export function submitEntryForm(params = {}) {
    return post(urls.submitEntryForm, params);
}

//查询组织架构 
export function getSysDepartmentList(params) {
    return get(urls.getSysDepartmentList, params);
}

//保存组织架构
export function addSysDepartment(params) {
    return postJson(urls.addSysDepartment, params);
}
  
//更新组织架构
export function updateSysDepartment(params) {
  return putReq(urls.updateSysDepartment, params);
}

//删除组织架构
export function deleteSysDepartment(params) {
  return deleteReq(urls.deleteSysDepartment+ "/" + params);
}

// 查询组织架构下的员工
export function getCustomerFromDept(params = {}) {
  return get(urls.getCustomerFromDept, params);
}

// 绑定组织架构与员工关系
export function addCustomerFromDept(params = {}) {
  return postJson(urls.addCustomerFromDept, params);
}

// 删除组织架构与员工关系
export function deleteCustomerFromDept(params) {
    return deleteReq(urls.deleteCustomerFromDept, params);
}

// 查询组织架构以及组织架构下员工
export function getSysDepartmentAndStaff(params = {}) {
    return get(urls.getSysDepartmentAndStaff, params);
}

// 根据员工名称模糊查询
export function searchCustomerInPublicStaffs(params = {}) {
    return get(urls.searchCustomerInPublicStaffs, params);
}

// 根据工号获取表单列表
export function getMemberRecord(params = {}) {
    return get(urls.getMemberRecord, params);
}

// 导出员工档案
export function exportStaffInfo(params = {}) {
    return get(urls.exportStaffInfo, params);
}

// 判断是否提交
export function getSubmitState(params = {}) {
    return get(urls.getSubmitState, params);
}

/**
 * 获取所有地区情况
 */
 export function getAllAreaInfo (params = {}) {
    return get(urls.getAllAreaInfo, params);
  }

// 发送入职单短信验证码
export function sendEntrySms(params) {
    return get(urls.sendEntrySms, params);
}

// 验证入职单短信验证码
export function verifyEntrySms(params) {
    return get(urls.verifyEntrySms, params);
}

export function getAllNewDict(params) {
    return get(urls.getAllNewDict, params);
}