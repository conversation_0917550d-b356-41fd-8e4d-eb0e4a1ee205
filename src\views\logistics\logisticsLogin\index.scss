.realNameAuth {
  height: 100%;
  background: #fff;
  padding: 0 28px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .title {
    height: 28px;
    font-family: PingFangSC-Medium;
    font-weight: 550;
    font-size: 20px;
    color: #000000;
    text-align: center;
    margin-top: 108px;
  }

  main {
    margin-top: 48px;
    box-sizing: border-box;
    flex: 1;

    .input-box {
      width: 319px;
      height: 56px;
      margin-bottom: 20px;
      background-color: #f7f7f7;
      border-radius: 28px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .input {
        flex: 1;
        min-width: 0;
        border: 0; /*清除自带的2px的边框*/
        padding: 0; /*清除自带的padding间距*/
        outline: none; /*清除input点击之后的黑色边框*/
        height: 56px;
        background-color: #f7f7f7;
        font-size: 16px;
        color: #000000;
        border-radius: 28px;
        padding-left: 20px;
      }

      .delete {
        width: 16px;
        height: 16px;
        margin-right: 20px;
      }

      .authCode {
        width: 102px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #ff9900;
        letter-spacing: 0;
        line-height: 24px;
        margin-right: 20px;
        text-align: right;
      }

      .authCode-and {
        width: 102px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #b2b2b2;
        letter-spacing: 0;
        line-height: 24px;
        margin-right: 20px;
        text-align: right;
      }
    }

    input::-webkit-input-placeholder {
      /* WebKit browsers，webkit内核浏览器 */
      color: #a1a1a1;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #b2b2b2;
      letter-spacing: 0;
    }
    input:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #a1a1a1;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #b2b2b2;
      letter-spacing: 0;
      line-height: 24px;
    }
    input::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #a1a1a1;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #b2b2b2;
      letter-spacing: 0;
      line-height: 24px;
    }
    input:-ms-input-placeholder {
      /* Internet Explorer 10+ */
      color: #a1a1a1;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #b2b2b2;
      letter-spacing: 0;
      line-height: 24px;
    }

    .van-button {
      width: 319px;
      height: 44px;
      line-height: 44px;
      margin-top: 40px;
      background: #ff9900;
      border-radius: 22px;
      color: #ffffff;
    }

    .agree {
      margin-top: 16px;
      display: flex;

      .agree-txt {
        margin-left: 8px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #7f7f7f;
        letter-spacing: 0;
      }
      .agree-color {
        color: #ff9900;
      }
    }
  }

  footer {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    margin-bottom: 50px;
    p {
      width: 72px;
      height: 16px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #7f7f7f;
      text-align: center;
      line-height: 16px;
      margin-bottom: 16px;
    }
    .wx {
      width: 44px;
      height: 44px;
    }
  }
}

.title-name {
  height: 56px;
  font-family: PingFangSC-SNaNpxibold;
  font-weight: 600;
  font-size: 17px;
  color: #000000;
  text-align: center;
  position: relative;

  img {
    width: 16px;
    height: 16px;
    position: absolute;
    right: 16px;
  }
}

.dialog-box {
  height: 222px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .dialog-title {
    margin-top: 8px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: center;
  }
  .dialog-input-box {
    width: 279px;
    height: 48px;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 12px;

    .dialog-input {
      flex: 1;
      height: 100%;
      min-width: 0;
      border: 0; /*清除自带的2px的边框*/
      padding: 0; /*清除自带的padding间距*/
      outline: none; /*清除input点击之后的黑色边框*/
      font-size: 16px;
      color: #000000;
    }

    .dialog-authCode {
      width: 102px;
      font-size: 14px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      color: #ff9900;
      letter-spacing: 0;
      text-align: right;
      padding-right: 12px;
    }

    .dialog-authCode-and {
      width: 102px;
      font-size: 14px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      color: #b2b2b2;
      letter-spacing: 0;
      text-align: right;
      padding-right: 12px;
    }
  }

  .dialog-btn {
    width: 116px;
    height: 44px;
    background: #ff9900;
    border-radius: 22px;
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
    margin-top: 24px;
  }

  input::-webkit-input-placeholder {
    /* WebKit browsers，webkit内核浏览器 */
    color: #a1a1a1;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #b2b2b2;
    letter-spacing: 0;
  }

  input:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #a1a1a1;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #b2b2b2;
    letter-spacing: 0;
    line-height: 24px;
  }

  input::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #a1a1a1;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #b2b2b2;
    letter-spacing: 0;
    line-height: 24px;
  }

  input:-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: #a1a1a1;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #b2b2b2;
    letter-spacing: 0;
    line-height: 24px;
  }
}

.whyLogin {
  font-size: 14px;
  text-align: center;
  margin-top: 50px;
  font-family: PingFangSC-Regular;
  color: #02a7f0;
  cursor: pointer;
}
