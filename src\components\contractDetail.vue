<template>
  <div class="detail">
    <van-nav-bar
      left-arrow
      @click-left="onClickLeft"
      v-show="showHome"
      title="合同/订单详情"
    ></van-nav-bar>
    <div class="body">
      <div class="basic-wrap">
        <div class="contract-status">
          {{
            params.status &&
            contractList.find(item => item.value === params.status)
              ? contractList.find(item => item.value === params.status).dname
              : "--"
          }}
        </div>
        <div class="contract-title">{{ params.contractName }}</div>
        <div class="bill-no" v-show="params.contractType === '3'">
          账单编号：{{ params.billNo }}
        </div>
        <div class="line"></div>
        <div class="basic-info">
          <p class="w-1">
            <span class="label">品牌名称：</span>
            <span class="content">{{ params.straightClient || "--" }}</span>
          </p>
          <p class="w-1">
            <span class="label">甲方代表：</span>
            <span class="content">{{ params.signUser || "--" }}</span>
          </p>
          <p class="w-1">
            <span class="label">甲方名称：</span>
            <span class="content">{{ params.customerName || "--" }}</span>
          </p>
          <p class="w-1">
            <span class="label">门店名称：</span>
            <span class="content">{{ params.customerRemark || "--" }}</span>
          </p>
          <p class="w-1">
            <span class="label">负责人：</span>
            <span class="content">{{ params.userName || "--" }}</span>
          </p>
          <p class="w-1">
            <span class="label">创建时间：</span>
            <span class="content">{{ params.createTime || "--" }}</span>
          </p>
          <p class="w-1">
            <span class="label">支付状态：</span>
            <span class="content">{{ paymentStatus }}</span>
          </p>
        </div>
      </div>

      <div class="platform-fee">
        <div class="platform-title">设备费用</div>
        <div v-if="!hardwareList" class="w-content">无</div>
        <div
          v-else
          class="platlist"
          v-for="(ele, index) in hardwareList"
          :key="index"
        >
          <p class="plat total">
            <span class="label">{{ ele.productName || "--" }}</span>
            <span class="content">
              合计金额：<span class="nums"
                ><span class="sepera">¥</span
                >{{ ele.discountPrice || "--" }}</span
              >
            </span>
          </p>
          <p class="plat">
            <span class="label">类型：</span>
            <span class="content">{{
              ele.firstType &&
              allTypeList &&
              allTypeList.find(item => item.id === ele.firstType)
                ? allTypeList.find(item => item.id === ele.firstType).typeName
                : "--"
            }}</span>
          </p>
          <!-- 这个单价实际为折后单价 -->
          <p class="plat">
            <span class="label">单价：</span>
            <span class="content">{{ ele.salePrice || "--" }}</span>
          </p>
          <p class="plat">
            <span class="label">单位：</span>
            <span class="content">{{
              ele.unit &&
              unitList &&
              unitList.find(item => item.value == ele.unit)
                ? unitList.find(item => item.value == ele.unit).dname
                : "--"
            }}</span>
          </p>
          <p class="plat">
            <span class="label">数量：</span>
            <span class="content">{{ ele.nums || "--" }}</span>
          </p>
          <p class="plat">
            <span class="label">合计金额：</span>
            <span class="content">¥{{ ele.discountPrice || "--" }}</span>
          </p>

          <!-- 分割线 -->
          <div
            class="line"
            v-if="hardwareList.length > 1 && index != hardwareList.length - 1"
          ></div>
        </div>
      </div>

      <!-- 服务明细 -->
      <!-- <p class="w-2 w-3">
        <span class="line"></span>
        <span class="title">服务明细</span>
      </p> -->
      <div class="platform-fee">
        <div class="platform-title">服务明细</div>
        <div v-if="!serviceList" class="w-content">无</div>
        <div
          v-else
          class="platlist"
          v-for="(item, i) in serviceList"
          :key="i + 'serviceList'"
        >
          <p class="plat total">
            <span class="label">{{ item.productName || "--" }}</span>
            <span class="content">
              合计金额：<span class="nums"
                ><span class="sepera">¥</span
                >{{ item.discountPrice || "--" }}</span
              >
            </span>
          </p>
          <p class="plat">
            <span class="label">类型：</span>
            <span class="content">{{
              item.firstType &&
              allTypeList &&
              allTypeList.find(ele => ele.id === item.firstType)
                ? allTypeList.find(ele => ele.id === item.firstType).typeName
                : "--"
            }}</span>
          </p>
          <!-- 这个单价实际为折后单价 -->
          <p class="plat">
            <span class="label">单价：</span>
            <span class="content">{{ item.salePrice || "--" }}</span>
          </p>
          <p class="plat">
            <span class="label">单位：</span>
            <span class="content">{{
              item.unit &&
              unitList &&
              unitList.find(ele => ele.value == item.unit)
                ? unitList.find(ele => ele.value == item.unit).dname
                : "--"
            }}</span>
          </p>
          <p class="plat">
            <span class="label">数量：</span>
            <span class="content">{{ item.nums || "--" }}</span>
          </p>
          <p class="plat">
            <span class="label">年：</span>
            <span class="content">{{ item.years || "--" }}</span>
          </p>
          <!-- 分割线 -->
          <div
            class="line"
            v-if="serviceList.length > 1 && i != serviceList.length - 1"
          ></div>
        </div>
      </div>

      <!-- 施工费用 -->
      <div class="platform-fee">
        <div class="platform-title">施工费用</div>
        <div v-if="!constructionList" class="w-content">无</div>
        <div
          class="platlist"
          v-for="(ale, index) in constructionList"
          :key="index + 'constructionList'"
          v-else
        >
          <p class="plat total">
            <span class="label">{{ ale.productName || "--" }}</span>
            <span class="content">
              合计金额：<span class="nums"
                ><span class="sepera">¥</span
                >{{ ale.discountPrice || "--" }}</span
              >
            </span>
          </p>
          <p class="plat">
            <span class="label">类型：</span>
            <span class="content">{{
              ale.firstType &&
              allTypeList &&
              allTypeList.find(ele => ele.id === ale.firstType)
                ? allTypeList.find(ele => ele.id === ale.firstType).typeName
                : "--"
            }}</span>
          </p>
          <!-- 这个单价实际为折后单价 -->
          <p class="plat">
            <span class="label">单价：</span>
            <span class="content">{{ ale.salePrice || "--" }}</span>
          </p>
          <p class="plat">
            <span class="label">单位：</span>
            <span class="content">{{
              ale.unit &&
              unitList &&
              unitList.find(ele => ele.value == ale.unit)
                ? unitList.find(ele => ele.value == ale.unit).dname
                : "--"
            }}</span>
          </p>
          <p class="plat">
            <span class="label">数量：</span>
            <span class="content">{{ ale.nums || "--" }}</span>
          </p>

          <!-- 分割线 -->
          <div
            class="line"
            v-if="
              constructionList.length > 1 &&
                index != constructionList.length - 1
            "
          ></div>
        </div>
      </div>

      <!-- 签约主体 -->
      <div class="sign-contain">
        <div class="title">签约主体</div>
        <div class="first-party">
          <p class="w-1">
            <span class="label">甲方(签章)：</span>
            <span class="content">{{
              customerCompanyInfo.companyName || "--"
            }}</span>
          </p>
          <p class="w-1">
            <span class="label">代表签字：</span>
            <span class="content">{{
              customerCompanyInfo.signUser || "--"
            }}</span>
          </p>
          <p class="w-1">
            <span class="label">开户银行：</span>
            <span class="content">{{
              customerCompanyInfo.customerOpenBank || "--"
            }}</span>
          </p>
          <p class="w-1">
            <span class="label">账号：</span>
            <span class="content">{{
              customerCompanyInfo.customerOpenAccount || "--"
            }}</span>
          </p>
          <p class="w-1">
            <span class="label">纳税人识别号：</span>
            <span class="content">{{
              customerCompanyInfo.customerTaxpayerAccount || "--"
            }}</span>
          </p>
          <p class="w-1">
            <span class="label">地址：</span>
            <span class="content">{{
              customerCompanyInfo.companyAddress || "--"
            }}</span>
          </p>
          <p class="w-1">
            <span class="label">电话：</span>
            <span class="content">{{
              customerCompanyInfo.customerBankPhone || "--"
            }}</span>
          </p>
        </div>
        <div class="second-party">
          <p class="w-1">
            <span class="label">乙方(签章)：</span>
            <span class="content">{{ companyInfo.companyName || "--" }}</span>
          </p>
          <p class="w-1">
            <span class="label">代表签字：</span>
            <span class="content">{{ companyInfo.signUser || "--" }}</span>
          </p>
          <p class="w-1">
            <span class="label">开户银行：</span>
            <span class="content">{{
              companyInfo.customerOpenBank || "--"
            }}</span>
          </p>
          <p class="w-1">
            <span class="label">账号：</span>
            <span class="content">{{
              companyInfo.customerOpenAccount || "--"
            }}</span>
          </p>
          <p class="w-1">
            <span class="label">纳税人识别号：</span>
            <span class="content">{{
              companyInfo.customerTaxpayerAccount || "--"
            }}</span>
          </p>
          <p class="w-1">
            <span class="label">地址：</span>
            <span class="content">{{
              companyInfo.companyAddress || "--"
            }}</span>
          </p>
          <p class="w-1">
            <span class="label">电话：</span>
            <span class="content">{{ companyInfo.companyPhone || "--" }}</span>
          </p>
        </div>
      </div>
    </div>
    <div class="footer">
      <div class="footer-box">
        <div class="amount">¥{{ params.price || "--" }}</div>
      </div>
      <div class="btn-box">
        <van-button
          @click="onCancelOrder"
          type="default"
          class="cancel-btn"
          round
          v-if="
            params.contractType == '3' &&
              params.paymentAmount == 0 &&
              isFromBill
          "
          >取消订单</van-button
        >
        <van-button
          v-if="
            ['1', '2'].includes(params.paymentStatus) && params.paymentMode != 2
          "
          color="#FF9900"
          type="primary"
          round
          @click="onPayClick"
          >付款</van-button
        >
        <!-- <van-button v-if="params.paymentStatus == '3'" color="#FF9900" disabled
          >已付款</van-button
        > -->
       <!-- <van-button
          v-if="params.paymentStatus == '3'"
          color="#FF9900"
          type="primary"
          round
          style="margin-right: 8px;"
          @click="gotoInvoiceManage"
          :disabled="params.price === params.alreadyBillingAmount"
          >去开票</van-button
        >> -->
        <van-button
          v-if="params.paymentMode == 2"
          @click="showPaymentDialog = true"
          color="#FF9900"
          type="primary"
          round
          :disabled="params.isPayment == false"
          >对公转账</van-button
        >
      </div>
    </div>

    <!-- 直接登录弹窗 -->
    <dialogLogin :showDialog="showLogin" :sourcePage="'contractPage'" />

    <!-- 认证弹窗 -->
    <dialogAttest
      :showDialog="showDialog"
      :customerBankId="params.customerBankId"
      :sourcePage="'contractPage'"
    />
    <van-popup
      v-model="showPaymentDialog"
      position="bottom"
      round
      class="payment-popup"
    >
      <div class="popup-header">
        <div></div>
        <div class="title">对公转账</div>
        <van-icon name="cross" @click="showPaymentDialog = false" />
      </div>

      <div class="payment-container">
        <div class="item">
          <label>户名</label>
          <span class="desc">苏州万店掌网络科技有限公司</span>
        </div>
        <div class="item">
          <label>账号</label>
          <span class="desc">32250198863600000746</span>
        </div>
        <div class="item">
          <label>开户行</label>
          <span class="desc specail"
            >中国建设银行苏州市高新技术产业开发区支行</span
          >
        </div>
        <div class="item">
          <label>合同号</label>
          <span class="desc">{{ params.contractNo }}</span>
        </div>
        <div class="item">
          <label>金额</label>
          <span class="desc">{{ params.price }}</span>
        </div>

        <!-- 新增单选切换 -->
        <div class="radio-group">
          <van-radio-group
            v-model="verifyType"
            direction="horizontal"
            @change="handleVerifyTypeChange"
          >
            <van-radio name="upload" checked-color="#FF9900"
              >上传银行回单</van-radio
            >
            <van-radio name="input" checked-color="#FF9900"
              >输入转账信息查询</van-radio
            >
          </van-radio-group>
        </div>

        <!-- 上传回单模式 -->
        <template v-if="verifyType === 'upload'">
          <div class="upload">
            <label class="must">付款凭证</label>
            <div class="upload-box">
              <upload-file
                :max="1"
                :accept="accept"
                isNormal
                v-model="attachmentList"
                @on-upload-success="handleUploadSuccess"
                @del="handleDeleteFile"
              ></upload-file>
            </div>
          </div>
          <van-button
            v-if="attachmentList.length > 0"
            block
            class="query-btn"
            :disabled="!ocrResult.account"
            @click="verifyVoucher"
            >查询</van-button
          >

          <!-- 添加查询结果展示区域 -->
          <div v-if="queryResult" class="query-result">
            <div class="title">查询结果</div>
            <div class="result-item">
              <span class="label">对方账号：</span>
              <span class="value">{{ queryResult.bankAccount || "--" }}</span>
            </div>
            <div class="result-item">
              <span class="label">汇款流水号：</span>
              <span class="value">{{ queryResult.seqNo || "--" }}</span>
            </div>
            <div class="result-item">
              <span class="label">可被关联最大金额：</span>
              <span class="value">{{ queryResult.amount || "--" }}</span>
            </div>
          </div>
        </template>

        <!-- 手动输入模式 -->
        <template v-else>
          <div class="input-form">
            <van-field
              v-model="formData.account"
              label="汇款银行流水账号"
              placeholder="汇款银行流水账号"
              required
              input-align="right"
            />
            <van-field
              v-model="formData.date"
              label="转账日期"
              placeholder="转账日期"
              readonly
              required
              @click="showDatePicker = true"
              input-align="right"
            />
            <van-field
              v-model="formData.amount"
              label="汇款金额"
              type="number"
              required
              placeholder="汇款金额"
              input-align="right"
            />
          </div>
          <van-button
            block
            class="query-btn"
            :disabled="!formData.account || !formData.date || !formData.amount"
            @click="verifyInput"
            >查询</van-button
          >

          <!-- 添加查询结果展示区域 -->
          <div v-if="queryResult" class="query-result">
            <div class="title">查询结果</div>
            <div class="result-item">
              <span class="label">对方账号：</span>
              <span class="value">{{ queryResult.bankAccount || "--" }}</span>
            </div>
            <div class="result-item">
              <span class="label">汇款流水号：</span>
              <span class="value">{{ queryResult.seqNo || "--" }}</span>
            </div>
            <div class="result-item">
              <span class="label">可被关联最大金额：</span>
              <span class="value">{{ queryResult.amount || "--" }}</span>
            </div>
          </div>
        </template>
      </div>

      <div class="action-wrap">
        <van-button class="copy" @click="handleCancel">复制账户</van-button>
        <!-- <van-button class="sure" @click="handleConfirm"
          >确认&完成付款</van-button
        > -->
        <van-button
          class="sure"
          :disabled="!queryResult"
          @click="relatedContract"
          >确认&关联回款到当前订单</van-button
        >
      </div>
    </van-popup>

    <van-calendar
      v-model="showDatePicker"
      color="#FF9900"
      :min-date="minDate"
      :max-date="maxDate"
      @confirm="onDateConfirm"
    />
  </div>
</template>

<script>
import {
  NavBar,
  Field,
  Loading,
  Icon,
  GoodsAction,
  GoodsActionButton,
  Button,
  Toast,
  Dialog,
  Popup,
  RadioGroup,
  Radio,
  Calendar
} from "vant";

import {
  getContractDetailForH5,
  getAllDict,
  queryProductTypesToTree,
  saveBillVoucher,
  cancelOrder,
  getTicketOCR,
  getPaySerialNewRecord,
  relatedContract
} from "../api/delivery";

import { getUserRealName } from "../api/realNameAuth";

import { findDeeply } from "../common/utils";
import { paymentStatusList } from "../common/dictionary";
import dialogAttest from "../components/dialogAttest.vue";
import dialogLogin from "../components/dialogLogin.vue";
import uploadFile from "./upload-file/upload-file.vue";

export default {
  components: {
    [NavBar.name]: NavBar,
    [Field.name]: Field,
    [Loading.name]: Loading,
    [Icon.name]: Icon,
    [GoodsActionButton.name]: GoodsActionButton,
    [GoodsAction.name]: GoodsAction,
    [Button.name]: Button,
    [Toast.name]: Toast,
    [Dialog.name]: Dialog,
    [Dialog.Component.name]: Dialog.Component,
    [Popup.name]: Popup,
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio,
    [Calendar.name]: Calendar,
    dialogAttest,
    dialogLogin,
    uploadFile
  },
  data() {
    return {
      params: {}, //合同信息
      customerCompanyInfo: {}, //甲方信息
      companyInfo: {}, //公司信息
      hardwareList: {}, //设备费用
      serviceList: {}, //服务费用信息
      constructionList: {}, //施工信息
      contractList: [], //合同状态
      unitList: [], //单位
      allTypeList: [], //产品类别树
      paymentStatus: "", //回款状态
      showHome: false,
      showDialog: false,
      showLogin: true,
      authObj: {}, //实名认证信息
      showPaymentDialog: false,
      attachmentList: [],
      accept: "image/jpeg,image/jpg,image/png,image/gif",
      isFromBill: false,
      verifyType: "upload",
      formData: {
        account: "",
        date: "",
        amount: ""
      },
      showDatePicker: false,
      queryResult: null,
      ocrResult: {},
      minDate: new Date(2015, 0, 1),
      maxDate: new Date(2030, 12, 31)
    };
  },
  created() {
    // this.getUserRealName();
    if (this.$route.query?.isLogin == "1") {
      this.showLogin = false;
    }
    if (this.$route.query?.billId) {
      this.isFromBill = true;
    }
    this.getAllDict();
    this.queryProductTypesToTree();
    sessionStorage.setItem("pay", false);
  },
  mounted() {
    var ua = window.navigator.userAgent.toLowerCase();
    this.getContractDetailForH5();
    if (ua.match(/MicroMessenger/i) == "micromessenger") {
      this.showHome = false;
    } else if (ua.match(/AlipayClient/i) == "alipayclient") {
      this.showHome = false;
    } else {
      this.showHome = true;
    }
  },
  watch: {
    params(val) {
      this.paymentStatus = paymentStatusList[val.paymentStatus - 1];
    }
  },
  methods: {
    // 回退到app页面
    onClickLeft() {
      this.backToApp();
    },
    onPayClick() {
      this.$router.push({
        name: "payCost",
        query: {
          contractId: this.$route.query.contractId,
          token: this.$route.query.token,
          type: 1, //1为合同详情 2 为账单详情
          openId: this.authObj?.openId,
          mobilePhone: this.authObj?.mobilePhone,
          accountName: this.authObj?.accountName,
          isOpenOrder: this.$route.query?.isOpenOrder,
          specialType: this.$route.query?.specialType
        }
      });
    },

    // 查询当前用户是否已经实名认证通过（仅登录查询）
    async getUserRealName() {
      try {
        const res = await getUserRealName();
        if (
          (res?.length == 0 ||
            !res ||
            res[0]?.cardStatus == 0 ||
            res[0]?.cardStatus == 2) &&
          this.params?.customerBankId &&
          this.$route.query?.isLogin == "1"
        ) {
          this.showDialog = true;
        } else {
          this.authObj = res[0];
        }
      } catch (error) {
        console.log(error);
      }
    },

    /**获取合同详情 */
    async getContractDetailForH5() {
      try {
        let id = this.$route.query.contractId || "";
        const res = await getContractDetailForH5(id);
        this.params = res;
        this.customerCompanyInfo = res.customerCompanyInfo;
        this.companyInfo = res.companyInfo;
        this.hardwareList = res.hardwareList;
        this.serviceList = res.serviceList;
        this.constructionList = res.constructionList;
        this.getUserRealName();
      } catch (error) {
        console.log(error);
      }
    },
    /**
     * 全部字典项
     */
    async getAllDict() {
      const res = await getAllDict();
      sessionStorage.setItem("dictTree", JSON.stringify(res));
      if (res) {
        this.getOption();
      }
    },

    /**
     * 字典项
     */
    getOption() {
      let dictTree = JSON.parse(sessionStorage.getItem("dictTree"));
      const contractList = findDeeply(
        dictTree,
        item => item.type === "contract_status"
      );
      const unitList = findDeeply(dictTree, item => item.type === "unit");
      this.contractList = [...contractList.children] || [];
      this.unitList = [...unitList.children] || [];
    },

    /**
     * 产品类别
     */
    async queryProductTypesToTree() {
      const res = await queryProductTypesToTree();
      const arr = [];
      res.forEach(item => {
        item.children && arr.push(...item.children);
      });
      this.allTypeList = arr;
    },
    gotoInvoiceManage() {
      if (this.params.status === "2") {
        Toast.fail({
          duration: 2000,
          message: "合同审核中，审核通过后方可开票"
        });
        return;
      }
      this.$router.push({
        name: "applyBill",
        query: {
          contractId: this.$route.query.contractId,
          token: this.$route.query.token,
          contactNos: this.params.contractNo
        }
      });
    },
    async handleConfirm() {
      if (!this.queryResult) {
        Toast.fail("请先查询转账信息");
        return;
      }

      try {
        Toast.loading({
          message: "提交中...",
          forbidClick: true
        });

        const params = {
          id: this.$route.query.billId,
          paymentVoucher:
            this.attachmentList.length > 0
              ? this.attachmentList[0].fileUrl
              : "",
          paymentMode: 2,
          // 添加查询结果相关信息
          bankAccount: this.queryResult.bankAccount,
          seqNo: this.queryResult.seqNo,
          amount: this.queryResult.amount
        };

        await saveBillVoucher(params);

        Toast.clear();
        Toast.success("提交成功");
        this.showPaymentDialog = false;

        // 刷新页面数据
        this.getContractDetailForH5();
      } catch (error) {
        Toast.clear();
        Toast.fail(error.message || "提交失败");
      }
    },
    handleCancel() {
      const copyText = `户\u00A0\u00A0\u00A0名\u00A0\u00A0\u00A0\u00A0\u00A0苏州万店掌网络科技有限公司\n\n账\u00A0\u00A0\u00A0号\u00A0\u00A0\u00A0\u00A0\u00A032250198863600000746 \n\n开户行\u00A0\u00A0\u00A0\u00A0\u00A0中国建设银行苏州市高新技术产业
      \u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0开发区支行营业部\n\n合同号\u00A0\u00A0\u00A0\u00A0\u00A0${this.params.contractNo}\n\n金\u00A0\u00A0\u00A0额\u00A0\u00A0\u00A0\u00A0\u00A0${this.params.price}`;
      this.$copyText(copyText).then(
        () => {
          Toast.success("复制成功");
        },
        () => {
          Toast.fail("复制失败");
        }
      );
    },
    handleSuccess(result) {
      this.attachmentList = [
        {
          fileUrl: result.fileurl,
          fileurl: result.fileurl,
          progress: 100
        }
      ];
      Dialog({
        title: "上传成功",
        message: "对公转账凭证上传成功，请等待万店掌确认审核时间:工作日"
      });
    },
    onCancelOrder() {
      Dialog.confirm({
        message:
          "取消订单可以切换支付方式，如果您已经转账请勿操作，等待万店掌确认回款。确认继续？",
        beforeClose: async (action, done) => {
          const params = {
            billId: this.$route.query.billId,
            contractId: this.$route.query.contractId
          };
          if (action === "confirm") {
            try {
              await cancelOrder(params);
              Toast.success("取消订单成功");
              setTimeout(() => {
                this.goBack();
              }, 1000);
              done();
            } catch (error) {
              Toast.fail({
                duration: 3000,
                message: error.data || error.result
              });
              done(false);
            }
          } else {
            done();
          }
        }
      });
    },
    goBack() {
      // 跳转到费用中心
      if (window.ovopark.browser.android) {
        window.ovopark.action("openAppPage", { module: "CRM_EXPENSE_CENTER" });
      } else {
        window.location.href =
          "ioswdzforappstore://push/crm?type=ivan_view&page=ExpenseCenterTopViewController";
      }
    },
    // handleUploadSuccess(result) {
    // this.attachmentList = [
    //   {
    //     fileUrl: result.fileurl,
    //     fileurl: result.fileurl,
    //     progress: 100
    //   }
    // ];
    // Dialog({
    //   title: "上传成功",
    //   message: "对公转账凭证上传成功，请等待万店掌确认审核时间:工作日"
    // });
    // },

    // 对公转账凭证ocr
    async handleUploadSuccess(result) {
      this.attachmentList = [
        {
          fileUrl: result.fileurl,
          fileurl: result.fileurl,
          progress: 100
        }
      ];

      try {
        Toast.loading({
          message: "识别中...",
          forbidClick: true
        });

        const res = await getTicketOCR({
          url: result.fileurl
        });

        Toast.clear();

        if (res && res.account) {
          this.ocrResult = res;
          Toast.success("凭证识别成功");
        } else {
          Toast.fail("凭证识别失败，请上传清晰的付款凭证");
        }
      } catch (error) {
        Toast.clear();
        Toast.fail("凭证识别失败");
        console.log(error);
      }
    },

    async verifyVoucher() {
      if (!this.ocrResult.account) {
        Toast.fail("请先上传有效的付款凭证");
        return;
      }

      try {
        Toast.loading({
          message: "查询中...",
          forbidClick: true
        });

        const res = await getPaySerialNewRecord({
          bankAccount: this.ocrResult.account,
          amount: this.ocrResult.amount,
          payDate: this.ocrResult.date
        });

        Toast.clear();

        if (res) {
          this.queryResult = res;
        } else {
          Toast.fail("未查询到相关记录");
        }
      } catch (error) {
        Toast.clear();
        Toast.fail(error.result || "查询失败");
        console.log(error);
      }
    },
    async verifyInput() {
      if (
        !this.formData.account ||
        !this.formData.date ||
        !this.formData.amount
      ) {
        Toast.fail("请填写完整信息");
        return;
      }

      try {
        Toast.loading({
          message: "查询中...",
          forbidClick: true
        });

        const res = await getPaySerialNewRecord({
          bankAccount: this.formData.account,
          amount: this.formData.amount,
          payDate: this.formData.date
        });

        Toast.clear();

        if (res) {
          this.queryResult = res;
        } else {
          Toast.fail("未查询到相关记录");
        }
      } catch (error) {
        Toast.clear();
        Toast.fail(error.result || "查询失败");
      }
    },
    onDateConfirm(date) {
      // 格式化日期为 YYYY-MM-DD
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      this.formData.date = `${year}-${month}-${day}`;
      this.showDatePicker = false;
    },
    handleVerifyTypeChange() {
      // 清空查询结果
      this.queryResult = null;

      // 根据切换的类型清空相应数据
      if (this.verifyType === "upload") {
        // 切换到上传模式，清空手动输入的数据
        this.formData = {
          account: "",
          date: "",
          amount: ""
        };
      } else {
        // 切换到手动输入模式，清空上传的数据
        this.attachmentList = [];
        this.ocrResult = {};
      }
    },
    handleDeleteFile() {
      // 清空OCR结果
      this.ocrResult = {};
      // 清空查询结果
      this.queryResult = null;
    },

    // 回款关联合同
    async relatedContract() {
      if (!this.queryResult) {
        Toast.fail("请先查询转账结果");
        return;
      }

      try {
        Toast.loading({
          message: "提交中...",
          forbidClick: true
        });

        // 计算各项金额
        const hardwareTotal =
          this.hardwareList && this.hardwareList.length
            ? this.hardwareList.reduce(
                (total, item) => total + Number(item.discountPrice || 0),
                0
              )
            : 0;

        const softwareTotal =
          this.serviceList && this.serviceList.length
            ? this.serviceList.reduce(
                (total, item) => total + Number(item.discountPrice || 0),
                0
              )
            : 0;

        const constructionTotal =
          this.constructionList && this.constructionList.length
            ? this.constructionList.reduce(
                (total, item) => total + Number(item.discountPrice || 0),
                0
              )
            : 0;

        // 构建请求参数
        const paymentDetailList = [
          {
            hardwareMoney: hardwareTotal,
            softwareMoney: softwareTotal,
            constructionMoney: constructionTotal,
            contractNo: this.params.contractNo || "",
            payTime:
              this.verifyType === "upload"
                ? this.ocrResult.date
                : this.formData.date,
            serialId: this.queryResult.id,
            receiptUrl:
              this.attachmentList.length > 0
                ? this.attachmentList[0].fileUrl
                : ""
          }
        ];

        await relatedContract({ paymentDetailList });

        Toast.clear();
        Toast.success("关联成功");
        this.showPaymentDialog = false;
        // 延迟2.5秒后查询详情，检查支付状态
        setTimeout(() => {
          this.getContractDetailForH5();
        }, 2500);
      } catch (error) {
        Toast.clear();
        Toast.fail(error.result || "关联失败，请重试");
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.detail {
  height: 100%;
  width: 100%;
  background-color: rgb(247, 247, 247);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
/deep/.van-nav-bar__content {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  height: 44px;
}
/deep/.van-nav-bar__title {
  height: 24px;
  font-family: PingFangSC-Semibold;
  font-weight: bold;
  font-size: 17px;
  color: #333333;
  text-align: center;
}
/deep/ .van-nav-bar .van-icon {
  color: #333;
  font-size: 18px;
}
.body {
  box-sizing: border-box;
  padding: 12px 16px;
  overflow-x: hidden;
  overflow-y: auto;
  flex: 1;
  .basic-wrap {
    background-color: #fff;
    border-radius: 6px;
    padding: 12px 16px;
  }
  .contract-status {
    font-size: 14px;
    color: #1cbb61;
    margin-bottom: 8px;
  }
  .contract-title {
    font-size: 18px;
    color: #000000;
    font-weight: bold;
  }
  .bill-no {
    font-size: 14px;
    color: #7f7f7f;
    line-height: 22px;
  }
  .line {
    height: 1px;
    background-color: #e5e5e5;
    margin-top: 12px;
  }
  .basic-info {
    background-image: linear-gradient(180deg, #fafafa 0%, #ffffff 100%);
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    margin-top: 12px;
    padding: 12px;
  }
  .platform-fee {
    border-radius: 6px;
    padding: 12px 16px;
    background-color: #fff;
    margin-top: 12px;
    .platform-title {
      font-size: 16px;
      color: #000000;
      line-height: 24px;
      font-weight: bold;
      margin-bottom: 12px;
    }
    .w-content {
      font-size: 14px;
      color: #333333;
      line-height: 22px;
    }
    .platlist {
      .line {
        height: 1px;
        background-color: #e5e5e5;
        margin-top: 12px;
      }
      .plat {
        font-size: 12px;
        color: #7f7f7f;
        line-height: 20px;
        margin-bottom: 4px;
        .content {
          color: #7f7f7f;
        }
        &.total {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          .label {
            font-size: 14px;
            color: #333333;
            line-height: 22px;
            font-weight: bold;
          }
          .content {
            font-size: 12px;
            color: #333333;
            line-height: 20px;
            .nums {
              font-size: 18px;
              color: #ff9900;
            }
          }
        }
      }
    }
  }
  .sign-contain {
    border-radius: 6px;
    padding: 12px 16px;
    margin-top: 12px;
    background-color: #fff;
    .title {
      font-size: 16px;
      color: #000000;
      line-height: 24px;
      font-weight: bold;
    }
    .first-party,
    .second-party {
      padding: 12px;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      margin-top: 12px;
    }
  }
}

h5 {
  padding: 5px;
}
.w-1 {
  display: flex;
  justify-content: space-between;
  line-height: 20px;
  margin-bottom: 8px;
  &:last-child {
    margin-bottom: 0;
  }
}
.w-2 {
  height: 21px;
  margin-bottom: 2px;
  .line {
    display: inline-block;
    width: 3px;
    height: 17px;
    background: #ff9900;
    vertical-align: text-top;
    margin-right: 7px;
  }
  .title {
    width: 60px;
    height: 21px;
    font-family: PingFangSC-Medium;
    font-weight: bold;
    font-size: 15px;
    color: #333333;
    letter-spacing: 0;
  }
}

.w-3 {
  margin-top: 16px;
}
span {
  font-size: 13px;
}

.label {
  font-size: 12px;
  color: #7f7f7f;
  line-height: 20px;
}

.content {
  font-size: 12px;
  color: #333333;
  line-height: 20px;
  text-align: right;
  max-width: 200px;
}

.footer {
  display: flex;
  justify-content: space-between;
  height: 52px;
  align-items: center;
  background: #ffffff;
  box-sizing: border-box;
  box-shadow: 0 -4px 8px 0 rgba(0, 0, 0, 0.06);
  margin-top: 10px;
  padding: 0 16px;

  .footer-box {
    line-height: 52px;
    box-sizing: border-box;
    display: flex;

    .amount {
      font-size: 24px;
      color: #333333;
      font-weight: bold;
    }
  }
  .btn-box {
    display: flex;
    .cancel-btn {
      width: 88px;
      height: 38px;
      background: #f7f7f7;
      border-radius: 6px;
      margin-right: 8px;
    }
    .van-button--primary {
      width: 88px;
      height: 38px;
      background: #ff9900;
      border-radius: 6px;
    }
  }
}
.fenge {
  width: 100%;
  height: 6px;
  background: #f3f3f3;
}
.payment-popup {
  min-height: 90vh;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;

  .popup-header {
    display: flex;
    height: 56px;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;

    .title {
      font-size: 16px;
      font-weight: bold;
    }
  }

  .radio-group {
    margin: 16px 0;

    // 调整单选按钮组的样式
    ::v-deep .van-radio-group {
      display: flex;
      gap: 46px;
      .van-radio {
        margin-right: 0;
      }
    }
  }

  .input-form {
    margin: 16px 0;
  }

  .verify-btn {
    margin: 16px 0;
    background: #ff9900;
    border: none;

    &.van-button--primary {
      background: #ff9900;
    }
  }

  .payment-container {
    flex: 1;
    padding: 16px;
    .item {
      font-size: 14px;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      line-height: 26px;
      label {
        display: inline-block;
        color: #7f7f7f;
        width: 50px;
        text-align: left;
      }
      .desc {
        text-align: right;
        color: #000000;
        &.specail {
          width: 182px;
        }
      }
    }
    .upload {
      display: flex;
      margin-bottom: 24px;
      .must {
        font-size: 14px;
        &::before {
          content: "*";
          display: inline-block;
          margin-right: 4px;
          font-size: 14px;
          color: #ed4014;
          font-family: SimSun;
          line-height: 1;
          font-weight: 500;
        }
      }
      .upload-box {
        width: 78px;
        height: 78px;
        background: #f7f7f7;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 12px;
      }
    }
  }

  .query-btn {
    border: 2px dashed #ff9900;
    border-radius: 4px;
    color: #ff9900;
    font-size: 14px;
  }

  .query-result {
    margin-top: 16px;
    height: 130px;
    background: #f7f7f7;
    border-radius: 6px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .title {
      width: 56px;
      height: 22px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      letter-spacing: 0;
      line-height: 22px;
    }

    .result-item {
      height: 20px;
      color: #595959;
      display: flex;
      justify-content: space-between;
    }
  }

  .action-wrap {
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    gap: 12px;
    border-top: 1px solid #e5e5e5;
    .copy {
      width: 123px;
      height: 40px;
      background: rgba(255, 153, 0, 0.06);
      border-radius: 6px;
      font-size: 16px;
      color: #ff9900;
      padding: 0;
      margin-right: 16px;
    }
    .sure {
      flex: 1;
      height: 40px;
      background: #ff9900;
      border-radius: 6px;
      font-size: 16px;
      color: #ffffff;
      padding: 0;

      &.van-button--disabled {
        opacity: 0.5;
        background: #ff9900;
        color: #ffffff;
      }
    }
  }
}

::v-deep .van-radio__icon {
  font-size: 16px;
}
</style>
