.contract-repayment {
  background-color: #f5f7f9;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .header {
    flex: 0 0 auto;

    ::v-deep .van-nav-bar .van-icon {
      color: #333;
      font-size: 18px;
    }
  }

  .content-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 20px;
  }

  :deep(.van-nav-bar) {
    background-color: #fff;

    .van-nav-bar__arrow {
      color: #333333;
      font-size: 24px;
    }
  }

  .section-title {
    padding: 0 16px;
    height: 48px;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    font-size: 16px;
    color: #000000;
    line-height: 48px;
  }

  .personal-data {
    background: #fff;
    margin-bottom: 12px;
  }

  .filter-tabs {
    padding: 0 16px 16px;

    .tab-wrapper {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    .tab-item {
      display: flex;
      align-items: center;
      padding: 5px 15px;
      font-size: 14px;
      color: #323233;
      background: #f7f8fa;
      border-radius: 20px;
      cursor: pointer;
      border: 1px solid transparent;

      &.active {
        color: #ff9900;
        border: 1px solid #ff9900;
        background: rgba(255, 153, 0, 0.05);
      }

      .van-icon {
        margin-left: 5px;
      }
    }
  }

  /* --- 表格样式 (已优化) --- */
  .table-container {
    background: #fff;
    margin-bottom: 12px;
    // 启用水平滚动
    overflow-x: auto;
    // 提升移动端滚动体验
    -webkit-overflow-scrolling: touch;

    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }
    scrollbar-width: none; // for Firefox

    .optimized-table {
      // 设置一个最小宽度以确保表格在内容不足时也能触发滚动
      min-width: 800px;

      .optimized-table-header {
        // 固定表头，使其在垂直滚动时保持在顶部
        position: sticky;
        top: 0;
        z-index: 2;
      }

      .optimized-table-row {
        display: flex;
        width: 100%;
        border-bottom: 1px solid #ebedf0;
        &:last-child {
          border-bottom: none;
        }
      }

      // --- 背景色与字体样式 ---
      .optimized-table-header .optimized-table-row {
        background: #f9f9f9;
        font-weight: 500;
        color: #323233;
        font-size: 13px;
      }

      .optimized-table-body .optimized-table-row {
        font-size: 14px;
        color: #646566;
        background: #fff;
        cursor: pointer;
        transition: background-color 0.2s;
        &:active {
          background-color: #f7f8fa;
        }
      }

      .optimized-table-footer .optimized-table-row {
        background: #f9f9f9;
        font-weight: 600;
        font-size: 14px;
        color: #323233;
      }

      // --- 单元格通用样式 ---
      .optimized-table-cell {
        display: flex;
        align-items: center;
        padding: 0 12px;
        height: 44px;
        flex-basis: 120px; // 默认列宽
        flex-grow: 1;
        box-sizing: border-box;

        .cell-content {
          display: flex;
          align-items: center;
          width: 100%;
          // 默认居中对齐，方便覆盖
          justify-content: center;
        }

        // 数值列内容居右
        &.cell-amount .cell-content {
          justify-content: flex-end;
        }
        // 表头中的数值列标题居中
        .optimized-table-header & .cell-content {
          justify-content: center;
        }

        // 名称列
        &.cell-name {
          flex-basis: 150px;
          flex-shrink: 0;
          .cell-content {
            justify-content: flex-start;
          }
        }

        // --- 固定操作列 ---
        &.cell-action {
          flex-basis: 60px; // 固定宽度
          flex-grow: 0;
          flex-shrink: 0;

          // 实现固定列的关键
          position: sticky;
          right: 0;

          // 设置背景色以防滚动时下方内容透出
          background-color: inherit;

          // 添加阴影以示区分
          box-shadow: -4px 0 6px -4px rgba(0, 0, 0, 0.08);
          z-index: 1;
        }
      }

      // 确保固定列的背景色在不同状态下都正确
      .optimized-table-header .cell-action {
        background-color: #f9f9f9;
      }
      .optimized-table-body .optimized-table-row .cell-action {
        background-color: #fff;
      }
      .optimized-table-body .optimized-table-row:active .cell-action {
        background-color: #f7f8fa;
      }
      .optimized-table-footer .cell-action {
        background-color: #f9f9f9;
      }

      // 排序图标样式
      .sort-icons {
        display: flex;
        flex-direction: column;
        margin-left: 4px;
        .sort-icon {
          font-size: 12px;
          color: #c8c9cc;
          line-height: 8px;
          height: 8px;
          &.active {
            color: #ff9900;
          }
        }
      }
    }
  }

  .chart-section {
    .chart-container {
      margin-bottom: 12px;
      background: #fff;
      padding: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;

      .title-area {
        .chart-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          margin-bottom: 12px;
        }

        .unit-text {
          font-size: 12px;
          color: #999;
        }
      }

      .legend-area {
        display: flex;
        align-items: center;

        .legend-item {
          display: flex;
          align-items: center;
          margin-left: 16px;

          &:first-child {
            margin-left: 0;
          }

          .legend-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 4px;

            &.contract-dot {
              background-color: #ff9900;
            }

            &.repayment-dot {
              background-color: #4ecb73;
            }

            &.prev-year-dot {
              background-color: #ff9900;
            }

            &.current-year-dot {
              background-color: #4ecb73;
            }
          }

          span {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }

    .chart {
      height: 300px;
      margin-bottom: 0;
    }
  }

  // 弹窗样式
  .date-popup {
    padding: 0;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;

    .popup-header {
      position: relative;
      padding: 20px 0;
    }

    .date-popup-title {
      text-align: center;
      font-size: 16px;
      font-weight: 500;
    }

    .close-icon {
      position: absolute;
      top: 20px;
      right: 16px;
      font-size: 16px;
      color: #323233;
    }

    .date-type-tabs {
      display: flex;
      flex-wrap: wrap;
      padding: 0 16px;
      // flex: 1;

      .date-type-item {
        width: calc(25% - 12px);
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 6px 12px;
        border-radius: 4px;
        font-size: 14px;
        background: #f2f3f5;
        color: #323233;

        &.active {
          background-color: #ff9900;
          color: #fff;
        }
      }
    }
  }

  .custom-date-popup {
    padding: 0;
    position: relative;
    display: flex;
    flex-direction: column;
    background: #fff;
    overflow-x: hidden; // 禁止横向滚动

    .popup-header {
      position: relative;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #ebedf0;
    }

    .date-popup-title {
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: #000;
    }

    .close-icon {
      position: absolute;
      top: 50%;
      right: 16px;
      transform: translateY(-50%);
      font-size: 18px;
      color: #323233;
      padding: 8px;
      margin-right: -8px;
    }

    .date-input-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      margin-bottom: 0;
      background: #fff;

      .date-input {
        flex: 1;
        max-width: 155px;
        height: 36px;

        .custom-date-input {
          width: 100%;
          height: 36px;
          padding: 0;
          border: 1px solid #dcdcdc;
          border-radius: 4px;
          font-size: 14px;
          color: #333;
          background: #fff;
          text-align: center;
          outline: none;
          box-sizing: border-box;

          &.active {
            border-color: #ff9900;
          }

          &.selected {
            color: #ff9900;
          }

          &.disabled {
            opacity: 0.6;
            pointer-events: none;
            background-color: #f5f5f5;
            border: 1px solid #dcdcdc;
            color: #999;
          }

          &::placeholder {
            color: #999;
          }
        }
      }

      .date-separator {
        padding: 0 8px;
        color: #969799;
        flex: 0 0 auto;
      }
    }

    .date-picker-container {
      display: flex;
      justify-content: space-between;
      height: 250px;
      margin: 0;
      border-top: 1px solid #f2f3f5;
      overflow-x: hidden; // 禁止横向滚动

      .date-picker-column {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden; // 禁止横向滚动
        -webkit-overflow-scrolling: touch;
        text-align: center;

        &::-webkit-scrollbar {
          display: none;
        }

        &:not(:last-child) {
          border-right: 1px solid #f2f3f5;
        }

        .date-picker-item {
          height: 44px;
          line-height: 44px;
          color: #999;
          font-size: 14px;
          padding: 0 4px;
          margin: 0 auto;
          width: 80%;

          &.active {
            color: #333;
            font-weight: 500;
            position: relative;

            &:after {
              content: "";
              position: absolute;
              left: 50%;
              bottom: 7px;
              width: 4px;
              height: 4px;
              background-color: #ff9900;
              border-radius: 50%;
              transform: translateX(-50%);
            }
          }
        }
      }
    }

    .date-confirm-btn {
      margin: 16px;

      .van-button {
        height: 44px;
        background: #ff9900;
        border-color: #ff9900;
        font-size: 16px;
        border-radius: 4px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        letter-spacing: 2px;

        &:active {
          background: #e68a00;
          border-color: #e68a00;
        }
      }
    }
  }

  // 日期选择器样式调整
  :deep(.van-picker) {
    flex: 1;
  }

  :deep(.van-picker-column__item) {
    font-size: 17px;

    &--selected {
      color: #323233;
      font-weight: 500;
    }
  }

  :deep(.van-picker__mask) {
    background-image: linear-gradient(
        180deg,
        hsla(0, 0%, 100%, 0.9),
        hsla(0, 0%, 100%, 0.4)
      ),
      linear-gradient(0deg, hsla(0, 0%, 100%, 0.9), hsla(0, 0%, 100%, 0.4));
  }

  // 弹出层样式优化
  :deep(.van-popup--bottom) {
    border-radius: 16px 16px 0 0;

    &.van-popup--round {
      overflow: hidden;
    }
  }
}
