<template>
  <div class="contract-repayment">
    <div class="header">
      <van-nav-bar
        :title="
          currentView === 'organization'
            ? '合同/回款额'
            : selectedDepName + ' - 人员数据'
        "
        :left-arrow="true"
        @click-left="handleNavLeftClick"
      />
    </div>

    <div class="content-container">
      <div class="personal-data">
        <div class="section-title">区域数据</div>
        <div class="filter-tabs">
          <div class="tab-wrapper">
            <div
              :class="['tab-item', { active: currentTab === 'time' }]"
              @click="handleTabClick('time')"
            >
              <span>{{ getDateTypeLabel() }}</span>
              <van-icon name="arrow-down" size="11" />
            </div>
            <div
              :class="['tab-item', { active: currentTab === 'custom' }]"
              @click="handleTabClick('custom')"
            >
              <span>{{ customDateRangeText }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="table-view-wrapper">
        <transition name="slide-view" mode="out-in">
          <div v-if="currentView === 'organization'" key="organization">
            <div class="table-container">
              <template v-if="tableData && tableData.length > 0">
                <div class="optimized-table">
                  <div class="optimized-table-header">
                    <div class="optimized-table-row">
                      <div
                        :class="[
                          'optimized-table-cell',
                          column.field === 'depName'
                            ? 'cell-name'
                            : 'cell-amount'
                        ]"
                        v-for="column in columnFields"
                        :key="column.field"
                        @click="
                          column.field !== 'depName'
                            ? handleSort(column.field)
                            : null
                        "
                      >
                        <div class="cell-content">
                          <span class="cell-title">{{ column.label }}</span>
                          <div
                            class="sort-icons"
                            v-if="column.field !== 'depName'"
                          >
                            <van-icon
                              name="arrow-up"
                              :class="[
                                'sort-icon',
                                {
                                  active:
                                    currentSort === column.field &&
                                    sortOrder === 'asc'
                                }
                              ]"
                            />
                            <van-icon
                              name="arrow-down"
                              :class="[
                                'sort-icon',
                                {
                                  active:
                                    currentSort === column.field &&
                                    sortOrder === 'desc'
                                }
                              ]"
                            />
                          </div>
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-action">
                        <div class="cell-content">操作</div>
                      </div>
                    </div>
                  </div>
                  <div class="optimized-table-body">
                    <div
                      class="optimized-table-row"
                      v-for="(item, index) in tableData"
                      :key="index"
                    >
                      <div class="optimized-table-cell cell-name">
                        <div class="cell-content">{{ item.depName }}</div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content">
                          {{ item.contractAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content">
                          {{ item.hardwareAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content">
                          {{ item.softwareAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content">
                          {{ item.constructionAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content">
                          {{ item.repaymentAmount }}
                        </div>
                      </div>
                      <div
                        class="optimized-table-cell cell-action"
                        @click="showPersonnelView(item)"
                      >
                        <div class="cell-content">
                          <van-icon name="arrow" size="16" color="#999" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="optimized-table-footer">
                    <div class="optimized-table-row">
                      <div class="optimized-table-cell cell-name">
                        <div class="cell-content summary-label">汇总</div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content summary-value">
                          {{ totalData.contractAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content summary-value">
                          {{ totalData.hardwareAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content summary-value">
                          {{ totalData.softwareAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content summary-value">
                          {{ totalData.constructionAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content summary-value">
                          {{ totalData.repaymentAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-action">
                        <div class="cell-content"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="empty-state-container">
                  <van-empty description="暂无数据" />
                </div>
              </template>
            </div>
          </div>

          <div v-else key="personnel">
            <div class="table-container">
              <template v-if="personnelData && personnelData.length > 0">
                <div class="optimized-table">
                  <div class="optimized-table-header">
                    <div class="optimized-table-row">
                      <div
                        :class="[
                          'optimized-table-cell',
                          column.field === 'depName'
                            ? 'cell-name'
                            : 'cell-amount'
                        ]"
                        v-for="column in columnFields"
                        :key="column.field"
                        @click="
                          column.field !== 'depName'
                            ? handlePersonnelSort(column.field)
                            : null
                        "
                      >
                        <div class="cell-content">
                          <span class="cell-title">{{ column.label }}</span>
                          <div
                            class="sort-icons"
                            v-if="column.field !== 'depName'"
                          >
                            <van-icon
                              name="arrow-up"
                              :class="[
                                'sort-icon',
                                {
                                  active:
                                    personnelCurrentSort === column.field &&
                                    personnelSortOrder === 'asc'
                                }
                              ]"
                            />
                            <van-icon
                              name="arrow-down"
                              :class="[
                                'sort-icon',
                                {
                                  active:
                                    personnelCurrentSort === column.field &&
                                    personnelSortOrder === 'desc'
                                }
                              ]"
                            />
                          </div>
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-action"></div>
                    </div>
                  </div>
                  <div class="optimized-table-body">
                    <div
                      class="optimized-table-row"
                      v-for="(item, index) in personnelData"
                      :key="index"
                    >
                      <div class="optimized-table-cell cell-name">
                        <div class="cell-content">{{ item.userName }}</div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content">
                          {{ item.contractAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content">
                          {{ item.hardwareAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content">
                          {{ item.softwareAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content">
                          {{ item.constructionAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content">
                          {{ item.repaymentAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-action"></div>
                    </div>
                  </div>
                  <div class="optimized-table-footer">
                    <div class="optimized-table-row">
                      <div class="optimized-table-cell cell-name">
                        <div class="cell-content summary-label">汇总</div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content summary-value">
                          {{ personnelTotalData.contractAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content summary-value">
                          {{ personnelTotalData.hardwareAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content summary-value">
                          {{ personnelTotalData.softwareAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content summary-value">
                          {{ personnelTotalData.constructionAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-amount">
                        <div class="cell-content summary-value">
                          {{ personnelTotalData.repaymentAmount }}
                        </div>
                      </div>
                      <div class="optimized-table-cell cell-action">
                        <div class="cell-content"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="empty-state-container">
                  <van-empty description="暂无人员数据" />
                </div>
              </template>
            </div>
          </div>
        </transition>
      </div>

      <div class="chart-section">
        <div class="chart-container">
          <div class="chart-header">
            <div class="title-area">
              <div class="chart-title">合同/回款额</div>
              <div class="unit-text">单位：万元</div>
            </div>
            <div class="legend-area">
              <div class="legend-item">
                <div class="legend-dot contract-dot"></div>
                <span>合同额</span>
              </div>
              <div class="legend-item">
                <div class="legend-dot repayment-dot"></div>
                <span>回款额</span>
              </div>
            </div>
          </div>
          <div v-if="hasContractChartData" ref="contractChart" class="chart"></div>
          <div v-else class="chart-empty">
            <van-empty description="暂无图表数据" />
          </div>
        </div>
        <div class="chart-container">
          <div class="chart-header">
            <div class="title-area">
              <div class="chart-title">合同/回款年度累计趋势</div>
              <div class="unit-text">单位：万元</div>
            </div>
            <div class="legend-area">
              <div class="legend-item">
                <div class="legend-dot contract-dot"></div>
                <span>合同额</span>
              </div>
              <div class="legend-item">
                <div class="legend-dot repayment-dot"></div>
                <span>回款额</span>
              </div>
            </div>
          </div>
          <div v-if="hasTrendChartData" ref="trendChart" class="chart"></div>
          <div v-else class="chart-empty">
            <van-empty description="暂无趋势数据" />
          </div>
        </div>
        <div class="chart-container">
          <div class="chart-header">
            <div class="title-area">
              <div class="chart-title">销售额同比</div>
              <div class="unit-text">单位：万元</div>
            </div>
            <div class="legend-area">
              <div class="legend-item">
                <div class="legend-dot prev-year-dot"></div>
                <span>{{ salesComparisonData.prevYear || lastYear }}</span>
              </div>
              <div class="legend-item">
                <div class="legend-dot current-year-dot"></div>
                <span>{{ salesComparisonData.currentYear || currentYear }}</span>
              </div>
            </div>
          </div>
          <div v-if="hasSalesComparisonData" ref="salesComparisonChart" class="chart"></div>
          <div v-else class="chart-empty">
            <van-empty description="暂无同比数据" />
          </div>
        </div>
      </div>
    </div>

    <van-popup
      v-model="showDatePopup"
      position="bottom"
      round
      :style="{ height: '242px' }"
    >
      <div class="date-popup">
        <div class="popup-header">
          <div class="date-popup-title">时间段</div>
          <van-icon
            name="cross"
            class="close-icon"
            @click="showDatePopup = false"
          />
        </div>
        <div class="date-type-tabs">
          <div
            v-for="(item, index) in dateTypeOptions"
            :key="index"
            :class="['date-type-item', { active: dateType === item.value }]"
            @click="selectDateType(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </van-popup>

    <van-popup
      v-model="showCustomDatePopup"
      position="bottom"
      round
      :style="{ height: 'auto' }"
    >
      <div class="custom-date-popup">
        <div class="popup-header">
          <div class="date-popup-title">自定义时间</div>
          <van-icon
            name="cross"
            class="close-icon"
            @click="showCustomDatePopup = false"
          />
        </div>
        <div class="date-input-container">
          <div class="date-input">
            <input
              type="text"
              readonly
              :value="customStartDate"
              placeholder="开始"
              class="custom-date-input"
              :class="{
                active: activeInput === 'start',
                selected: !!customStartDate
              }"
              @click="handleDateInputClick('start')"
            />
          </div>
          <span class="date-separator">—</span>
          <div class="date-input">
            <input
              type="text"
              readonly
              :value="customEndDate"
              placeholder="结束"
              class="custom-date-input"
              :class="{
                active: activeInput === 'end',
                selected: !!customEndDate,
                disabled: !isEndDateEnabled
              }"
              @click="handleDateInputClick('end')"
            />
          </div>
        </div>
        <div class="date-picker-container" v-if="isPickerVisible">
          <div class="date-picker-column">
            <div
              v-for="year in yearList"
              :key="'year-' + year"
              :class="[
                'date-picker-item',
                selectedDate && selectedDate.getFullYear() === year
                  ? 'active'
                  : ''
              ]"
              @click="selectYear(year)"
            >
              {{ year }}年
            </div>
          </div>
          <div class="date-picker-column">
            <div
              v-for="month in 12"
              :key="'month-' + month"
              :class="[
                'date-picker-item',
                selectedDate && selectedDate.getMonth() + 1 === month
                  ? 'active'
                  : ''
              ]"
              @click="selectMonth(month)"
            >
              {{ month.toString().padStart(2, "0") }}月
            </div>
          </div>
          <div class="date-picker-column">
            <div
              v-for="day in getDaysInMonth()"
              :key="'day-' + day"
              :class="[
                'date-picker-item',
                selectedDate && selectedDate.getDate() === day ? 'active' : ''
              ]"
              @click="selectDay(day)"
            >
              {{ day.toString().padStart(2, "0") }}日
            </div>
          </div>
        </div>
        <div class="date-confirm-btn">
          <van-button type="warning" block @click="confirmCustomDate"
            >确定</van-button
          >
        </div>
      </div>
    </van-popup>

    <!-- 蒙层Loading效果 -->
    <div v-if="pageLoading" class="loading-overlay">
      <div class="loading-content">
        <van-loading type="spinner" size="24px" color="#FF9900" />
        <div class="loading-text">数据加载中...</div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  NavBar,
  Icon,
  Image as VanImage,
  Popup,
  Field,
  Button,
  Loading,
  List,
  Empty
} from "vant";
import * as echarts from "echarts/core";
import { LineChart, BarChart } from "echarts/charts";
import {
  TooltipComponent,
  GridComponent,
  LegendComponent
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";
import {
  getOrganizeCountList,
  getSalesContractEchars,
  getPersonCountList,
  getYearCountListEchars
} from "api/contractRepayment";

echarts.use([
  TooltipComponent,
  GridComponent,
  LegendComponent,
  LineChart,
  BarChart,
  CanvasRenderer
]);

export default {
  components: {
    [NavBar.name]: NavBar,
    [Icon.name]: Icon,
    [VanImage.name]: VanImage,
    [Popup.name]: Popup,
    [Field.name]: Field,
    [Button.name]: Button,
    [Loading.name]: Loading,
    [List.name]: List,
    [Empty.name]: Empty
  },
  data() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const lastYear = currentYear - 1;
    const yearList = [];
    for (let year = 2015; year <= currentYear; year++) {
      yearList.push(year);
    }

    return {
      currentView: "organization",
      personnelData: [],
      originalPersonnelData: [],
      selectedDepId: null,
      selectedDepName: "",
      personnelCurrentSort: "contractAmount",
      personnelSortOrder: "desc",
      personnelTotalData: {},
      tableData: [],
      originalTableData: [],
      pageLoading: false,
      totalData: {},
      currentSort: "contractAmount",
      sortOrder: "desc",
      columnFields: [
        { label: "组织架构", field: "depName" },
        { label: "合同金额(万)", field: "contractAmount" },
        { label: "硬件金额(万)", field: "hardwareAmount" },
        { label: "软件金额(万)", field: "softwareAmount" },
        { label: "施工金额(万)", field: "constructionAmount" },
        { label: "回款金额(万)", field: "repaymentAmount" }
      ],
      chartTimeRange: "month",
      chartDataMonth: null,
      chartDataYear: null,
      annualTrendData: null,
      salesComparisonData: {
        xAxis: [],
        prevYearData: [],
        currentYearData: [],
        prevYear: null,
        currentYear: null
      },
      currentTab: "time",
      dateType: "today",
      showDatePopup: false,
      showCustomDatePopup: false,
      customStartDate: "",
      customEndDate: "",
      datePickerMode: "start",
      selectedDate: null,
      isPickerVisible: false,
      yearList: yearList,
      currentDate: currentDate,
      isEndDateEnabled: false,
      dateTypeOptions: [
        { label: "今天", value: "today" },
        { label: "昨日", value: "yesterday" },
        { label: "本周", value: "week" },
        { label: "本月", value: "month" },
        { label: "今年", value: "year" }
      ],
      currentYear: currentYear,
      lastYear: lastYear,
      activeInput: ""
    };
  },

  mounted() {
    this.initRouteParams();
    this.initData();
  },

  beforeDestroy() {
    console.log("组件销毁");
  },

  computed: {
    customDateRangeText() {
      if (
        this.currentTab !== "custom" ||
        (!this.customStartDate && !this.customEndDate)
      ) {
        return "自定义";
      }
      const startFormatted = this.formatDateToHyphen(this.customStartDate);
      const endFormatted = this.formatDateToHyphen(
        this.customEndDate || this.customStartDate
      );
      return `${startFormatted} 至 ${endFormatted}`;
    },

    // 检查合同图表是否有数据
    hasContractChartData() {
      const data = this.chartTimeRange === "month" ? this.chartDataMonth : this.chartDataYear;
      return data && data.xAxis && data.xAxis.length > 0;
    },

    // 检查趋势图表是否有数据
    hasTrendChartData() {
      return this.annualTrendData && this.annualTrendData.xAxis && this.annualTrendData.xAxis.length > 0;
    },

    // 检查销售额同比图表是否有数据
    hasSalesComparisonData() {
      return this.salesComparisonData && this.salesComparisonData.xAxis && this.salesComparisonData.xAxis.length > 0;
    }
  },

  methods: {
    async handleNavLeftClick() {
      if (this.currentView === "personnel") {
        await this.showOrganizationView();
      } else {
        this.backToApp();
      }
    },

    async showOrganizationView() {
      this.pageLoading = true;
      try {
        this.currentView = "organization";
        // 切换回组织架构视图时重新获取数据，确保应用最新的筛选条件
        await this.getOrganizeCountList();
      } finally {
        this.pageLoading = false;
      }
    },

    async showPersonnelView(item) {
      if (item.depName === "汇总") return;
      this.pageLoading = true;
      try {
        this.selectedDepId = item.depId;
        this.selectedDepName = item.depName;
        await this.getPersonCountList();
        this.currentView = "personnel";
        this.$nextTick(() => {
          window.scrollTo({ top: 0, behavior: "smooth" });
        });
      } finally {
        this.pageLoading = false;
      }
    },

    async getPersonCountList() {
      this.personnelData = [];
      this.originalPersonnelData = [];
      this.personnelTotalData = {};
      let params = {
        timeType:
          this.currentTab === "time"
            ? this.getTimeTypeValue(this.dateType)
            : this.getTimeTypeValue("custom"),
        depId: this.selectedDepId
      };
      if (params.timeType === 6) {
        params.startTime = this.customStartDate
          ? this.formatDateToHyphen(this.customStartDate)
          : "";
        params.endTime = this.customEndDate
          ? this.formatDateToHyphen(this.customEndDate)
          : "";
      }
      try {
        const res = await getPersonCountList(params);
        if (res) {
          this.personnelData = res.list || [];
          this.originalPersonnelData = JSON.parse(
            JSON.stringify(res.list || [])
          );
          this.personnelTotalData = {
            contractAmount: res?.contractAmount || 0,
            hardwareAmount: res?.hardwareAmount || 0,
            softwareAmount: res?.softwareAmount || 0,
            constructionAmount: res?.constructionAmount || 0,
            repaymentAmount: res?.repaymentAmount || 0
          };
          
          // 默认按合同金额降序排序
          this.personnelCurrentSort = "contractAmount";
          this.personnelSortOrder = "desc";
          this.sortPersonnelTableData();
        }
      } catch (error) {
        console.error("获取人员数据失败:", error);
      }
    },

    handlePersonnelSort(field) {
      if (this.personnelCurrentSort === field) {
        this.personnelSortOrder =
          this.personnelSortOrder === "asc"
            ? "desc"
            : this.personnelSortOrder === "desc"
            ? ""
            : "asc";
      } else {
        this.personnelCurrentSort = field;
        this.personnelSortOrder = "asc";
      }
      this.sortPersonnelTableData();
    },

    sortPersonnelTableData() {
      if (!this.personnelCurrentSort || !this.personnelSortOrder) {
        this.personnelData = JSON.parse(
          JSON.stringify(this.originalPersonnelData)
        );
        return;
      }
      this.personnelData.sort((a, b) => {
        const valueA = this.parseNumericValue(a[this.personnelCurrentSort]);
        const valueB = this.parseNumericValue(b[this.personnelCurrentSort]);
        return this.personnelSortOrder === "asc"
          ? valueA - valueB
          : valueB - valueA;
      });
    },

    getTimeTypeValue(dateType) {
      const typeMap = {
        today: 1,
        yesterday: 2,
        week: 3,
        month: 4,
        year: 5,
        custom: 6
      };
      return typeMap[dateType] || "";
    },

    // 初始化路由参数
    initRouteParams() {
      const timeType = this.$route.query.timeType;
      if (timeType) {
        // 根据timeType参数设置对应的dateType
        const timeTypeMap = {
          '1': 'today',
          '2': 'yesterday', 
          '3': 'week',
          '4': 'month',
          '5': 'year'
        };
        
        if (timeTypeMap[timeType]) {
          this.dateType = timeTypeMap[timeType];
          this.currentTab = 'time';
        }
      }
    },

    async initData() {
      this.pageLoading = true;
      await this.getOrganizeCountList();
      await this.getSalesContractEcharsMonth();
      await this.getSalesContractEcharsYear();
      await this.getYearCountListEchars();
      this.$nextTick(() => {
        this.initTableLayout();
      });
    },

    async getOrganizeCountList() {
      try {
        const params = {
          timeType:
            this.currentTab === "time"
              ? this.getTimeTypeValue(this.dateType)
              : this.getTimeTypeValue("custom")
        };
        if (params.timeType === 6) {
          params.startTime = this.customStartDate
            ? this.formatDateToHyphen(this.customStartDate)
            : "";
          params.endTime = this.customEndDate
            ? this.formatDateToHyphen(this.customEndDate)
            : "";
        }
        const res = await getOrganizeCountList(params);
        if (res && res.list) {
          this.tableData = res.list || [];
          this.originalTableData = JSON.parse(JSON.stringify(res.list || []));
          this.totalData = {
            contractAmount: res?.contractAmount || 0,
            hardwareAmount: res?.hardwareAmount || 0,
            softwareAmount: res?.softwareAmount || 0,
            constructionAmount: res?.constructionAmount || 0,
            repaymentAmount: res?.repaymentAmount || 0
          };
          
          // 默认按合同金额降序排序
          this.currentSort = "contractAmount";
          this.sortOrder = "desc";
          this.sortTableData();
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.pageLoading = false;
      }
    },

    async getSalesContractEcharsMonth() {
      try {
        const res = await getSalesContractEchars({ type: 2, timeType: 4 });
        if (res) {
          this.chartDataMonth = {
            xAxis: res.times || [],
            contract: res.contractAmounts || [],
            repayment: res.repaymentAmounts || []
          };
          this.$nextTick(() => this.updateCharts());
        } else {
          console.error("获取本月图表数据失败:", res?.msg || "接口返回异常");
        }
      } catch (error) {
        console.error("获取本月图表数据异常:", error);
      }
    },

    async getSalesContractEcharsYear() {
      try {
        const res = await getSalesContractEchars({ type: 2, timeType: 5 });
        if (res) {
          const yearData = {
            xAxis: res.times || [],
            contract: res.contractAmounts || [],
            repayment: res.repaymentAmounts || []
          };
          this.chartDataYear = yearData;
          this.annualTrendData = yearData;
          this.$nextTick(() => this.updateCharts());
        } else {
          console.error("获取本年图表数据失败:", res?.msg || "接口返回异常");
        }
      } catch (error) {
        console.error("获取本年图表数据异常:", error);
      }
    },

    // 获取组织年度合同回款统计列表echars
    async getYearCountListEchars() {
      try {
        const res = await getYearCountListEchars();
        if (res && Array.isArray(res)) {
          // 处理接口返回数据，构建销售额同比数据
          this.processSalesComparisonData(res);
        }
      } catch(error) {
        console.error('获取年度统计数据失败:', error);
      }
    },

    // 通用的tooltip格式化方法
    createTooltipFormatter(chartType = 'default') {
      return (params) => {
        const timeLabel = params[0].axisValue;
        let html = `<div style="padding: 8px;">`;
        
        // 根据图表类型设置时间显示
        if (chartType === 'trend') {
          html += `<div style="font-weight: bold; margin-bottom: 6px; color: #333;">${timeLabel}月</div>`;
        } else if (chartType === 'comparison') {
          html += `<div style="font-weight: bold; margin-bottom: 6px; color: #333;">${timeLabel}月</div>`;
        } else {
          html += `<div style="font-weight: bold; margin-bottom: 6px; color: #333;">${timeLabel}日</div>`;
        }
        
        params.forEach((param) => {
          const color = param.color;
          const value = param.value;
          const name = param.seriesName;
          html += `<div style="display: flex; align-items: center; margin-bottom: 4px;">`;
          html += `<span style="display: inline-block; width: 8px; height: 8px; background-color: ${color}; border-radius: 50%; margin-right: 6px;"></span>`;
          html += `<span style="color: #666; margin-right: 8px;">${name}:</span>`;
          html += `<span style="font-weight: bold; color: #333;">${value}万元</span>`;
          html += `</div>`;
        });
        
        html += `</div>`;
        return html;
      };
    },

    // 通用的tooltip配置
    getTooltipConfig(chartType = 'default') {
      return {
        trigger: "axis",
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#E5E5E5',
        borderWidth: 1,
        textStyle: {
          color: '#333',
          fontSize: 12
        },
        extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border-radius: 4px;',
        formatter: this.createTooltipFormatter(chartType)
      };
    },

    // 处理销售额同比数据
    processSalesComparisonData(data) {
      // 按年份分组数据
      const groupedByYear = {};
      data.forEach(item => {
        const year = item.year;
        if (!groupedByYear[year]) {
          groupedByYear[year] = {};
        }
        groupedByYear[year][item.month] = item.contractAmount || 0;
      });

      // 动态获取接口返回数据中的年份，按降序排列
      const availableYears = Object.keys(groupedByYear).map(year => parseInt(year)).sort((a, b) => b - a);
      
      // 取最新的两个年份进行对比，如果只有一个年份，则与前一年对比
      let currentYear, prevYear;
      if (availableYears.length >= 2) {
        currentYear = availableYears[0];
        prevYear = availableYears[1];
      } else if (availableYears.length === 1) {
        currentYear = availableYears[0];
        prevYear = currentYear - 1;
      } else {
        // 如果没有数据，使用默认值
        currentYear = new Date().getFullYear();
        prevYear = currentYear - 1;
      }

      // 构建12个月的数据数组
      const months = Array.from({length: 12}, (_, i) => i + 1);
      const currentYearData = months.map(month => groupedByYear[currentYear]?.[month] || 0);
      const prevYearData = months.map(month => groupedByYear[prevYear]?.[month] || 0);

      // 更新销售额同比数据
      this.salesComparisonData = {
        xAxis: months.map(m => m.toString()),
        prevYearData: prevYearData,
        currentYearData: currentYearData,
        prevYear: prevYear,
        currentYear: currentYear
      };

      // 刷新图表
      this.$nextTick(() => {
        this.initSalesComparisonChart();
      });
    },

    formatDateToHyphen(dateStr) {
      if (!dateStr) return "";
      const match = dateStr.match(/(\d{4})年(\d{2})月(\d{2})日/);
      if (match) {
        return `${match[1]}-${match[2]}-${match[3]}`;
      }
      return "";
    },

    handleRowClick(item) {
      if (item.depName === "汇总") {
        return;
      }
    },

    getDateTypeLabel() {
      const found = this.dateTypeOptions.find(
        item => item.value === this.dateType
      );
      return found ? found.label : "今天";
    },

    handleTabClick(tab) {
      this.currentTab = tab;
      if (tab === "time") {
        this.showDatePopup = true;
      } else {
        this.showCustomDatePopup = true;
        this.isPickerVisible = false;
      }
    },

    async selectDateType(type) {
      this.dateType = type;
      this.showDatePopup = false;
      this.currentTab = "time";
      this.customStartDate = "";
      this.customEndDate = "";
      this.isEndDateEnabled = false;

      this.pageLoading = true;
      try {
        // 修正: 根据当前视图刷新对应的数据
        if (this.currentView === "organization") {
          await this.getOrganizeCountList();
        } else {
          await this.getPersonCountList();
        }
      } finally {
        this.pageLoading = false;
      }
    },

    handleDateInputClick(mode) {
      if (mode === "end" && !this.isEndDateEnabled) {
        this.$toast("请先选择开始日期");
        return;
      }
      this.activeInput = mode;
      this.datePickerMode = mode;
      this.isPickerVisible = true;
      if (mode === "start" && this.customStartDate) {
        this.selectedDate = this.parseCustomDate(this.customStartDate);
      } else if (mode === "end" && this.customEndDate) {
        this.selectedDate = this.parseCustomDate(this.customEndDate);
      } else {
        this.selectedDate = new Date();
        if (mode === "end" && this.customStartDate) {
          const startDate = this.parseCustomDate(this.customStartDate);
          if (this.selectedDate < startDate) {
            this.selectedDate = new Date(startDate);
          }
        }
      }
    },

    parseCustomDate(dateStr) {
      if (!dateStr) return new Date();
      const match = dateStr.match(/(\d{4})年(\d{2})月(\d{2})日/);
      if (match) {
        return new Date(
          parseInt(match[1]),
          parseInt(match[2]) - 1,
          parseInt(match[3])
        );
      }
      return new Date();
    },

    getDaysInMonth() {
      if (!this.selectedDate) return 31;
      const year = this.selectedDate.getFullYear();
      const month = this.selectedDate.getMonth();
      return new Date(year, month + 1, 0).getDate();
    },

    selectYear(year) {
      if (!this.selectedDate) this.selectedDate = new Date();
      const newDate = new Date(this.selectedDate);
      newDate.setFullYear(year);
      if (this.datePickerMode === "end" && this.customStartDate) {
        const startDate = this.parseCustomDate(this.customStartDate);
        if (newDate < startDate) newDate.setTime(startDate.getTime());
      }
      this.selectedDate = newDate;
      this.updateCustomDate();
    },

    selectMonth(month) {
      if (!this.selectedDate) this.selectedDate = new Date();
      const newDate = new Date(this.selectedDate);
      const originalDay = newDate.getDate();
      newDate.setMonth(month - 1);
      if (newDate.getDate() !== originalDay) {
        newDate.setDate(0);
      }
      if (this.datePickerMode === "end" && this.customStartDate) {
        const startDate = this.parseCustomDate(this.customStartDate);
        if (newDate < startDate) newDate.setTime(startDate.getTime());
      }
      this.selectedDate = newDate;
      this.updateCustomDate();
    },

    selectDay(day) {
      if (!this.selectedDate) this.selectedDate = new Date();
      const newDate = new Date(this.selectedDate);
      newDate.setDate(day);
      if (this.datePickerMode === "end" && this.customStartDate) {
        const startDate = this.parseCustomDate(this.customStartDate);
        if (newDate < startDate) newDate.setTime(startDate.getTime());
      }
      this.selectedDate = newDate;
      this.updateCustomDate();
      this.isPickerVisible = false;
      this.clearActiveInput();
    },

    updateCustomDate() {
      if (!this.selectedDate) return;
      const date = this.selectedDate;
      const formattedDate = `${date.getFullYear()}年${(date.getMonth() + 1)
        .toString()
        .padStart(2, "0")}月${date
        .getDate()
        .toString()
        .padStart(2, "0")}日`;
      if (this.datePickerMode === "start") {
        this.customStartDate = formattedDate;
        this.isEndDateEnabled = true;
        if (
          this.customEndDate &&
          this.parseCustomDate(this.customEndDate) < this.selectedDate
        ) {
          this.customEndDate = "";
        }
      } else {
        this.customEndDate = formattedDate;
      }
    },

    updateCharts() {
      this.$nextTick(() => {
        if (this.hasContractChartData) {
          this.initContractChart();
        }
        if (this.hasTrendChartData) {
          this.initTrendChart();
        }
        if (this.hasSalesComparisonData) {
          this.initSalesComparisonChart();
        }
      });
    },

    initContractChart() {
      const chartDom = this.$refs.contractChart;
      if (!chartDom) return;
      const myChart = echarts.init(chartDom);
      const chartData =
        this.chartTimeRange === "month"
          ? this.chartDataMonth
          : this.chartDataYear;
      if (!chartData) return;
      const option = {
        tooltip: this.getTooltipConfig('contract'),
        legend: { show: false },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "5%",
          containLabel: true
        },
        xAxis: { type: "category", boundaryGap: false, data: chartData.xAxis },
        yAxis: {
          type: "value",
          splitLine: { lineStyle: { type: "dashed", color: "#E5E5E5" } },
          axisLine: { show: false },
          axisTick: { show: false }
        },
        series: [
          {
            name: "合同额",
            type: "line",
            data: chartData.contract,
            smooth: true,
            symbol: "circle",
            symbolSize: 6,
            itemStyle: { color: "#FF9900" },
            lineStyle: { color: "#FF9900", width: 2 },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: "rgba(255, 153, 0, 0.2)" },
                  { offset: 1, color: "rgba(255, 153, 0, 0)" }
                ]
              }
            }
          },
          {
            name: "回款额",
            type: "line",
            data: chartData.repayment,
            smooth: true,
            symbol: "circle",
            symbolSize: 6,
            itemStyle: { color: "#4ECB73" },
            lineStyle: { color: "#4ECB73", width: 2 },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: "rgba(78, 203, 115, 0.2)" },
                  { offset: 1, color: "rgba(78, 203, 115, 0)" }
                ]
              }
            }
          }
        ]
      };
      myChart.setOption(option);
      window.addEventListener("resize", () => myChart.resize());
    },

    initTrendChart() {
      const chartDom = this.$refs.trendChart;
      if (!chartDom) return;
      const myChart = echarts.init(chartDom);
      const chartData = this.annualTrendData;
      if (!chartData) return;
      const option = {
        tooltip: this.getTooltipConfig('trend'),
        legend: { show: false },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "5%",
          containLabel: true
        },
        xAxis: { type: "category", boundaryGap: false, data: chartData.xAxis },
        yAxis: {
          type: "value",
          splitLine: { lineStyle: { type: "dashed", color: "#E5E5E5" } },
          axisLine: { show: false },
          axisTick: { show: false }
        },
        series: [
          {
            name: "合同额",
            type: "line",
            data: chartData.contract,
            smooth: true,
            symbol: "circle",
            symbolSize: 6,
            itemStyle: { color: "#FF9900" },
            lineStyle: { color: "#FF9900", width: 2 },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: "rgba(255, 153, 0, 0.2)" },
                  { offset: 1, color: "rgba(255, 153, 0, 0)" }
                ]
              }
            }
          },
          {
            name: "回款额",
            type: "line",
            data: chartData.repayment,
            smooth: true,
            symbol: "circle",
            symbolSize: 6,
            itemStyle: { color: "#4ECB73" },
            lineStyle: { color: "#4ECB73", width: 2 },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: "rgba(78, 203, 115, 0.2)" },
                  { offset: 1, color: "rgba(78, 203, 115, 0)" }
                ]
              }
            }
          }
        ]
      };
      myChart.setOption(option);
      window.addEventListener("resize", () => myChart.resize());
    },

    initSalesComparisonChart() {
      const chartDom = this.$refs.salesComparisonChart;
      if (!chartDom) return;
      
      // 验证数据是否已加载
      if (!this.salesComparisonData.xAxis.length) {
        console.log('销售额同比数据尚未加载');
        return;
      }
      
      const myChart = echarts.init(chartDom);
      const option = {
        tooltip: this.getTooltipConfig('comparison'),
        legend: { show: false },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "5%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: this.salesComparisonData.xAxis,
          axisTick: { alignWithLabel: true }
        },
        yAxis: {
          type: "value",
          splitLine: { lineStyle: { type: "dashed", color: "#E5E5E5" } },
          axisLine: { show: false },
          axisTick: { show: false }
        },
        series: [
          {
            name: this.salesComparisonData.prevYear?.toString() || this.lastYear.toString(),
            type: "bar",
            data: this.salesComparisonData.prevYearData,
            itemStyle: { color: "#FF9900" },
            barWidth: "20%",
            barGap: "30%"
          },
          {
            name: this.salesComparisonData.currentYear?.toString() || this.currentYear.toString(),
            type: "bar",
            data: this.salesComparisonData.currentYearData,
            itemStyle: { color: "#4ecb73" },
            barWidth: "20%"
          }
        ]
      };
      myChart.setOption(option);
      window.addEventListener("resize", () => myChart.resize());
    },

    async confirmCustomDate() {
      if (!this.customStartDate) {
        this.$toast("请选择开始日期");
        return;
      }
      if (!this.customEndDate) this.customEndDate = this.customStartDate;
      this.clearActiveInput();
      this.showCustomDatePopup = false;
      this.currentTab = "custom";

      this.pageLoading = true;
      try {
        // 修正: 根据当前视图刷新对应的数据
        if (this.currentView === "organization") {
          await this.getOrganizeCountList();
        } else {
          await this.getPersonCountList();
        }
      } finally {
        this.pageLoading = false;
      }
    },

    updateMainChart() {
      this.$nextTick(() => {
        this.initContractChart();
      });
    },

    handleSort(field) {
      if (this.currentSort === field) {
        this.sortOrder =
          this.sortOrder === "asc"
            ? "desc"
            : this.sortOrder === "desc"
            ? ""
            : "asc";
      } else {
        this.currentSort = field;
        this.sortOrder = "asc";
      }
      this.sortTableData();
    },

    clearActiveInput() {
      this.activeInput = "";
    },

    sortTableData() {
      if (!this.currentSort || !this.sortOrder) {
        this.tableData = JSON.parse(JSON.stringify(this.originalTableData));
        return;
      }
      this.tableData.sort((a, b) => {
        const valueA = this.parseNumericValue(a[this.currentSort]);
        const valueB = this.parseNumericValue(b[this.currentSort]);
        return this.sortOrder === "asc" ? valueA - valueB : valueB - valueA;
      });
    },

    parseNumericValue(value) {
      if (value === null || value === undefined || value === "") return 0;
      if (typeof value === "number") return isNaN(value) ? 0 : value;
      const numValue = parseFloat(String(value).replace(/,/g, ""));
      return isNaN(numValue) ? 0 : numValue;
    },

    initTableLayout() {
      this.$nextTick(() => console.log("表格布局初始化完成"));
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";

/* --- 全新、更简单的动画样式，解决高度问题 --- */
.table-view-wrapper {
  position: relative;
  margin-top: 12px;
}

/* 动画效果: 'out-in' 模式确保旧元素先离开，新元素再进入，
  这对于自适应高度的容器至关重要，可以防止高度在切换瞬间发生"跳动"
*/

/* 定义离开过程的动画 */
.slide-view-leave-active {
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
  /* 关键：在离开动画执行时，将旧元素绝对定位，使其不影响新元素的高度计算 */
  position: absolute;
  width: 100%;
  top: 0;
}
.slide-view-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 定义进入过程的动画 */
.slide-view-enter-active {
  transition: opacity 0.3s ease-in, transform 0.3s ease-in;
  /* 稍微延迟进入，让动画看起来更有层次 */
  transition-delay: 0.1s;
}
  .slide-view-enter {
    opacity: 0;
    transform: translateX(20px);
  }

  /* 蒙层Loading样式 */
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 24px 32px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(4px);
  }

  .loading-text {
    margin-top: 12px;
    color: #333;
    font-size: 14px;
    font-weight: 500;
  }

  /* 图表空状态样式 */
  .chart-empty {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  </style>
