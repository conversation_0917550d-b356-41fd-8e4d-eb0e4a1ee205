<template>
  <div class="card-video">
    <div class="video-box">
      <div v-if="!videoUrl">暂无数据</div>
      <my-video-player
        v-else
        :videoUrl="videoUrl"
        :coverUrl="coverurl"
      ></my-video-player>
    </div>
  </div>
</template>

<script>
import { getCardFileById } from "../../api/card";
import MyVideoPlayer from "./myVideoPlayer";

export default {
  components: {
    MyVideoPlayer
  },
  data() {
    return {
      videoUrl: "",
      coverurl:
        "https://ovopark.oss-cn-hangzhou.aliyuncs.com/front/CRM/fD5g97rRbKLw7zM/*face.png"
    };
  },
  mounted() {
    let id = this.$route.query.id;
    let knowledgeId = this.$route.query.knowledgeId;
    this.getCardFileById({
      fileId: id,
      knowledgeId
    });
  },
  methods: {
    // 根据id获取名片详情
    async getCardFileById(params) {
      try {
        const res = await getCardFileById(params);
        if (res) {
          this.videoUrl = res.fileUrl;
        }
      } catch (error) {
        console.log(error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.card-video {
  height: 100%;
  width: 100%;
  background: #fff;
  display: flex;
  justify-content: center;
  overflow: hidden;

  .video-box {
    width: 100%;
    height: 200px;
    display: flex;
    margin-top: 50px;
    justify-content: center;
  }

  .video-size {
    width: 750rpx;
    height: 200px;
  }
}
</style>
