import Vue from "vue";
import {
  Toast
} from "vant";

Vue.use(Toast);

/**
 * 返回请求错误码
 */
const ERROR_CODE = {
  OK: 'ok',
  TOKEN_INVALID: 113099,
  SUCCESS: 0,
  ERROR: 6,
};

/**
 * 请求返回数据处理
 */
export const responseHandler = (res) => {

    const originData = res.data || {};

    if (
      Object.prototype.hasOwnProperty.call(originData, "result") &&
      Object.prototype.hasOwnProperty.call(originData, "code")
    ) {
      // 格式1：{ result: ok, code: 0, data: <any> }
      return new Promise((resolve, reject) => {
        if (originData.code == ERROR_CODE.SUCCESS) {
          resolve(originData.data);
        } else if (originData.code == ERROR_CODE.TOKEN_INVALID) {
          resolve(originData.data);
        } else {
          Toast.fail({
            duration: 3000,
            message: originData.result || "请求返回出错",
          });
          reject(originData);
        }
      });
    } else if (
      Object.prototype.hasOwnProperty.call(originData, "result") &&
      Object.prototype.hasOwnProperty.call(originData, "data")
    ) {
      // 格式2：{ result: ok, data: <any> }
      return new Promise((resolve, reject) => {
        if (originData.result == ERROR_CODE.OK) {
          resolve(originData.data);
        } else {
          Toast.fail({
            duration: 3000,
            message: originData.result || "请求返回出错",
          });
          // Toast.fail(originData.result || "请求返回出错");
          reject(originData);
        }
      });
    } else if (
      Object.prototype.hasOwnProperty.call(originData, "data") &&
      Object.prototype.hasOwnProperty.call(originData, "code")
    ) {
      // 格式2：{ code: 0, data: <any>, isError }
      return new Promise((resolve, reject) => {
        if (originData.code == ERROR_CODE.SUCCESS) {
          resolve(originData.data);
        } else {
          Toast.fail({
            duration: 3000,
            message: originData || "请求返回出错",
          });
          // Toast.fail(originData || "请求返回出错");
          reject(originData);
        }
      });
    } else if (
      Object.prototype.hasOwnProperty.call(originData, "data") &&
      Object.prototype.hasOwnProperty.call(originData, "code")
    ) {
      // 格式2：{ code: 0, data: <any>, isError }
      return new Promise((resolve, reject) => {
        if (originData.code == ERROR_CODE.SUCCESS) {
          resolve(originData.data);
        } else {
          Toast.fail({
            duration: 3000,
            message: originData || "请求返回出错",
          });
          // Toast.fail(originData || "请求返回出错");
          reject(originData);
        }
      });
    }
};
