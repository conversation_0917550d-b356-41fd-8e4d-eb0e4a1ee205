<template>
  <div class="contribution-repayment-detail">
    <!-- 页面标题 -->
    <div class="page-header">
      <van-nav-bar
        title="贡献回款详情"
        class="page-header-nav-bar"
        left-arrow
        @click-left="onClickLeft"
      />
    </div>

    <!-- 个人数据部分 -->
    <div class="personal-data">
      <div class="section-title">个人数据</div>
      <div class="filter-tabs">
        <div class="tab-wrapper">
          <div
            :class="['tab-item', { active: currentTab === 'time' }]"
            @click="handleTabClick('time')"
          >
            <span>{{ getDateTypeLabel() }}</span>
            <van-icon name="arrow-down" size="11" />
          </div>
          <div
            :class="['tab-item', { active: currentTab === 'custom' }]"
            @click="handleTabClick('custom')"
          >
            <span>{{ customDateRangeText }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="table-wrapper" ref="tableWrapper">
        <table class="data-table">
          <!-- 表头 -->
          <thead class="table-header">
            <tr>
              <th class="col-username">员工姓名</th>
              <th class="col-month">所属月份</th>
              <th class="col-contract">合同号</th>
              <th class="col-percentage">比例</th>
              <th class="col-amount">贡献回款</th>
              <th class="col-margin">预估毛利</th>
            </tr>
          </thead>
          <!-- 表体 -->
          <tbody class="table-body">
            <template v-if="allData && allData.length > 0">
              <tr
                class="table-row"
                v-for="(item, index) in allData"
                :key="index"
                @click="handleRowClick(item)"
              >
                <td class="col-username" :title="item.username">{{ item.username }}</td>
                <td class="col-month">{{ item.month }}</td>
                <td class="col-contract" :title="item.contractNo">{{ item.contractNo }}</td>
                <td class="col-percentage">{{ item.percentage }}%</td>
                <td class="col-amount">{{ item.amount }}</td>
                <td class="col-margin">{{ item.grossMargin }}</td>
              </tr>
            </template>
          </tbody>
        </table>

        <!-- 加载状态显示 -->
        <div class="loading-status" v-if="allData && allData.length > 0">
          <div v-if="pageLoading" class="loading-text">加载中...</div>
          <div v-else-if="!hasMoreData" class="finished-text">没有更多了</div>
        </div>

        <!-- 汇总行 - 在滚动容器内 -->
        <div class="summary-container" :class="summaryPositionClass" v-if="allData && allData.length > 0">
          <table class="summary-table">
            <tr class="summary-row">
              <td class="col-username">汇总</td>
              <td class="col-month">-</td>
              <td class="col-contract">-</td>
              <td class="col-percentage">-</td>
              <td class="col-amount">{{ totalData.repaymentAmount }}</td>
              <td class="col-margin">{{ totalData.grossProfit }}</td>
            </tr>
          </table>
        </div>

        <!-- 无数据时显示空状态 -->
        <div v-if="!allData || allData.length === 0" class="empty-state-container">
          <van-empty description="暂无数据" />
        </div>
      </div>
    </div>

    <!-- 时间类型选择弹窗 -->
    <van-popup
      v-model="showDatePopup"
      position="bottom"
      round
      :style="{ height: 'auto' }"
    >
      <div class="date-popup">
        <div class="popup-header">
          <div class="date-popup-title">选择时间</div>
          <van-icon
            name="cross"
            class="close-icon"
            @click="showDatePopup = false"
          />
        </div>
        <div class="date-type-tabs">
          <div
            v-for="option in dateTypeOptions"
            :key="option.value"
            :class="['date-type-item', { active: dateType === option.value }]"
            @click="selectDateType(option.value)"
          >
            {{ option.label }}
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 自定义日期选择弹窗 -->
    <van-popup
      v-model="showCustomDatePopup"
      position="bottom"
      round
      :style="{ height: 'auto' }"
    >
      <div class="custom-date-popup">
        <div class="popup-header">
          <div class="date-popup-title">自定义时间</div>
          <van-icon
            name="cross"
            class="close-icon"
            @click="showCustomDatePopup = false"
          />
        </div>

        <div class="date-input-container">
          <div class="date-input">
            <input
              type="text"
              readonly
              :value="customStartDate"
              placeholder="开始"
              class="custom-date-input"
              :class="{
                active: activeInput === 'start',
                selected: !!customStartDate
              }"
              @click="handleDateInputClick('start')"
            />
          </div>
          <span class="date-separator">—</span>
          <div class="date-input">
            <input
              type="text"
              readonly
              :value="customEndDate"
              placeholder="结束"
              class="custom-date-input"
              :class="{
                active: activeInput === 'end',
                selected: !!customEndDate,
                disabled: !isEndDateEnabled
              }"
              @click="handleDateInputClick('end')"
            />
          </div>
        </div>

        <div class="date-picker-container" v-if="isPickerVisible">
          <div class="date-picker-column">
            <div
              v-for="year in yearList"
              :key="'year-' + year"
              :class="[
                'date-picker-item',
                selectedDate && selectedDate.getFullYear() === year
                  ? 'active'
                  : ''
              ]"
              @click="selectYear(year)"
            >
              {{ year }}年
            </div>
          </div>
          <div class="date-picker-column">
            <div
              v-for="month in 12"
              :key="'month-' + month"
              :class="[
                'date-picker-item',
                selectedDate && selectedDate.getMonth() + 1 === month
                  ? 'active'
                  : ''
              ]"
              @click="selectMonth(month)"
            >
              {{ month.toString().padStart(2, "0") }}月
            </div>
          </div>
          <div class="date-picker-column">
            <div
              v-for="day in getDaysInMonth()"
              :key="'day-' + day"
              :class="[
                'date-picker-item',
                selectedDate && selectedDate.getDate() === day ? 'active' : ''
              ]"
              @click="selectDay(day)"
            >
              {{ day.toString().padStart(2, "0") }}日
            </div>
          </div>
        </div>

        <div class="date-confirm-btn">
          <van-button
            style="background: #ff9900; color:#fff;"
            block
            @click="confirmCustomDate"
            >确定</van-button
          >
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { NavBar, Icon, Button, Popup, Empty } from "vant";

import {
  getPaymentDetailForH5
} from "api/contractRepayment";

export default {
  name: "ContributionRepaymentDetail",
  components: {
    [NavBar.name]: NavBar,
    [Icon.name]: Icon,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Empty.name]: Empty
  },

  data() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();

    // 生成年份列表：从2015年到当前年份
    const yearList = [];
    for (let year = 2015; year <= currentYear; year++) {
      yearList.push(year);
    }

    return {
      // 分页相关状态
      pageLoading: false,
      page: 1,
      pageSize: 10,
      hasMoreData: true,
      allData: [], // 存储所有数据
      totalData: {}, // 存储汇总行数据

      // 排序相关状态
      currentSort: "amount", // 当前排序的字段，默认为'amount'
      sortOrder: "desc", // 排序方向: 'asc'升序, 'desc'降序(默认)

      // 列字段映射
      columnFields: [
        { label: "员工姓名", field: "username" },
        { label: "所属月份", field: "month" },
        { label: "合同号", field: "contractNo" },
        { label: "比例", field: "percentage" },
        { label: "贡献回款", field: "amount" },
        { label: "预估毛利", field: "grossMargin" }
      ],

      // 时间选择相关
      currentTab: "time", // 'time' 或 'custom'
      dateType: "today", // 默认选择今天
      showDatePopup: false,
      showCustomDatePopup: false,
      customStartDate: "", // 默认为空，不预先选择
      customEndDate: "", // 默认为空，不预先选择
      datePickerMode: "start", // 'start' 或 'end'
      selectedDate: null, // 当前选择的日期，默认为null
      isPickerVisible: false, // 控制日期选择器的显示
      yearList: yearList, // 可选年份列表，从2015年到当前年份
      currentDate: currentDate,
      isEndDateEnabled: false, // 控制结束日期是否可选
      dateTypeOptions: [
        { label: "今天", value: "today" },
        { label: "昨日", value: "yesterday" },
        { label: "本周", value: "week" },
        { label: "本月", value: "month" },
        { label: "今年", value: "year" }
      ],
      activeInput: "", // 当前激活的输入框：'start', 'end' 或 ''
      debounceTimer: null, // 防抖定时器
      summaryPositionClass: "", // 汇总行定位样式类
      scrollLoadDebounce: null, // 滚动加载防抖定时器
      isAutoLoading: false // 是否正在自动加载中
    };
  },

  async mounted() {
    // 初始化获取数据
    this.pageLoading = true;
    try {
      await this.initData();
    } finally {
      this.pageLoading = false;
    }

    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleResize);
    
    // 添加滚动事件监听
    this.$nextTick(() => {
      const tableWrapper = this.$refs.tableWrapper;
      if (tableWrapper) {
        tableWrapper.addEventListener("scroll", this.handleScroll);
      }
    });
    
    // 初始化完成
  },

  updated() {
    // 数据更新后重新计算汇总行位置
    this.calculateSummaryPosition();
  },

  beforeDestroy() {
    // 清理防抖定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    
    if (this.scrollLoadDebounce) {
      clearTimeout(this.scrollLoadDebounce);
    }
    
    // 清理事件监听
    window.removeEventListener("resize", this.handleResize);
    
    const tableWrapper = this.$refs.tableWrapper;
    if (tableWrapper) {
      tableWrapper.removeEventListener("scroll", this.handleScroll);
    }
  },

  computed: {
    // 自定义日期范围显示文本
    customDateRangeText() {
      if (
        this.currentTab !== "custom" ||
        (!this.customStartDate && !this.customEndDate)
      ) {
        return "自定义";
      }

      // 如果有开始和结束日期，格式化为 "YYYY-MM-DD 至 YYYY-MM-DD"
      const startFormatted = this.formatDateToHyphen(this.customStartDate);
      const endFormatted = this.formatDateToHyphen(
        this.customEndDate || this.customStartDate
      );

      return `${startFormatted} 至 ${endFormatted}`;
    }
  },

  methods: {
    // 获取timeType对应的数值
    getTimeTypeValue(dateType) {
      const typeMap = {
        today: 1, // 实时（今天）
        yesterday: 2, // 昨日
        week: 3, // 本周
        month: 4, // 本月
        year: 5, // 今年
        custom: 6 // 自定义
      };

      return typeMap[dateType] || "";
    },

    // 初始化数据
    async initData() {
      // 重置页码和数据
      this.page = 1;
      this.hasMoreData = true;
      this.allData = [];
      this.totalData = {};
      this.isAutoLoading = false;

      // 首次加载数据
      await this.loadData(true);

      // 自动填充：如果容器高度大于内容高度且还有更多数据，继续加载
      await this.autoFillContainer();

      // 计算汇总行位置
      this.calculateSummaryPosition();
    },

    // 统一数据加载方法
    async loadData(isFirstLoad = false) {
      if (!isFirstLoad && !this.hasMoreData) return;

      try {
        // 页码管理
        if (isFirstLoad) {
          this.page = 1;
          this.allData = [];
        } else {
          this.page += 1;
        }

        const params = {
          no: this.page,
          limit: 10,
          timeType:
            this.currentTab === "time"
              ? this.getTimeTypeValue(this.dateType)
              : this.getTimeTypeValue("custom")
        };

        // 只有当timeType为6（自定义）时才传递startTime和endTime参数
        const timeTypeValue =
          this.currentTab === "time"
            ? this.getTimeTypeValue(this.dateType)
            : this.getTimeTypeValue("custom");

        if (timeTypeValue === 6) {
          params.startTime = this.customStartDate
            ? this.formatDateToHyphen(this.customStartDate)
            : "";
          params.endTime = this.customEndDate
            ? this.formatDateToHyphen(this.customEndDate)
            : "";
        }

        this.pageLoading = true;
        const res = await getPaymentDetailForH5(params);

        if (res && res.result) {
          // 处理列表数据
          const data = res.result.records || [];
          this.allData = isFirstLoad ? data : [...this.allData, ...data];
          
          // 判断是否还有更多数据
          const totalCount = res.result.total || 0;
          this.hasMoreData = this.allData.length < totalCount;
          
          // 处理汇总数据（第一次加载时获取）
          if (isFirstLoad && res) {
            this.totalData = {
              repaymentAmount: res.repaymentAmount || 0,
              grossProfit: res.grossProfit || 0
            };
          }
        } else {
          this.hasMoreData = false;
        }
      } catch (error) {
        console.error("数据加载异常:", error);
        this.hasMoreData = false;
      } finally {
        this.pageLoading = false;
        // 确保totalData有默认值
        if (!this.totalData || Object.keys(this.totalData).length === 0) {
          this.totalData = {
            repaymentAmount: 0,
            grossProfit: 0
          };
        }
      }
    },



    // 将自定义日期格式转为带连字符的格式：YYYY-MM-DD
    formatDateToHyphen(dateStr) {
      if (!dateStr) return "";

      const regex = /(\d{4})年(\d{2})月(\d{2})日/;
      const match = dateStr.match(regex);

      if (match) {
        const year = match[1];
        const month = match[2];
        const day = match[3];
        return `${year}-${month}-${day}`;
      }

      return "";
    },

    onClickLeft() {
      this.backToApp();
    },

    handleRowClick(item) {
      // 如果是汇总行，不跳转
      if (item.username === "汇总") {
        return;
      }
    },

    getDateTypeLabel() {
      const found = this.dateTypeOptions.find(
        item => item.value === this.dateType
      );
      return found ? found.label : "今天";
    },

    handleTabClick(tab) {
      if (tab === "time") {
        // 如果当前已经是时间选择模式，直接打开弹窗
        if (this.currentTab === "time") {
          this.showDatePopup = true;
          return;
        }
        
        // 切换到时间选择，清空自定义日期
        this.currentTab = tab;
        this.customStartDate = "";
        this.customEndDate = "";
        this.isEndDateEnabled = false;
        this.activeInput = "";
        this.isPickerVisible = false;
        this.selectedDate = null;
        this.showDatePopup = true;
      } else {
        // 如果当前已经是自定义模式，直接打开弹窗
        if (this.currentTab === "custom") {
          this.showCustomDatePopup = true;
          this.isPickerVisible = false;
          return;
        }
        
        // 切换到自定义日期，重置时间选择
        this.currentTab = tab;
        this.dateType = "today";
        this.showDatePopup = false;
        this.showCustomDatePopup = true;
        this.isPickerVisible = false;
      }
    },

    async selectDateType(type) {
      // 清除之前的防抖定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      this.dateType = type;
      this.showDatePopup = false;
      this.currentTab = "time";

      // 清除自定义日期相关状态，确保状态隔离
      this.customStartDate = "";
      this.customEndDate = "";
      this.isEndDateEnabled = false;
      this.activeInput = "";
      this.isPickerVisible = false;
      this.selectedDate = null;
      this.showCustomDatePopup = false;

      // 防抖处理，避免快速切换时重复请求
      this.debounceTimer = setTimeout(async () => {
        this.pageLoading = true;
        try {
          await this.initData();
        } finally {
          this.pageLoading = false;
        }
      }, 300);
    },

    // 处理日期输入框点击
    handleDateInputClick(mode) {
      if (mode === "end" && !this.isEndDateEnabled) {
        // 如果点击结束日期，但尚未选择开始日期，则提示并不做处理
        this.$toast("请先选择开始日期");
        return;
      }

      // 设置当前激活的输入框
      this.activeInput = mode;

      this.datePickerMode = mode;
      this.isPickerVisible = true;

      // 设置初始选择日期
      if (mode === "start" && this.customStartDate) {
        this.selectedDate = this.parseCustomDate(this.customStartDate);
      } else if (mode === "end" && this.customEndDate) {
        this.selectedDate = this.parseCustomDate(this.customEndDate);
      } else {
        // 默认显示当前日期
        this.selectedDate = new Date();

        // 如果是选择结束日期，确保默认日期不小于开始日期
        if (mode === "end" && this.customStartDate) {
          const startDate = this.parseCustomDate(this.customStartDate);
          if (this.selectedDate < startDate) {
            this.selectedDate = new Date(startDate);
          }
        }
      }
    },

    // 解析自定义格式的日期字符串
    parseCustomDate(dateStr) {
      if (!dateStr) return new Date();

      const regex = /(\d{4})年(\d{2})月(\d{2})日/;
      const match = dateStr.match(regex);

      if (match) {
        const year = parseInt(match[1]);
        const month = parseInt(match[2]) - 1;
        const day = parseInt(match[3]);
        return new Date(year, month, day);
      }

      return new Date();
    },

    // 获取当前选择年月的天数
    getDaysInMonth() {
      if (!this.selectedDate) return 31;

      const year = this.selectedDate.getFullYear();
      const month = this.selectedDate.getMonth();
      // 下个月的第0天就是当前月的最后一天
      const lastDay = new Date(year, month + 1, 0).getDate();

      return lastDay;
    },

    selectYear(year) {
      if (!this.selectedDate) {
        this.selectedDate = new Date();
      }
      const newDate = new Date(this.selectedDate);
      newDate.setFullYear(year);

      // 如果是选择结束日期，确保不小于开始日期
      if (this.datePickerMode === "end" && this.customStartDate) {
        const startDate = this.parseCustomDate(this.customStartDate);
        if (newDate < startDate) {
          newDate.setTime(startDate.getTime());
        }
      }

      this.selectedDate = newDate;
      this.updateCustomDate();
    },

    selectMonth(month) {
      if (!this.selectedDate) {
        this.selectedDate = new Date();
      }

      const newDate = new Date(this.selectedDate);
      newDate.setMonth(month - 1);

      // 如果是选择结束日期，确保不小于开始日期
      if (this.datePickerMode === "end" && this.customStartDate) {
        const startDate = this.parseCustomDate(this.customStartDate);
        const currentYear = newDate.getFullYear();
        const startYear = startDate.getFullYear();

        if (currentYear === startYear && month < startDate.getMonth() + 1) {
          newDate.setMonth(startDate.getMonth());
          newDate.setDate(startDate.getDate());
          this.selectedDate = newDate;
          this.updateCustomDate();
          return;
        }
      }

      // 处理月份变化时可能导致的日期溢出
      const daysInNewMonth = new Date(
        newDate.getFullYear(),
        month,
        0
      ).getDate();
      if (newDate.getDate() > daysInNewMonth) {
        newDate.setDate(daysInNewMonth);
      }

      this.selectedDate = newDate;
      this.updateCustomDate();
    },

    selectDay(day) {
      if (!this.selectedDate) {
        this.selectedDate = new Date();
      }

      const newDate = new Date(this.selectedDate);

      // 如果是选择结束日期，确保不小于开始日期
      if (this.datePickerMode === "end" && this.customStartDate) {
        const startDate = this.parseCustomDate(this.customStartDate);
        const currentYear = newDate.getFullYear();
        const currentMonth = newDate.getMonth();
        const startYear = startDate.getFullYear();
        const startMonth = startDate.getMonth();

        if (currentYear === startYear && currentMonth === startMonth) {
          // 同年同月情况下，确保日期不小于开始日期的日期
          if (day < startDate.getDate()) {
            newDate.setDate(startDate.getDate());
            this.selectedDate = newDate;
            this.updateCustomDate();
            return;
          }
        }
      }

      newDate.setDate(day);
      this.selectedDate = newDate;
      this.updateCustomDate();
    },

    updateCustomDate() {
      if (!this.selectedDate) return;

      const date = this.selectedDate;
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const formattedDate = `${year}年${month
        .toString()
        .padStart(2, "0")}月${day.toString().padStart(2, "0")}日`;

      if (this.datePickerMode === "start") {
        this.customStartDate = formattedDate;
        // 启用结束日期选择
        this.isEndDateEnabled = true;

        // 如果已经有结束日期，且结束日期小于新的开始日期，则清空结束日期
        if (this.customEndDate) {
          const endDate = this.parseCustomDate(this.customEndDate);
          if (endDate < this.selectedDate) {
            this.customEndDate = "";
          }
        }
      } else {
        this.customEndDate = formattedDate;
      }
    },

    // 清除激活状态
    clearActiveInput() {
      this.activeInput = "";
    },

    async confirmCustomDate() {
      // 检查是否有选择日期，如果没有则不做任何操作
      if (!this.customStartDate) {
        this.$toast("请选择开始日期");
        return;
      }

      // 确保结束日期存在，如果不存在则使用开始日期
      if (!this.customEndDate) {
        this.customEndDate = this.customStartDate;
      }

      // 清除之前的防抖定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      // 清除时间选择相关状态，确保状态隔离
      // 注意：不重置dateType，保持当前选择状态用于显示

      // 清除激活状态
      this.clearActiveInput();

      // 隐藏日期选择器
      this.isPickerVisible = false;
      this.showCustomDatePopup = false;
      this.currentTab = "custom";

      // 防抖处理，避免快速切换时重复请求
      this.debounceTimer = setTimeout(async () => {
        this.pageLoading = true;
        try {
          await this.initData();
        } finally {
          this.pageLoading = false;
        }
      }, 300);
    },

    handleSort() {
      // 新接口不支持排序，暂时禁用排序功能
      this.$toast("暂不支持排序功能");
      return;
    },

    // 计算汇总行定位方式
    calculateSummaryPosition() {
      this.$nextTick(() => {
        const tableWrapper = this.$refs.tableWrapper;
        if (!tableWrapper) return;

        // 获取容器高度和内容高度
        const containerHeight = tableWrapper.clientHeight;
        const contentHeight = tableWrapper.scrollHeight;

        // 如果内容高度大于容器高度，说明需要滚动，汇总行应该固定在底部
        // 否则汇总行应该正常流式布局
        if (contentHeight > containerHeight) {
          this.summaryPositionClass = "summary-sticky";
        } else {
          this.summaryPositionClass = "summary-normal";
        }
      });
    },

    // 监听窗口大小变化
    handleResize() {
      this.calculateSummaryPosition();
    },

    // 自动填充容器
    async autoFillContainer() {
      if (this.isAutoLoading) return;
      
      this.isAutoLoading = true;
      
      try {
        // 最多自动加载5页，防止无限循环
        let autoLoadCount = 0;
        const maxAutoLoad = 5;
        
        while (this.hasMoreData && autoLoadCount < maxAutoLoad) {
          await this.$nextTick();
          
          const tableWrapper = this.$refs.tableWrapper;
          if (!tableWrapper) break;
          
          const containerHeight = tableWrapper.clientHeight;
          const contentHeight = tableWrapper.scrollHeight;
          
          // 如果内容高度小于等于容器高度，说明需要加载更多数据填充容器
          if (contentHeight <= containerHeight + 100) { // 100px的缓冲区
            await this.loadData(false);
            autoLoadCount++;
          } else {
            break;
          }
        }
      } finally {
        this.isAutoLoading = false;
      }
    },

    // 处理滚动事件
    handleScroll() {
      if (this.scrollLoadDebounce) {
        clearTimeout(this.scrollLoadDebounce);
      }
      
      this.scrollLoadDebounce = setTimeout(() => {
        this.checkScrollLoad();
      }, 200);
    },

    // 检查是否需要滚动加载
    async checkScrollLoad() {
      if (this.pageLoading || !this.hasMoreData || this.isAutoLoading) {
        return;
      }

      const tableWrapper = this.$refs.tableWrapper;
      if (!tableWrapper) return;

      const scrollTop = tableWrapper.scrollTop;
      const scrollHeight = tableWrapper.scrollHeight;
      const clientHeight = tableWrapper.clientHeight;

      // 滚动到底部前50px时开始加载
      if (scrollTop + clientHeight >= scrollHeight - 50) {
        await this.loadData(false);
      }
    },


  }
};
</script>

<style lang="scss" scoped>
.contribution-repayment-detail {
  background-color: #f5f7f9;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    flex: 0 0 auto;
    background: #fff;
  }

  .section-title {
    padding: 0 16px;
    height: 48px;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    font-size: 16px;
    color: #000000;
    line-height: 48px;
  }

  .personal-data {
    background: #fff;
  }

  .filter-tabs {
    padding: 0 16px 16px;

    .tab-wrapper {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    .tab-item {
      display: flex;
      align-items: center;
      padding: 5px 15px;
      font-size: 14px;
      color: #323233;
      background: #f7f8fa;
      border-radius: 20px;
      cursor: pointer;
      border: 1px solid transparent;

      &.active {
        color: #ff9900;
        border: 1px solid #ff9900;
        background: rgba(255, 153, 0, 0.05);
      }

      .van-icon {
        margin-left: 5px;
      }
    }
  }

  .table-container {
    background: #fff;
    flex: 1;
    overflow: hidden;
    position: relative;

    .table-wrapper {
      height: 100%;
      overflow-x: auto;
      overflow-y: auto;
      max-height: calc(100vh - 200px);

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .loading-status {
      padding: 16px;
      text-align: center;
      
      .loading-text {
        color: #969799;
        font-size: 14px;
      }
      
      .finished-text {
        color: #969799;
        font-size: 14px;
      }
    }

    .empty-state-container {
      padding: 60px 0;
      text-align: center;
    }

    .data-table {
      width: 100%;
      min-width: 750px;
      border-collapse: collapse;
      table-layout: fixed;

      .col-username {
        width: 120px;
      }

      .col-month {
        width: 100px;
      }

      .col-contract {
        width: 150px;
      }

      .col-percentage {
        width: 80px;
      }

      .col-amount {
        width: 100px;
      }

      .col-margin {
        width: 100px;
      }

      .table-header {
        th {
          background: #f9f9f9;
          border: 1px solid #ebedf0;
          padding: 12px 8px;
          text-align: center;
          font-size: 14px;
          font-weight: 500;
          color: #333;
          position: sticky;
          top: -1px;
          z-index: 10;
        }
      }

      .table-body {
        .table-row {
          cursor: pointer;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: #f8f9fa;
          }

          &:active {
            background-color: #e8f4ff;
          }

          td {
            border: 1px solid #f2f3f5;
            padding: 12px 8px;
            text-align: center;
            font-size: 14px;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            &.col-username {
              color: #333;
              font-weight: 500;
            }
          }
        }
      }
    }

    // 汇总行样式
    .summary-container {
      background: #fff;
      border-top: 2px solid #ebedf0;

      // 正常流式布局 - 数据少时贴着表格底部
      &.summary-normal {
        position: relative;
      }

      // 固定底部布局 - 数据多时固定在容器底部
      &.summary-sticky {
        position: sticky;
        bottom: -1px;
        z-index: 5;
      }

      .summary-table {
        width: 100%;
        min-width: 750px;
        border-collapse: collapse;
        table-layout: fixed;

        // 复用相同的列宽定义
        .col-username {
          width: 120px;
        }

        .col-month {
          width: 100px;
        }

        .col-contract {
          width: 150px;
        }

        .col-percentage {
          width: 80px;
        }

        .col-amount {
          width: 100px;
        }

        .col-margin {
          width: 100px;
        }

        .summary-row {
          background: #f9f9f9;

          td {
            border: 1px solid #ebedf0;
            padding: 12px 8px;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            &.col-username {
              text-align: center;
            }
          }
        }
      }
    }
  }

  // 弹窗样式
  .date-popup {
    padding: 0;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;

    .popup-header {
      position: relative;
      padding: 20px 0;
    }

    .date-popup-title {
      text-align: center;
      font-size: 16px;
      font-weight: 500;
    }

    .close-icon {
      position: absolute;
      top: 20px;
      right: 16px;
      font-size: 16px;
      color: #323233;
    }

    .date-type-tabs {
      display: flex;
      flex-wrap: wrap;
      padding: 0 16px;

      .date-type-item {
        width: calc(25% - 12px);
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 6px 12px;
        border-radius: 4px;
        font-size: 14px;
        background: #f2f3f5;
        color: #323233;

        &.active {
          background-color: #ff9900;
          color: #fff;
        }
      }
    }
  }

  .custom-date-popup {
    padding: 0;
    position: relative;
    display: flex;
    flex-direction: column;
    background: #fff;
    overflow-x: hidden;

    .popup-header {
      position: relative;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #ebedf0;
    }

    .date-popup-title {
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      color: #000;
    }

    .close-icon {
      position: absolute;
      top: 50%;
      right: 16px;
      transform: translateY(-50%);
      font-size: 18px;
      color: #323233;
      padding: 8px;
      margin-right: -8px;
    }

    .date-input-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      margin-bottom: 0;
      background: #fff;

      .date-input {
        flex: 1;
        max-width: 155px;
        height: 36px;

        .custom-date-input {
          width: 100%;
          height: 36px;
          padding: 0;
          border: 1px solid #dcdcdc;
          border-radius: 4px;
          font-size: 14px;
          color: #333;
          background: #fff;
          text-align: center;
          outline: none;
          box-sizing: border-box;

          &.active {
            border-color: #ff9900;
          }

          &.selected {
            color: #ff9900;
          }

          &.disabled {
            opacity: 0.6;
            pointer-events: none;
            background-color: #f5f5f5;
            border: 1px solid #dcdcdc;
            color: #999;
          }

          &::placeholder {
            color: #999;
          }
        }
      }

      .date-separator {
        padding: 0 8px;
        color: #969799;
        flex: 0 0 auto;
      }
    }

    .date-picker-container {
      display: flex;
      justify-content: space-between;
      height: 250px;
      margin: 0;
      border-top: 1px solid #f2f3f5;
      overflow-x: hidden;

      .date-picker-column {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
        text-align: center;

        &::-webkit-scrollbar {
          display: none;
        }

        &:not(:last-child) {
          border-right: 1px solid #f2f3f5;
        }

        .date-picker-item {
          height: 44px;
          line-height: 44px;
          color: #999;
          font-size: 14px;
          padding: 0 4px;
          margin: 0 auto;
          width: 80%;

          &.active {
            color: #333;
            font-weight: 500;
            position: relative;

            &:after {
              content: "";
              position: absolute;
              left: 50%;
              bottom: 7px;
              width: 4px;
              height: 4px;
              background-color: #ff9900;
              border-radius: 50%;
              transform: translateX(-50%);
            }
          }
        }
      }
    }

    .date-confirm-btn {
      margin: 16px;

      :deep(.van-button) {
        height: 44px;
        background: #ff9900;
        border-color: #ff9900;
        font-size: 16px;
        border-radius: 4px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        letter-spacing: 2px;

        &:active {
          background: #e68a00;
          border-color: #e68a00;
        }
      }
    }
  }
}

::v-deep .van-nav-bar .van-icon {
  color: #333;
  font-size: 18px;
}
</style>

