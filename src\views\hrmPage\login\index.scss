.login-page {
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-image: url("../../../assets/hrm/bg.png");
  background-size: 100%;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;

  header {
    height: 105px;

    img {
      width: 100%;
      height: 100%;
    }
  }
  main {
    flex: 1;
    padding: 17px 28px 0 28px;
    margin: 0 auto;

    .title {
      .title-solt {
        // width: 168px;
        // height: 80px;
        margin-bottom: 9px;
        font-family: PingFangSC-S0pxibold;
        font-weight: 530;
        font-size: 28px;
        color: #ffffff;
      }
      .title-txt {
        width: 256px;
        height: 22px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 24px;
      }
    }
    .w-input {
      margin-bottom: 48px;
      /deep/.van-button--small {
        border: none;
        height: 8.53333vw;
        padding: 0 2.13333vw;
        font-size: 3.2vw;
        background-color: transparent;
        font-size: 14px;
        color: #ff9900;
      }

      //   修改下划线样式 前
      /deep/.van-cell::after {
        position: absolute;
        box-sizing: border-box;
        content: " ";
        pointer-events: none;
        right: 4.26667vw;
        bottom: 0;
        left: 4.26667vw;
        border-bottom: 1px solid #5d45e2;
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
      }
      //   修改下划线样式 后
      /deep/.van-cell::before {
        position: absolute;
        box-sizing: border-box;
        content: " ";
        pointer-events: none;
        right: 4.26667vw;
        bottom: 0;
        left: 4.26667vw;
        border-bottom: 1px solid #5d45e2;
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
      }
    }

    .submit {
      width: 320px;
      height: 40px;
      background: #ff9900;
      border-radius: 24px;
      border: none;
      margin: 0 auto;
      margin-bottom: 8px;
    }

    .notice {
      width: 185px;
      height: 17px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  footer {
    height: 189px;

    .img-box {
      width: 100%;
      height: 100%;
      margin-top: 30px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}

/deep/.van-cell-group {
  background-color: transparent;
}

/deep/.van-cell {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  box-sizing: border-box;
  width: 100%;
  padding: 2.66667vw 4.26667vw;
  overflow: hidden;
  color: #323233;
  font-size: 3.73333vw;
  line-height: 6.4vw;
  background-color: transparent;
}

/deep/.van-field__control {
  display: block;
  box-sizing: border-box;
  width: 100%;
  min-width: 0;
  margin: 0;
  padding: 0;
  color: #fff;
  line-height: inherit;
  text-align: left;
  background-color: transparent;
  border: 0;
  resize: none;
  font-size: 14px;
}
