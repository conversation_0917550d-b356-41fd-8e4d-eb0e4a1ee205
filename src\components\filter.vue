<template>
  <van-popup
    v-model="showPopup"
    position="bottom"
    :style="{ height: '100%', width: '100%' }"
  >
    <van-nav-bar
      title="筛选"
      left-arrow
      @click-left="onClickLeft"
      :border="false"
    />
    <div class="form-item" v-if="type == 'water'">
      <div class="form-item-name">
        <label class="label">对方户名</label>
      </div>
      <div class="form-item-content">
        <van-field
          v-model="oppositeAccountName"
          class="field"
          placeholder="请输入"
        ></van-field>
      </div>
    </div>
    <div class="form-item" v-if="type == 'water'">
      <div class="form-item-name">
        <label class="label">客户名称</label>
      </div>
      <div class="form-item-content">
        <van-field
          v-model="customerName"
          class="field"
          placeholder="请输入"
        ></van-field>
      </div>
    </div>
    <div class="form-item" v-if="type == 'water'">
      <div class="form-item-name">
        <label class="label">对方账号</label>
      </div>
      <div class="form-item-content">
        <van-field
          v-model="oppositeBankAccount"
          class="field"
          placeholder="请输入"
        ></van-field>
      </div>
    </div>
    <div class="form-item" v-if="type == 'water'">
      <div class="form-item-name">
        <label class="label">备注</label>
      </div>
      <div class="form-item-content">
        <van-field
          v-model="mobileRemark"
          class="field"
          placeholder="请输入"
        ></van-field>
      </div>
    </div>
    <div class="form-item" v-if="type == 'water'">
      <div class="form-item-name">
        <label class="label">金额</label>
      </div>
      <div class="form-item-content">
        <div class="range">
          <van-field
            v-model="amountStart"
            class="field"
            type="number"
            placeholder="最低"
          ></van-field>
          <span class="seperator">-</span>
          <van-field
            v-model="amountEnd"
            class="field"
            type="number"
            placeholder="最高"
          ></van-field>
        </div>
      </div>
    </div>
    <div class="form-item" v-if="type == 'water'">
      <div class="form-item-name">
        <label class="label">支付状态</label>
      </div>
      <div class="form-item-content">
        <div class="status">
          <div
            class="btn"
            :class="{ active: payAllChecked }"
            @click="togglePay"
          >
            全部
          </div>
          <div
            class="btn"
            v-for="item in statusList"
            :key="item.value"
            @click="handleCheck(item)"
            :class="{ active: waterStatus.includes(item.value) }"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </div>
    <div class="form-item" v-if="type == 'water'">
      <div class="form-item-name">
        <label class="label">回款日期</label>
      </div>
      <div class="form-item-content">
        <div class="range">
          <van-field
            v-model="payStartTime"
            class="field"
            @click="openTimePicker('payStartTime')"
            placeholder="开始"
          />
          <span class="seperator">-</span>
          <van-field
            v-model="payEndTime"
            class="field"
            @click="openTimePicker('payEndTime')"
            placeholder="结束"
          />
        </div>
      </div>
    </div>
    <div class="form-item" v-if="type == 'water'" style="margin-bottom: 52px;">
      <div class="form-item-name">
        <label class="label">关联合同</label>
      </div>
      <div class="form-item-content">
        <div class="status">
          <div
            class="btn"
            :class="{ active: relatedAllChecked }"
            @click="toggleAllRelate"
          >
            全部
          </div>
          <div
            class="btn"
            v-for="item in conStatusList"
            :key="item.value"
            @click="handleStatusChange(item)"
            :class="{ active: conStatus.includes(item.value) }"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </div>
    <div class="form-item" v-if="type == 'contract'">
      <div class="form-item-name">
        <label class="label">关键字</label>
      </div>
      <div class="form-item-content">
        <van-field
          v-model="keyWord"
          class="field"
          placeholder="请输入"
        ></van-field>
      </div>
    </div>
    <div class="form-item" v-if="type == 'contract'">
      <div class="form-item-name">
        <label class="label">销售备注</label>
      </div>
      <div class="form-item-content">
        <van-field
          v-model="projectRemark"
          class="field"
          placeholder="请输入"
        ></van-field>
      </div>
    </div>
    <div class="form-item" v-if="type == 'contract'">
      <div class="form-item-name">
        <label class="label">回款状态</label>
      </div>
      <div class="form-item-content">
        <div class="status">
          <div
            class="btn"
            :class="{ active: refundStatusAllChecked }"
            @click="toggleRefundStatus"
          >
            全部
          </div>
          <div
            class="btn"
            v-for="item in refundList"
            :key="item.value"
            @click="handleRefundChange(item)"
            :class="{ active: refundStatus.includes(item.value) }"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </div>
    <div class="form-item" v-if="type == 'contract'">
      <div class="form-item-name">
        <label class="label">合同总额</label>
      </div>
      <div class="form-item-content">
        <div class="range">
          <van-field
            v-model="priceStart"
            class="field"
            type="number"
            placeholder="最低"
          ></van-field>
          <span class="seperator">-</span>
          <van-field
            v-model="priceEnd"
            class="field"
            type="number"
            placeholder="最高"
          ></van-field>
        </div>
      </div>
    </div>
    <div class="form-item" v-if="type == 'contract'">
      <div class="form-item-name">
        <label class="label">欠款总额</label>
      </div>
      <div class="form-item-content">
        <div class="range">
          <van-field
            v-model="arrearsAmountStart"
            class="field"
            type="number"
            placeholder="最低"
          ></van-field>
          <span class="seperator">-</span>
          <van-field
            v-model="arrearsAmountEnd"
            class="field"
            type="number"
            placeholder="最高"
          ></van-field>
        </div>
      </div>
    </div>
    <div class="opt">
      <van-button class="reset" @click="reset">重置</van-button>
      <van-button class="sure" @click="makeSure">确定</van-button>
    </div>
    <van-popup
      v-model="showTimePicker"
      round
      position="bottom"
      :style="{ height: '30%' }"
    >
      <van-datetime-picker
        type="date"
        title="选择年月日"
        @confirm="handleDateChange"
        @cancel="showTimePicker = false"
      />
    </van-popup>
  </van-popup>
</template>
<script>
import { Popup, NavBar, Button, Field, DatetimePicker } from "vant";
export default {
  components: {
    [Popup.name]: Popup,
    [NavBar.name]: NavBar,
    [Button.name]: Button,
    [Field.name]: Field,
    [DatetimePicker.name]: DatetimePicker
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: "water"
    }
  },
  watch: {
    value(val) {
      this.showPopup = val;
    },
    showPopup(val) {
      this.$emit("input", val);
    }
  },
  data() {
    return {
      showPopup: false,
      showTimePicker: false,
      oppositeBankAccount: "",
      oppositeAccountName: "",
      customerName: "",
      mobileRemark: "",
      amountStart: "",
      amountEnd: "",
      priceStart: "",
      priceEnd: "",
      arrearsAmountStart: "",
      arrearsAmountEnd: "",
      payStartTime: "",
      payEndTime: "",
      waterStatus: ["1"],
      conStatus: ["2", "3"],
      refundStatus: ["1"],
      payAllChecked: false,
      relatedAllChecked: false,
      refundStatusAllChecked: false,
      statusList: [
        { label: "已支付", value: "1" },
        { label: "未支付", value: "0" }
      ],
      conStatusList: [
        { label: "全额关联", value: "1" },
        { label: "未关联", value: "2" },
        { label: "待审批", value: "3" },
        { label: "已审批", value: "4" },
        { label: "驳回", value: "5" }
      ],
      refundList: [
        { label: "未回款", value: "1" },
        { label: "回款中", value: "2" },
        { label: "已回款", value: "3" }
      ],
      dateType: "",
      keyWord: "",
      projectRemark: ""
    };
  },
  created() {
    this.payStartTime = this.$moment()
      .subtract(1, "months")
      .format("YYYY-MM-DD");
    this.payEndTime = this.$moment().format("YYYY-MM-DD");
  },
  methods: {
    onClickLeft() {
      this.showPopup = false;
    },
    togglePay() {
      this.payAllChecked = !this.payAllChecked;
      if (this.payAllChecked) {
        this.waterStatus = this.statusList.map(v => v.value);
      } else {
        this.waterStatus = [];
      }
    },
    toggleAllRelate() {
      this.relatedAllChecked = !this.relatedAllChecked;
      if (this.relatedAllChecked) {
        this.conStatus = this.conStatusList.map(v => v.value);
      } else {
        this.conStatus = [];
      }
    },
    toggleRefundStatus() {
      this.refundStatusAllChecked = !this.refundStatusAllChecked;
      if (this.refundStatusAllChecked) {
        this.refundStatus = this.refundList.map(v => v.value);
      } else {
        this.refundStatus = [];
      }
    },
    handleDateChange(value) {
      this.dateType == "payStartTime"
        ? (this.payStartTime = this.$moment(value).format("YYYY-MM-DD"))
        : (this.payEndTime = this.$moment(value).format("YYYY-MM-DD"));
      this.showTimePicker = false;
    },
    handleCheck(item) {
      if (this.waterStatus.includes(item.value)) {
        this.waterStatus = this.waterStatus.filter(v => v != item.value);
      } else {
        this.waterStatus.push(item.value);
      }
      // 判断是否全选
      if (this.waterStatus.length == this.statusList.length) {
        this.payAllChecked = true;
      } else {
        this.payAllChecked = false;
      }
    },
    handleStatusChange(item) {
      if (this.conStatus.includes(item.value)) {
        this.conStatus = this.conStatus.filter(v => v != item.value);
      } else {
        this.conStatus.push(item.value);
      }
      // 判断是否全选
      if (this.conStatus.length == this.conStatusList.length) {
        this.relatedAllChecked = true;
      } else {
        this.relatedAllChecked = false;
      }
    },
    handleRefundChange(item) {
      if (this.refundStatus.includes(item.value)) {
        this.refundStatus = this.refundStatus.filter(v => v != item.value);
      } else {
        this.refundStatus.push(item.value);
      }
      // 判断是否全选
      if (this.refundStatus.length == this.refundList.length) {
        this.refundStatusAllChecked = true;
      } else {
        this.refundStatusAllChecked = false;
      }
    },
    openTimePicker(type) {
      this.dateType = type;
      this.showTimePicker = true;
    },
    reset() {
      if (this.type == "water") {
        this.oppositeBankAccount = "";
        this.oppositeAccountName = "";
        this.customerName = "";
        this.mobileRemark = "";
        this.amountStart = "";
        this.amountEnd = "";
        this.waterStatus = ["1"];
        this.conStatus = ["2", "3"];
        this.payStartTime = this.$moment()
          .subtract(1, "months")
          .format("YYYY-MM-DD");
        this.payEndTime = this.$moment().format("YYYY-MM-DD");
        // 清掉全选按钮状态
        this.payAllChecked = false;
        this.relatedAllChecked = false;
      } else {
        this.keyWord = "";
        this.projectRemark = "";
        this.refundStatus = [];
        this.priceStart = "";
        this.priceEnd = "";
        this.arrearsAmountStart = "";
        this.arrearsAmountEnd = "";
      }
      this.showPopup = false;
      this.$emit("filter");
    },
    makeSure() {
      this.showPopup = false;
      this.$emit("filter");
    },
    getComponentParams() {
      let params = {};
      if (this.type == "water") {
        params = {
          oppositeBankAccount: this.oppositeBankAccount,
          oppositeAccountName: this.oppositeAccountName,
          customerName: this.customerName,
          mobileRemark: this.mobileRemark,
          amountStart: this.amountStart,
          amountEnd: this.amountEnd,
          payStatuses: this.waterStatus.join(","),
          payStartTime: this.payStartTime,
          payEndTime: this.payEndTime,
          relatedStatus: this.conStatus.join(",")
        };
      } else {
        params = {
          keyWord: this.keyWord,
          projectRemark: this.projectRemark,
          paymentStatus: this.refundStatus.join(","),
          priceStart: this.priceStart,
          priceEnd: this.priceEnd,
          arrearsAmountStart: this.arrearsAmountStart,
          arrearsAmountEnd: this.arrearsAmountEnd
        };
      }
      return params;
    }
  }
};
</script>

<style lang="scss" scoped>
.title {
  height: 44px;
  text-align: center;
  line-height: 44px;
}
.opt {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 52px;
  background-color: #fff;
  border-top: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 52px;
  .reset {
    padding: 0;
    width: 120px;
    height: 44px;
    background: #ffffff;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    text-align: center;
    line-height: 44px;
    margin-right: 8px;
  }
  .sure {
    padding: 0;
    width: 215px;
    height: 44px;
    background: #ff9900;
    border-radius: 8px;
    font-size: 16px;
    color: #ffffff;
    text-align: center;
    line-height: 44px;
  }
}
.form-item {
  background-color: #fff;
  margin-bottom: 8px;
  .form-item-name {
    height: 44px;
    line-height: 44px;
    padding: 0 16px;
    .label {
      font-size: 14px;
      color: #000000;
      font-weight: bold;
    }
  }
  .form-item-content {
    padding: 0 16px;
    .field {
      background: #f7f7f7;
      border-radius: 6px;
      height: 40px;
    }
    .range {
      display: flex;
      align-items: center;
      justify-content: space-around;
      .field {
        height: 40px;
        background: #f7f7f7;
        border-radius: 6px;
        width: 160px;
      }
    }
    .status {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .btn {
        width: 100px;
        height: 40px;
        background: #f7f7f7;
        border-radius: 6px;
        font-size: 16px;
        color: #7f7f7f;
        text-align: center;
        line-height: 40px;
        margin-right: 12px;
        margin-bottom: 12px;
        &.active {
          background: #ff99001a;
          color: #ff9900;
        }
      }
    }
  }
}
</style>
