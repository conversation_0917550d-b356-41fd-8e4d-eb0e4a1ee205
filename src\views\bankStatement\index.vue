<template>
  <div class="bank-statement">
    <van-sticky>
      <van-nav-bar
        title="银行流水"
        left-arrow
        @click-left="onClickLeft"
        @click-right="onClickRight"
      >
        <template #right>
          <van-icon
            name="https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/07/09/<EMAIL>"
            size="20"
          />
        </template>
      </van-nav-bar>
    </van-sticky>

    <div class="bank-container">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="getBankList"
        >
          <div
            class="statement"
            v-for="item in bankStatementList"
            :key="item.id"
          >
            <div class="bank-title">
              <div>
                <div class="name">{{ item.oppositeAccountName }}</div>
                <div class="account">
                  对方账号：{{ item.oppositeBankAccount }}
                </div>
              </div>
              <div class="btn-wrap">
                <van-button class="relative" @click="goRelated(item)"
                  >关联</van-button
                >
                <van-button
                  class="relative"
                  @click="clearCustomerId(item)"
                  v-show="item.customerName"
                  >清空</van-button
                >
              </div>
            </div>
            <div class="line"></div>
            <div class="bank-content">
              <div class="item">
                <span class="label">客户名称</span>
                <span class="value">{{ item.customerName || "-" }}</span>
              </div>
              <div class="item">
                <span class="label">支付状态</span>
                <span class="value">{{
                  item.payStatus == "1" ? "已支付" : "未支付"
                }}</span>
              </div>
              <div class="item">
                <span class="label money">金额/已关联金额/待审批金额</span>
                <span class="value"
                  >{{ item.amount }}/{{ item.alreadyRelatedAmount }}/{{
                    item.pendingAmount
                  }}</span
                >
              </div>
              <div class="item">
                <span class="label">创建人</span>
                <span class="value">{{ item.createUsername }}</span>
              </div>
              <div class="item">
                <span class="label">回款时间</span>
                <span class="value">{{ item.payTime }}</span>
              </div>
              <div class="item">
                <span class="label">备注</span>
                <span class="value">{{ item.mobileRemark }}</span>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <filter-dialog v-model="showFilter" ref="filter" @filter="handleFilter" />
  </div>
</template>

<script>
import { payMessageCenter, clearPayCustomer } from "../../api/water";
import {
  NavBar,
  Button,
  List,
  PullRefresh,
  Toast,
  Icon,
  Sticky,
  Dialog
} from "vant";
import filterDialog from "../../components/filter.vue";
export default {
  components: {
    [NavBar.name]: NavBar,
    [Button.name]: Button,
    [List.name]: List,
    [PullRefresh.name]: PullRefresh,
    [Toast.name]: Toast,
    [Icon.name]: Icon,
    [Dialog.name]: Dialog,
    [Sticky.name]: Sticky,
    filterDialog
  },
  data() {
    return {
      refreshing: false,
      finished: false,
      loading: false,
      page: {
        no: 1,
        limit: 20
      },
      total: 0,
      bankStatementList: [],
      showFilter: false
    };
  },
  methods: {
    onClickLeft() {
      this.backToApp();
    },
    onClickRight() {
      this.showFilter = true;
      console.log("点击了右侧按钮");
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.page.no = 1;
      this.getBankList();
    },
    async getBankList() {
      const params = this.$refs.filter.getComponentParams();
      if (this.refreshing) {
        this.bankStatementList = [];
        this.refreshing = false;
      }
      try {
        const res = await payMessageCenter({
          isPayType: 1,
          ...this.page,
          ...params
        });
        this.total = res.total || 0;
        this.bankStatementList = this.bankStatementList.concat(
          res.records || []
        );
        this.loading = false;
        if (this.bankStatementList.length >= this.total) {
          this.finished = true;
        } else {
          this.page.no++;
        }
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.data || error.result
        });
      }
    },
    handleFilter() {
      this.page.no = 1;
      this.bankStatementList = [];
      this.getBankList();
    },

    // 清空客户
    clearCustomerId(item) {
      Dialog.confirm({
        title: "",
        message: "确定清空流水对应客户信息？"
      })
        .then(async () => {
          await clearPayCustomer({
            id: item.id
          });
          Toast.success("清空成功！");
          this.handleFilter();
        })
        .catch(() => {
          // on cancel
        });
    },

    goRelated(item) {
      this.$router.push({
        name: "relatedWater",
        query: {
          id: item.id,
          customerId: item.customerId
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
