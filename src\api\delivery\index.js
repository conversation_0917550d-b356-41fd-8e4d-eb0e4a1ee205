import urls from "./config";
import { get, post, postJson } from "../request";

/**
 * 详细信息
 */
export function getJdSalReceiverModel(params = {}) {
  return get(urls.getJdSalReceiverModel, params);
}

/**
 * 物流信息
 */
export function getLogistics(params = {}) {
  return get(urls.getLogistics, params);
}

/*
 * 查询列表
 */
export function searchLogisticsProcess(params = {}) {
  return get(urls.searchLogisticsProcess, params);
}

/**
 * h5合同详情
 */
export function getContractDetailForH5(params = {}) {
  return get(urls.getContractDetailForH5 + "/" + params);
}

/*
 * 生成微信预付单
 */
export function payh5(params) {
  return post(urls.payh5, params);
}

export function contractPay(params) {
  return post(urls.contractPay, params);
}

/**验证账单是否支付 */
export function isPay(params) {
  return get(urls.isPay, params);
}

/**获取openId */
export function getOpenId(params) {
  return get(urls.getOpenId, params);
}

/** 合同扫码支付获取预下单信息*/
export function contractScanPay(params) {
  return get(urls.contractScanPay, params);
}

/**jsApi */
export function payJsApi(params) {
  return postJson(urls.payJsApi, params);
}

/*
 * 字典项
 */

export function getAllDict(params = {}) {
  return get(urls.getAllDict, params);
}

/*
 * h5账单详情
 */
export function h5BillDetail(params) {
  return get(urls.h5BillDetail, params);
}

//查询产品列表（硬软安装）
export function queryProductTypesToTree(params) {
  return get(urls.queryProductTypesToTree, params);
}

/**
 * 客户确认账单
 */
export function confirmBill(params) {
  return postJson(urls.confirmBill, params);
}

//根基手机号查询企业，门店
export function getUserByPhone(params) {
  return get(urls.getUserByPhone, params);
}

//根据id查询收款码
export function getCollectionByOrderNo(params) {
  return get(urls.getCollectionByOrderNo, params);
}

// 动态二维码（发起支付）
export function qrPay(params) {
  return postJson(urls.qrPay, params);
}

//获取微信基本信息
export function getWeChatInfo(params) {
  return get(urls.getWeChatInfo, params);
}

/**获取openId2 */
export function getOpenId2(params) {
  return get(urls.getOpenId2, params);
}

// 确认账单付款凭证
export function saveBillVoucher(params) {
  return postJson(urls.saveBillVoucher, params);
}

// 应收账单详情列表接口
export function getCountOverdueBillByUser(params) {
  return postJson(urls.getCountOverdueBillByUser, params);
}

// 应收账单下层级列表接口
export function getNextCountOverdueBillByUser(params) {
  return postJson(urls.getNextCountOverdueBillByUser, params);
}

// 取消订单
export function cancelOrder(params) {
  return get(urls.cancelOrder, params);
}

// 获取客户账户列表
export function getCustomerAccountList(params) {
  return get(urls.getCustomerAccountList, params);
}

export function getCurrentCustomer(params = {}) {
  return get(urls.getCurrentCustomer, params);
}

// 获取验证码
export function getVerifyCode(params = {}) {
  return get(urls.getVerifyCode, params);
}

// 客户账号余额扣减接口
export function payCustomerAccount(params = {}) {
  return postJson(urls.payCustomerAccount, params);
}

// 对公转账凭证ocr
export function getTicketOCR(params = {}) {
  return get(urls.getTicketOCR, params);
}

// 获取未关联流水列表(对公转账)
export function getPaySerialNewRecord(params = {}) {
  return postJson(urls.getPaySerialNewRecord, params);
}


// 回款关联合同
export function relatedContract(params) {
  return postJson(urls.relatedContract, params);
}