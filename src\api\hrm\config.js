export default {
    getStaffList: '/api/member/staff/v1/getStaffList', //查看员工列表
    getStaffCount: '/api/member/staff/v1/getStaffCount', //统计员工数量
    getStaffInfo: '/api/member/staff/v1/getStaffInfo', //查看员工详情

    getStaffRelationshipList: '/api/member/staff/v1/getStaffRelationshipList', //查询联系人
    deleteStaffRelationship: '/api/member/staff/v1/deleteStaffRelationship', //删除联系人
    updateStaffRelationship: '/api/member/staff/v1/updateStaffRelationship', //更新联系人
    saveStaffRelationship: '/api/member/staff/v1/saveStaffRelationship', //添加联系人


    getStaffBankAccountList: '/api/member/staff/v1/getStaffBankAccountList', //查询银行
    deleteStaffBankAccount: '/api/member/staff/v1/deleteStaffBankAccount', //删除银行
    saveStaffBankAccount: '/api/member/staff/v1/saveStaffBankAccount', //保存银行卡
    updateStaffBankAccount: '/api/member/staff/v1/updateStaffBankAccount', //更新银行卡
    getStaffAccountDetail: "/api/member/staff/v1/getStaffAccountDetail",


    getStaffWorkHistoryList: '/api/member/staff/v1/getStaffWorkHistoryList', //获取列表
    saveStaffWorkHistory: '/api/member/staff/v1/saveStaffWorkHistory', //新增工作经历
    updateStaffWorkHistory: '/api/member/staff/v1/updateStaffWorkHistory', //更新
    deleteStaffWorkHistory: '/api/member/staff/v1/deleteStaffWorkHistory', //删除


    getEducationList: '/api/member/education/v1/getEducationList', //查询教育经历
    saveEducation: '/api/member/education/v1/saveEducation', //新增教育经历
    updateEducation: '/api/member/education/v1/updateEducation', //更新教育经历
    getEducation: '/api/member/education/v1/getEducation', //获取教育经历详情
    deleteEducation: '/api/member/education/v1/deleteEducation', //删除教育经历

    saveStaffContract: '/api/member/staff/v1/saveStaffContract', //新增员工合同
    getStaffContractList: '/api/member/staff/v1/getStaffContractList', //查询员工合同列表
    updateStaffContract: '/api/member/staff/v1/updateStaffContract', //跟新员工合同
    deleteStaffContract: '/api/member/staff/v1/deleteStaffContract', //删除员工合同
    getStaffContractDetail: '/api/member/staff/v1/getStaffContractDetail', //合同详情


    getAttachmentInfo: '/api/member/entry/v1/getAttachmentInfo', //查看入职资料
    saveOrUpdateAttachment: '/api/member/entry/v1/saveOrUpdateAttachment', //保存更新入职资料

    

    sendToNew: '/api/member/msg/v1/sendToNew', //发送入职单
    confirmEntry: '/api/member/staff/v1/confirmEntry', // 确认入职


    saveStaff: '/api/member/staff/v1/saveStaff', //保存员工信息
    updateStaff: '/api/member/staff/v1/updateStaff', //更新员工信息


    getAllDict: '/api/member/dict/v1/getAllDict', //数据字典

    getOcrInfo:"/api/common/tool/v1/getOcrInfo", //OCR获取文字信息
    getStructAddr:"/api/common/tool/v1/getStructAddr", //获取结构化地址
    submitEntryForm:'/api/member/staff/v1/submitEntryForm',//提交入职单
    
    getSysDepartmentList:'/api/member/department/v1/getSysDepartmentList', //查询组织架构

    getDutiesPage:'/api/member/duties/v1/getDutiesPage', //职务管理列表
    saveDuties:'/api/member/duties/v1/saveDuties', //添加职务接口
    deleteDuties:'/api/member/duties/v1/deleteDuties', //删除职务
    updateDuties: '/api/member/duties/v1/updateDuties', //编辑职务

    getPostPage: "/api/member/post/v1/getPostPage", //岗位管理列表
    savePost:'/api/member/post/v1/savePost', //添加岗位接口
    updatePost:'/api/member/post/v1/updatePost',//编辑岗位
    deletePost:'/api/member/post/v1/deletePost', //删除岗位

    addSysDepartment:"/api/member/department/v1/addSysDepartment", //保存hrm组织架构
    updateSysDepartment:"/api/member/department/v1/updateSysDepartment", //更新组织架构
    deleteSysDepartment:"/api/member/department/v1/deleteSysDepartment", //删除组织架构
    getCustomerFromDept:"/api/member/department/v1/getCustomerFromDept", //查询组织架构下的员工
    addCustomerFromDept:"/api/member/staffdepartment/v1/addCustomerFromDept",//绑定组织架构与员工关系
    deleteCustomerFromDept:"/api/member/staffdepartment/v1/deleteCustomerFromDept", //删除组织架构与员工关系
    getSysDepartmentAndStaff:"/api/member/department/v1/getSysDepartmentAndStaff",//查询组织架构以及组织架构下员工
    searchCustomerInPublicStaffs:"/api/member/staffdepartment/v1/searchCustomerInPublicStaffs",//根据员工名称模糊查询
    getMemberRecord:"/api/dynamic/form/summary/v1/getMemberRecord", //根据工号获取表单列表
    exportStaffInfo:"/api/member/staff/v1/exportStaffInfo", // 导出员工档案
    getSubmitState:"/api/member/staff/v1/getSubmitState", //判断是否提交
    sendEntrySms:"/api/member/msg/v1/sendEntrySms",//发送入职单短信验证码
    verifyEntrySms:"/api/member/msg/v1/verifyEntrySms",//验证入职单短信验证码

    getAllAreaInfo: "/api/crm/system/v1/getAllAreaInfo",//获取所有地区情况

    getAllNewDict: "/api/crm/dict/v1/getAllDict",    //新的字典树
   
};