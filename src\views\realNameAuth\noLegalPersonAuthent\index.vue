<template>
  <div class="noLegalPersonAuthent">
    <tabs :isActiveB="true" :message="message"></tabs>
    <main>
      <div class="tips">实名认证，以下方式任选其一</div>
      <div>
        <van-radio-group v-model="radio">
          <van-radio name="1" checked-color="#FF9900" disabled
            >法人手机号认证</van-radio
          >
          <van-radio name="2" checked-color="#FF9900" style="margin-left: 32px"
            >授权书认证</van-radio
          >
        </van-radio-group>
        <div class="input-box">
          <item-input
            v-model.trim="phone"
            :label="'经办人手机号'"
            :placeholder="'请输入经办人手机号'"
            must
          ></item-input>
        </div>
        <div class="input-box">
          <item-input
            ref="inputVerify"
            :focusInput="'inputVerify'"
            v-model="verifyCode"
            :label="'验证码'"
            :placeholder="'请输入验证码'"
            must
          ></item-input>
          <span class="autofill" @click="sendVcode" v-if="showVerify"
            >获取验证码</span
          >
          <span class="autofill-add" v-else>重新获取({{ count }}s)</span>
        </div>
      </div>

      <div class="authorization">
        <div class="authorization-tips">企业服务授权书上传</div>
        <div class="authorization-txt">
          请下载《上上签企业服务授权书》，加盖企业公章后拍照上传
        </div>
        <div class="authorization-upload">
          <upload-file
            :isAuthorization="true"
            :max="1"
            :size="15 * 1024"
            @on-upload-success="(val) => uploadSuccess(val)"
            v-model="attachmentUrlArr"
          ></upload-file>
        </div>
      </div>

      <div class="uploadTxt">
        <div
          style="display: flex; align-items: center"
          @click="downloadAuthPdf"
        >
          <span>下载授权书</span>
          <img
            style="width: 16px; height: 16px; margin-left: 4px"
            src="https://ovopark.oss-cn-hangzhou.aliyuncs.com/front/2023/12/20/18c85c2431a10e557eeef10.png"
          />
        </div>
        <div class="uploadTxt-tips">
          授权书认证万店掌会在1个工作日内完成审核，支持png\jpeg、jpg、bmp格式且大小不超过15M，仅支持无水印或“用于上上签实名认证”水印字样的照片
        </div>
      </div>
    </main>
    <footer>
      <div class="btn-box">
        <van-button
          class="btn"
          type="default"
          :disabled="!(phone && verifyCode && attachmentUrl)"
          color="#FF9900"
          block
          @click="applyReview"
          >确认提交</van-button
        >
      </div>
    </footer>
  </div>
</template>
  
  <script>
import tabs from "../components/tabs.vue";
import itemInput from "@/components/item-input.vue";
import UploadFile from "../../../components/upload-file/upload-file.vue";
import { Button, RadioGroup, Radio, Toast } from "vant";
// api
import { downloadAuthPdf, sendVcode, applyReview } from "@/api/realNameAuth";

export default {
  components: {
    tabs,
    itemInput,
    UploadFile,
    [Button.name]: Button,
    [Radio.name]: Radio,
    [RadioGroup.name]: RadioGroup,
    [Toast.name]: Toast
  },
  data() {
    return {
      count: "",
      showVerify: true,
      timer: null,
      message: {
        stepA: "企业认证",
        stepB: "实名认证"
      },
      radio: "2",
      name: "",
      idCode: "",
      phone: "",
      account: "",
      verifyCode: "", //验证码
      vCodeKey: "",
      creditCode: "",
      corp: "",
      cardGroupName: "",
      attachmentUrl: "", //上传凭证地址
      attachmentUrlArr:[], //上传凭证地址arr
    };
  },
  watch:{
    attachmentUrlArr(val) {
      if(val.length > 0) {
        this.attachmentUrl = val[0]?.fileurl;
      } else {
        this.attachmentUrl = "";
      }
    }
  },
  created() {
    let { name, idCode, account, creditCode, corp, cardGroupName } =
      this.$route?.query;
    if (name && idCode && account && creditCode && corp && cardGroupName) {
      this.name = name;
      this.idCode = idCode;
      this.account = account;
      this.creditCode = creditCode;
      this.corp = corp;
      this.cardGroupName = cardGroupName;
    }
    this.phone = sessionStorage.getItem("mobilePhoneAuth");
  },
  methods: {
    uploadSuccess(val) {
      this.attachmentUrl = val?.fileurl;
    },

    // 发送短信
    async sendVcode() {
      if (!this.phone) {
        return Toast.fail({
          duration: 2000,
          message: "请填写手机号",
          forbidClick: true
        });
      }
      Toast.loading({
        duration: 0,
        message: "获取验证中...",
        forbidClick: true
      });
      const TIME_COUNT = 60;
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.showVerify = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= TIME_COUNT) {
            this.count--;
          } else {
            this.showVerify = true;
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000);
      }
      try {
        const res = await sendVcode({
          mobile: this.phone,
          idName: this.name,
          idCode: this.idCode,
          account: this.account
        });
        if (res) {
          Toast.clear();
          Toast.success({
            message: "验证码已发送",
            forbidClick: true
          });
          this.$refs.inputVerify.focus();
          this.vCodeKey = res.vcodeKey;
        }
      } catch (error) {
        if (error) {
          Toast.clear();
        }
      }
    },

    // 授权书认证
    async applyReview() {
      Toast.loading({
        duration: 0,
        message: "授权认证中...",
        forbidClick: true
      });
      let params = {
        creditCode: this.creditCode, //信用代码
        legalName: this.corp, //法人
        applyName: this.name, //身份证名字
        applyIdNumber: this.idCode, //身份证号码
        attachmentUrl: this.attachmentUrl, //上传凭证地址
        contractId: this.$route.query?.contractId, //合同id
        approvalAccount: this.account, //account
        vCode: this.verifyCode, //验证码
        vCodeKey: this.vCodeKey, //验证码接口返回的key
        businessName: this.cardGroupName, //企业名称
        signType: 1
      };
      try {
        const res = await applyReview(params);
        if (res) {
          Toast.clear();
          Toast.success({
            message: "授权认证成功",
            forbidClick: true
          });
          // 成功后回到合同 / 付款页
          let sourcePage = this.$route.query?.sourcePage;
          let tokenAuth = sessionStorage.getItem("tokenAuth");
          if (sourcePage == "contractPage") {
            this.$router.push({
              name: "contractDetail",
              query: {
                contractId: this.$route.query?.contractId,
                token: tokenAuth,
                isLogin: "1"
              }
            });
          } else if (sourcePage == "paymentLandedPage") {
            // 跳转付款页面
            this.$router.push({
              name: "paymentLanded",
              query: {
                orderNo: this.$route.query?.orderNo,
                sourcePage: "paymentLandedPage",
                token: tokenAuth,
                isLogin: "1"
              }
            });
          }
        }
      } catch (error) {
        if (error) {
          Toast.clear();
        }
      }
    },

    // 下载授权书
    async downloadAuthPdf() {
      Toast.loading({
        duration: 0,
        message: "加载中...",
        forbidClick: true
      });
      try {
        const res = await downloadAuthPdf({
          billingName: this.cardGroupName, //签约主体名称/企业名称
          beAuthName: this.name, //被授权人姓名
          beAuthPhone: this.phone, //被授权人电话
          beAuthIdCard: this.idCode, //被授权人身份证号
          legalName: this.corp, //法人姓名
          creditCode: this.creditCode, //信用代码
          ssqAccount: this.account //上上签账号
        });
        if (res) {
          Toast.clear();
          window.location.href = res;
        }
      } catch (error) {
        if (error) {
          Toast.clear();
        }
      }
    }
  }
};
</script>
  
  <style lang="scss" scoped>
.noLegalPersonAuthent {
  background: #f7f6f8;
  display: flex;
  flex-direction: column;
  height: 100%;

  main {
    flex: 1;
    .input-box {
      background: #ffffff;
      padding: 0 16px;
      display: flex;
      align-items: center;
      position: relative;

      .autofill {
        font-size: 14px;
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: #ff9900;
        letter-spacing: 0;
        text-align: right;
        position: absolute;
        right: 16px;
      }

      .autofill-add {
        font-size: 14px;
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: #b2b2b2;
        letter-spacing: 0;
        text-align: right;
        position: absolute;
        right: 16px;
      }
    }
    .tips {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #7f7f7f;
      line-height: 20px;
      padding: 8px 16px;
    }

    .van-radio-group {
      padding: 16px;
      display: flex;
      background-color: #fff;
    }
    .van-radio {
      font-size: 14px;
    }

    .van-radio__icon {
      font-size: 16px;
    }

    .authorization {
      padding: 15px 16px;
      background: #fff;

      .authorization-tips {
        height: 22px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 22px;
      }
      .authorization-txt {
        height: 22px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #7f7f7f;
        line-height: 22px;
        margin-top: 8px;
      }
      .authorization-upload {
        // height: 162px;
        background: #f7f7f7;
        border-radius: 8px;
        margin-top: 20px;
      }
    }

    .uploadTxt {
      padding: 16px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #208bee;
      background-color: #fff;

      .uploadTxt-tips {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #7f7f7f;
        line-height: 22px;
        margin-top: 24px;
      }
    }
  }

  footer {
    z-index: 9999;
    height: 56px;
    background: #ffffff;
    box-shadow: 0 0 0 0 #f0f3fa;

    .btn-box {
      height: 100%;
      padding: 0 16px;
      display: flex;
      justify-content: center;
      align-items: center;

      .btn {
        border-radius: 21px;
      }
    }
  }
}
</style>