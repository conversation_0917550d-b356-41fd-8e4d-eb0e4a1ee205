<template>
  <van-popup v-model="show" position="bottom" round :style="{ height: height }">
    <div class="popup_time">
      <div class="title">
        <p></p>
        <p>期望发货日期</p>
        <img src="@/assets/<EMAIL>" @click="show = false" />
      </div>

      <main>
        <div class="pop_main">
          <p class="pop_title">推荐时间</p>
          <div class="pop-list">
            <div
              v-for="(item, index) in list"
              :key="item.id"
              :class="activeIndex == index ? 'active_p' : 'pop_check'"
              @click="checkTime(index, item)"
            >
              {{ item.key }}
            </div>
          </div>
        </div>

        <div class="pop_main">
          <p class="pop_title">自定义</p>
          <div class="pop_input">
            <input
              :value="startDate"
              type="text"
              class="time_input"
              placeholder="开始"
              readonly
              @click="startTime"
            />
            <p>—</p>
            <input
              :value="endDate"
              type="text"
              class="time_input"
              placeholder="结束"
              readonly
              @click="endTime"
            />
          </div>

          <!-- 开始 -->
          <van-datetime-picker
            v-show="showStartDatePicker"
            v-model="startCurrentDate"
            type="date"
            title="选择年月"
            :min-date="minDate"
            :max-date="maxDate"
            :formatter="formatter"
            @cancel="cancel"
            @confirm="confirm"
          />

          <!-- 结束 -->
          <van-datetime-picker
            v-show="showEndDatePicker"
            v-model="endCurrentDate"
            type="date"
            title="选择年月"
            :min-date="minDate"
            :max-date="maxDate"
            :formatter="formatter"
            @cancel="endCancel"
            @confirm="endConfirm"
          />
        </div>
      </main>

      <footer>
        <van-button
          block
          color="#FF9900"
          style="border-radius: 8px"
          @click="sureClick"
          >确定</van-button
        >
      </footer>
    </div>
  </van-popup>
</template>
  
  <script>
import { Popup, Button, DatetimePicker } from "vant";
export default {
  components: {
    [Popup.name]: Popup,
    [Button.name]: Button,
    [DatetimePicker.name]: DatetimePicker
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      activeIndex: 1,
      list: [
        { id: 1, key: "今天以前" },
        { id: 2, key: "今天" },
        { id: 3, key: "今天以后" }
      ],
      minDate: new Date(2020, 0, 1),
      maxDate: new Date(2025, 10, 1),
      startCurrentDate: new Date(),
      endCurrentDate: new Date(),
      height: "40%",
      showStartDatePicker: false,
      showEndDatePicker: false,
      startDate: "", //显示的开始时间
      endDate: "", //显示的结束时间
      deliveryDateMap: "2" //抛出推荐时间id (2代表 今天)
    };
  },
  watch: {
    value(val) {
      this.show = val;
    },
    show(val) {
      this.$emit("input", val);
    }
  },
  methods: {
    // 选择今天、前一天、后一天
    checkTime(index, item) {
      this.activeIndex = index;
      this.deliveryDateMap = item.id;
      // 选择前后时间时，自定义时间清空 这两个互斥
      this.startDate = "";
      this.endDate = "";
      this.startCurrentDate = "";
      this.endCurrentDate = "";
    },

    // 点击开始时间弹出时间选择器
    startTime() {
      this.showStartDatePicker = true;
      this.showEndDatePicker = false;
      this.height = "70%";
    },
    // 点击结束时间弹出时间选择器
    endTime() {
      this.showEndDatePicker = true;
      this.showStartDatePicker = false;
      this.height = "70%";
    },

    // 格式化年月日地址
    formatter(type, val) {
      if (type === "year") {
        return `${val}年`;
      } else if (type === "month") {
        return `${val}月`;
      } else if (type === "day") {
        return `${val}日`;
      }
      return val;
    },

    //开始时间确认
    confirm(val) {
      this.showStartDatePicker = false;
      this.height = "40%";
      this.startDate = this.$moment(val).format("YYYY-MM-DD");

      // 选择自定义时间时，推荐时间清空 这两个互斥
      this.activeIndex = -1;
      this.deliveryDateMap = "";
    },
    // 取消
    cancel() {
      this.showStartDatePicker = false;
      this.height = "40%";
    },

    // 结束时间确认
    endConfirm(val) {
      this.showEndDatePicker = false;
      this.height = "40%";
      this.endDate = this.$moment(val).format("YYYY-MM-DD");

      // 选择自定义时间时，推荐时间清空 这两个互斥
      this.activeIndex = -1;
      this.deliveryDateMap = "";
    },
    // 结束时间取消
    endCancel() {
      this.showEndDatePicker = false;
      this.height = "40%";
    },

    // 确认查询条件
    sureClick() {
      this.show = false;
      if (this.deliveryDateMap) {
        this.$emit("sureClick", {
          type: 1,
          deliveryDateMap: this.deliveryDateMap
        });
      } else {
        this.$emit("sureClick", {
          type: 2,
          deliverStartDate: this.startDate,
          deliverEndDate: this.endDate
        });
      
      }
    }
  }
};
</script>
  
  <style lang="scss" scoped>
.popup_time {
  height: 100%;
  padding: 0 16px;
  display: flex;
  flex-direction: column;
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 48px;

    p {
      font-family: PingFangSC-Medium;
      font-weight: 550;
      font-size: 16px;
      color: #000000;
      text-align: center;
    }
    img {
      width: 20px;
      height: 20px;
    }
  }

  main {
    flex: 1;
    min-height: 0;
  }

  .pop_main {
    flex: 1;
    margin-top: 16px;
    .pop_title {
      width: 56px;
      height: 20px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #1e232e;
      letter-spacing: 0;
    }

    .pop-list {
      display: flex;
      margin-top: 12px;
      flex-wrap: wrap;

      .pop_check {
        width: 80px;
        height: 32px;
        background: #f7f7f7;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #f7f7f7;
        margin-right: 8px;
      }

      .active_p {
        width: 80px;
        height: 32px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        border: 1px solid #ff9900;
        background: #ff99000f;
        color: #ff9900;
      }
    }

    .pop_input {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 12px 0;

      p {
        color: #b2b2b2;
      }

      .time_input {
        border: none;
        width: 155px;
        height: 32px;
        background: #f7f7f7;
        border-radius: 4px;
        text-align: center;
      }

      input::-webkit-input-placeholder {
        /* WebKit browsers，webkit内核浏览器 */
        color: #a1a1a1;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #b2b2b2;
        letter-spacing: 0;
        text-align: center;
      }
    }
  }

  footer {
    height: 44px;
    padding: 16px 0;
  }
}
</style>