<template>
  <div class="enter-network-login">
    <div class="logo-box">
      <img src="../../assets/<EMAIL>" alt="" class="logo" />
    </div>
    <div class="title">
      终端注册登录
    </div>
    <van-form @submit="onSubmit">
      <van-field
        v-model="username"
        name="username"
        placeholder="请输入用户名"
        :left-icon="userIcon"
      />
      <van-field
        v-model="password"
        name="password"
        :type="showPassword ? 'text' : 'password'"
        :left-icon="passwordIcon"
        :right-icon="showPassword ? showIcon : hideIcon"
        placeholder="请输入密码"
        @click-right-icon="switchPassword"
      />
      <div>
        <van-button
          round
          block
          type="info"
          native-type="submit"
          color="#FF9900"
          :disabled="forbiddenLogin"
          >登录</van-button
        >
      </div>
    </van-form>
  </div>
</template>
<script>
import { Form, Field, Button, Toast } from "vant";
import { login } from "../../api/enterNetwork";
import <PERSON><PERSON> from "js-cookie";
const Md5 = require("@/assets/js/md5");

export default {
  name: "enterNetworkLogin",
  components: {
    [Form.name]: Form,
    [Field.name]: Field,
    [Button.name]: Button,
    [Toast.name]: Toast
  },
  data() {
    return {
      username: "",
      password: "",
      userIcon: require("../../assets/<EMAIL>"),
      passwordIcon: require("../../assets/<EMAIL>"),
      showIcon: require("../../assets/show.png"),
      hideIcon: require("../../assets/hide.png"),
      showPassword: false,
      enterpriseId: "",
      productCode: "",
      activeRemark: "",
      macAddress: "",
      deptId: ""
    };
  },
  created() {
    this.productCode = this.$route.query.service;
    this.activeRemark = this.$route.query.remark;
    this.macAddress = this.$route.query.mac;
    this.deptId = this.$route.query.deptId;
  },
  computed: {
    forbiddenLogin() {
      if (this.username && this.password) {
        return false;
      } else {
        return true;
      }
    }
  },
  methods: {
    async onSubmit() {
      const params = {
        userName: this.username,
        password: Md5(this.password),
        loginType: 1
      };
      try {
        const res = await login(params);
        if (res?.enterpriseId) {
          this.enterpriseId = res.enterpriseId;
        }
        if (
          this.deptId &&
          res.enterpriseId &&
          res.enterpriseId != this.deptId
        ) {
          Toast.fail({
            duration: 2000,
            message: "请使用客户端同一企业账号登录激活"
          });
          return;
        }
        if (res?.token) {
          Cookie.set("token", res.token);
        }
        if (res?.errordescription) {
          // 如果存在errordescription，说明接口报错了，不允许登录，并提示出来
          Toast.fail({
            duration: 2000,
            message: res.errordescription
          });
          return;
        }
        this.$router.push({
          name: "enterNetworkRegister",
          query: {
            groupId: this.enterpriseId,
            productCode: this.productCode,
            activeRemark: this.activeRemark,
            macAddress: this.macAddress,
            deptId: this.deptId
          }
        });
      } catch (err) {
        Toast.fail({
          duration: 2000,
          message: err.result
        });
      }
    },
    switchPassword() {
      this.showPassword = !this.showPassword;
    }
  }
};
</script>
<style lang="scss" scoped>
/deep/ .van-form {
  .van-cell {
    width: 343px;
    height: 56px;
    background: #f5f6fa;
    border-radius: 28px;
    margin-bottom: 20px;
    padding: 0 16px;
    .van-cell__value {
      display: flex;
      align-items: center;
      .van-field__body {
        width: 100%;
      }
    }
    .van-field__left-icon {
      display: flex;
      align-items: center;
      margin-right: 8px;
      .van-icon__image {
        width: 16px;
        height: 18px;
        color: #333333;
      }
    }
  }
}
.enter-network-login {
  background-color: #fff;
  height: 100vh;
  padding: 28px 16px 0 16px;
  .logo-box {
    margin-bottom: 12px;
    text-align: center;
    .logo {
      width: 100px;
      height: 40px;
    }
  }
  .title {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #898fa3;
    margin-bottom: 32px;
    text-align: center;
  }
}
</style>
