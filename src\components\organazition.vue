<template>
  <div class="organazition-container">
    <van-nav-bar
      left-arrow
      @click-left="onClickLeft"
      :title="$t('organization')"
    ></van-nav-bar>
    <div class="body">
      <van-search
        v-model="searchVal"
        :placeholder="$t('search')"
        @search="handleSearch"
      />
      <div class="organazition-title" v-if="pathList.length > 0">
        <div class="path-box">
          <p
            class="path-item"
            v-for="item in pathList"
            :key="item.id"
            @click="findCurrentPath(item)"
          >
            <span class="name">{{ item.name }}</span>
            <img
              src="../assets/right_arrow.png"
              alt=""
              class="arrow path-arrow"
            />
          </p>
        </div>
      </div>
      <div class="organazition-list">
        <van-loading
          size="24px"
          vertical
          color="#FF9900"
          v-if="loading"
          style="margin-top: 200px"
          >{{ $t("loading") }}...</van-loading
        >

        <div
          class="organazition-item"
          v-for="item in organizationList"
          :key="item.id"
        >
          <van-checkbox
            v-model="item.checked"
            checked-color="#FF9900"
            @change="(event) => handleCheckChange(event, item)"
          />
          <div class="orgazation-right" @click="handleOrgazitionClick(item)">
            <div class="organazition-left">
              <img
                src="../assets/one_level.png"
                alt=""
                class="level"
                v-if="item.isParent"
              />
              <img
                src="../assets/second_level.png"
                alt=""
                class="level"
                v-else
              />
              <span class="organazition-name"
                >{{ item.name
                }}<span class="nums">({{ item.shopCount }})</span></span
              >
            </div>
            <img src="../assets/right_arrow.png" alt="" class="arrow" />
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <van-button
        type="primary"
        color="#FF9900"
        block
        @click="save"
        :disabled="organizationList.length == 0"
        >{{ $t("confirm") }}</van-button
      >
    </div>
  </div>
</template>

<script>
import { NavBar, Checkbox, Search, Button, Loading, Toast } from "vant";
import { getMasterOrgTrees } from "../api/renewal";
export default {
  components: {
    [NavBar.name]: NavBar,
    [Checkbox.name]: Checkbox,
    [Search.name]: Search,
    [Button.name]: Button,
    [Loading.name]: Loading,
    [Toast.name]: Toast
  },
  data() {
    return {
      searchVal: "",
      organizationList: [],
      pathList: [],
      checkedList: [],
      loading: false,
      groupId: "",
      checkedIdList: []
    };
  },
  created() {
    this.groupId = this.$route.query.groupId;
    this.getMasterOrgTrees();
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1);
    },
    async handleOrgazitionClick(item) {
      this.searchVal = "";
      try {
        await this.getMasterOrgTrees(item.id);
        const index = this.pathList.findIndex((path) => path.id === item.id);
        if (index == -1) {
          this.pathList.push(item);
        }
      } catch (error) {
        console.log(error);
      }
    },
    findCurrentPath(item) {
      const index = this.pathList.findIndex((path) => path.id === item.id);
      this.getMasterOrgTrees(item.id);
      this.pathList = this.pathList.slice(0, index + 1);
    },
    save() {
      // 数组去重
      const list = JSON.parse(sessionStorage.getItem("checkedIdList"));
      if (list) {
        this.checkedIdList = list.concat(this.checkedIdList);
      }
      const uniqueArr = this.checkedIdList.reduce((result, obj) => {
        if (!result.some((item) => item.id === obj.id)) {
          result.push(obj);
        }
        return result;
      }, []);
      sessionStorage.setItem("checkedIdList", JSON.stringify(uniqueArr));
      this.$router.go(-1);
    },
    async getMasterOrgTrees(id) {
      this.organizationList = [];
      const params = {
        showType: 3,
        groupId: this.groupId,
        showShopCount: true,
        keyword: this.searchVal
      };
      if (id) {
        params.id = id;
      }
      try {
        this.loading = true;
        let res = await getMasterOrgTrees(params);
        if (this.searchVal) {
          // 递归获取下面的子级
          res = this.searchByName(this.searchVal, res);
        }
        const result = this._resetTreeDataStructure(res);
        this.organizationList = result;
      } catch (error) {
        Toast.fail({
          duration: 2000,
          message: error.result || error.data
        });
      } finally {
        this.loading = false;
      }
    },
    handleSearch() {
      this.pathList = [];
      this.getMasterOrgTrees();
    },
    // 修改树结构的数据
    _resetTreeDataStructure(data) {
      if (!(data instanceof Array)) {
        return;
      }
      return data.map((item) => {
        if (item.isParent) {
          return Object.assign(this._changeStructure(item), { children: [] });
        } else {
          return this._changeStructure(item);
        }
      });
    },
    _changeStructure(res) {
      const checkedIdList =
        JSON.parse(sessionStorage.getItem("checkedIdList")) ||
        this.checkedIdList;
      return Object.assign({}, res, {
        title: res.name || "",
        checked: checkedIdList.some((it) => it.id === res.organizeId)
      });
    },
    handleCheckChange(checked, item) {
      const list = JSON.parse(sessionStorage.getItem("checkedIdList")) || [];
      if (checked) {
        this.checkedIdList.push({
          id: item.organizeId,
          name: item.name
        });
      } else {
        const storageIndex = list.findIndex((it) => it.id == item.organizeId);
        const index = this.checkedIdList.findIndex(
          (it) => it.id === item.organizeId
        );
        if (storageIndex !== -1) {
          list.splice(storageIndex, 1);
          sessionStorage.setItem("checkedIdList", JSON.stringify(list));
        }
        if (index !== -1) {
          this.checkedIdList.splice(index, 1);
        }
      }
    },
    searchByName(name, nodes) {
      let results = [];
      const searchNode = (nodes) => {
        nodes.forEach((node) => {
          if (node.name.includes(name)) {
            results.push(node);
          }
          if (node.child) {
            searchNode(node.child);
          }
        });
      };
      searchNode(nodes);
      return results;
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .van-nav-bar .van-icon {
  font-size: 28px;
  color: #666;
}
.organazition-container {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.body {
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
  flex: 1;
  background-color: #fff;
  padding: 0 16px;
  .organazition-title {
    height: 44px;
    display: flex;
    background: #ffffff;
    margin-top: 8px;
    line-height: 44px;
    font-size: 14px;
    color: #000000;
    font-weight: 700;
    .path-box {
      flex: 1;
      display: flex;
      white-space: nowrap;
      overflow-x: auto;
      .path-item {
        font-weight: 500;
        display: flex;
        align-items: center;
      }
    }
  }
  .organazition-item {
    display: flex;
    align-items: center;
    .orgazation-right {
      display: flex;
      flex: 1;
      align-items: center;
      border-bottom: 1px solid #f0f0f0;
      padding: 18px 0;
      margin-left: 12px;
    }
    .organazition-left {
      display: flex;
      align-items: center;
      .level {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }
      .organazition-name {
        font-size: 14px;
        color: #333333;
        font-weight: 500;
        .nums {
          font-weight: 400;
          font-size: 14px;
          color: #b2b2b2;
        }
      }
    }
  }
  .arrow {
    width: 16px;
    height: 16px;
    margin-left: auto;
    &.path-arrow {
      vertical-align: text-bottom;
    }
  }
}
.footer {
  width: 100vw;
  height: 52px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  .van-button {
    width: 343px;
    height: 38px;
    background: #ff9900;
    border-radius: 6px;
  }
}
</style>
