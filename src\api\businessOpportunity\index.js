import urls from "./config";
import { get } from "../request";

export function getUserInformationList(params = {}) {
  return get(urls.getUserInformationList, params);
}

export function updateReadStatus(id) {
  return get(`${urls.updateReadStatus}?id=${id}`);
}

export function getAllInformationList(params = {}) {
  return get(urls.getAllInformationList, params);
}

export function getUserInformationCount(params = {}) {
  return get(urls.getUserInformationCount, params);
}

export function getInformationDetail(id) {
  return get(`${urls.getInformationDetail}?id=${id}`);
}
