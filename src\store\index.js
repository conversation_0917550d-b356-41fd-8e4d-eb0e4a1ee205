import Vue from "vue";
import Vuex from "vuex";
Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
    arrearsAmount: 0,
    overdueMoney: 0,
    accountDate: {
      startDate: "",
      endDate: ""
    },
    selectedHistory: []
  },
  mutations: {
    setArrearsAmount(state, value) {
      state.arrearsAmount = value;
    },
    setOverdueMoney(state, value) {
      state.overdueMoney = value;
    },
    setAccountDate(state, value) {
      state.accountDate.startDate = value.startDate;
      state.accountDate.endDate = value.endDate;
    },
    setSelectedHistory(state, value) {
      let index = state.selectedHistory.findIndex(
        item => item.index === value.index
      );
      if (index !== -1) {
        state.selectedHistory.splice(index, 1, value);
        return;
      }
      state.selectedHistory.push(value);
    },
    cleanHistory(state) {
      state.selectedHistory = [];
    },
    deleteSelectedHistory(state, index) {
      state.selectedHistory.splice(
        index + 1,
        state.selectedHistory.length - index - 1
      );
    }
  }
});

// 导出仓库
export default store;
