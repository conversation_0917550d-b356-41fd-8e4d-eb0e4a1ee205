<template>
  <div>
    <van-popup
      v-model="show"
      position="right"
      :style="{ height: '100%', width: '100%' }"
    >
      <div class="add-eduction">
        <!-- title -->
        <div class="title">
          <van-icon name="cross" @click="closePopup" />
          <span class="title-name">{{editWork.id ? '编辑工作经历' :"工作经历"}}</span>
          <span  v-if="!editWork.id" class="save" @click="saveStaffWorkHistory">保存</span>
          <span v-else></span>
        </div>
        <!-- main -->
        <main>
          <van-field class="must" label="单位全称" v-model="form.companyName" placeholder="请输入" />

          <van-field class="no-must" label="岗位" v-model="form.post" placeholder="请输入" />

          <van-field class="no-must" label="部门" v-model="form.deptName" placeholder="请输入" />

          <van-field class="no-must" label="职务" v-model="form.position" placeholder="请输入" />

          <!-- 在校期间 -->
          <div class="duringSchool">
            <label class="must">在职时间</label>
            <div class="time">
              <p :class="this.startTime?'txtTime':''" @click="clickStartTime">{{this.startTime ? this.startTime :"开始日期"}}</p>
              <span>至</span>
              <p :class="this.endTime?'txtTime':''" @click="clickEndTime">{{this.endTime ? this.endTime :"结束日期"}}</p>
            </div>
          </div>

          <!-- 开始工作日期 -->
          <van-popup v-model="showStartTime" position="bottom">
            <van-datetime-picker
              v-model="startTimeTemp"
              type="date"
              title="选择年月日"
              :min-date="minDate"
              :max-date="maxDate"
              @confirm="onStartTime"
              @cancel="showStartTime = false"
            />
          </van-popup>

          <!-- 结束工作日期 -->
          <van-popup v-model="showEndTime" position="bottom">
            <van-datetime-picker
              v-model="endTimeTemp"
              type="date"
              title="选择年月日"
              :min-date="minDate"
              :max-date="maxDate"
              @confirm="onEndTime"
              @cancel="showEndTime = false"
            />
          </van-popup>

          <label class="main-work">主要从事技术工作</label>
          <van-field
            v-model="form.workContent"
            rows="2"
            autosize
            label=""
            type="textarea"
            maxlength="50"
            placeholder="请输入"
            show-word-limit
          />
        </main>

        <!-- footer -->
        <footer v-show="editWork.id">
          <van-button type="primary" class="last" @click="deleteStaffWorkHistory">删除</van-button>
          <van-button type="primary" class="next" @click="updateStaffWorkHistory">完成</van-button>
        </footer>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  NavBar,
  Button,
  Divider,
  CellGroup,
  Field,
  Icon,
  Dialog,
  Picker,
  Popup,
  DatetimePicker,
  Toast,
} from "vant";
// api
import {saveStaffWorkHistory , deleteStaffWorkHistory, updateStaffWorkHistory} from "../../../../api/hrm";
export default {
  components: {
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Divider.name]: Divider,
    [CellGroup.name]: CellGroup,
    [Field.name]: Field,
    [Icon.name]: Icon,
    [Picker.name]: Picker,
    [Popup.name]: Popup,
    [Dialog.Component.name]: Dialog.Component,
    [DatetimePicker.name]: DatetimePicker,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    editWork:{
      type:Object,
      default:() => {},
    }
  },
  data() {
    return {
      show: false,
      startTimeTemp:new Date(),
      endTimeTemp:new Date(),
      showStartTime: false, //工作开始弹出框
      showEndTime:false,//工作结束弹出框
      startTime:'', //开始工作日期
      endTime:'',//结束工作日期
      form: {
        id:"",
        companyName: "", //单位全称
        startTime: "", //传值开始日期
        endTime: "", //传值结束日期
        deptName: "", //部门
        post: "", //岗位
        workContent: "", //主要从事技术工作
        position: "", //职务
      },

      minDate: new Date(1979, 0, 1),
      maxDate: new Date(2099, 10, 1),
    };
  },
  watch: {
    value(val) {
      this.show = val;
    },
    show(val) {
      this.$emit("input", val);
      if(!val) {
        for(let i in this.form) {
          this.form[i] = "";
        }
        this.startTime = "";
        this.endTime = "";
      }
    },
    editWork(val) {
      console.log(val);
      if(val) {
        this.form.companyName = val.companyName, //单位全称
        this.form.startTime = val.startTime, //传值开始日期
        this.form.endTime = val.endTime, //传值结束日期
        this.form.deptName = val.deptName, //部门
        this.form.post = val.post, //岗位
        this.form.workContent = val.workContent, //主要从事技术工作
        this.form.position = val.position, //职务
        this.form.id = val.id;
        // 日期显示
        this.startTime = this.$moment(val.startTime).format("YYYY-MM-DD");
        this.endTime = this.$moment(val.endTime).format("YYYY-MM-DD");
      }
    }
  },
  methods: {
    closePopup() {
      this.show = false;
    },

    clickStartTime(val) {
      this.showStartTime = true;
      this.startTimeTemp = new Date(val.target.innerText);
    },

    // 点击工作开始时间
    onStartTime(val) {
      this.startTime = this.$moment(val).format("YYYY-MM-DD");
      this.form.startTime = this.$moment(val).format("YYYY-MM-DD HH:mm:ss");;
      this.showStartTime = false;
    },

    clickEndTime(val) {
      this.showEndTime = true;
      this.endTimeTemp = new Date(val.target.innerText);
    },

    // 点击工作结束时间
    onEndTime(val) {
      this.endTime = this.$moment(val).format("YYYY-MM-DD");
      this.form.endTime = this.$moment(val).format("YYYY-MM-DD HH:mm:ss");
      this.showEndTime = false;
    },

    // 新增工作经历
    async saveStaffWorkHistory() {
      if(!this.form.companyName || !this.form.startTime || !this.form.endTime) {
        return Toast.fail({
          duration: 2000,
          message: "请填写完整！",
        });
      }
      
      if (Date.parse(this.form.endTime) < Date.parse(this.form.startTime)) {
        return Toast.fail({
          duration: 2000,
          message: "工作开始日期不能小于工作结束日期",
        });
      }

      let obj = {
        staffId: this.$route.query.id,
        ...this.form,
      }
      try {
        const res = await saveStaffWorkHistory(obj);
        this.$emit('workUpdata');
        console.log(res);
        this.show = false;
      }catch(error) {
        console.log(error);
      }
    },

    // 更新工作经历
    async updateStaffWorkHistory() {
      if(!this.form.companyName || !this.form.startTime || !this.form.endTime) {
        return Toast.fail({
          duration: 2000,
          message: "请填写完整！",
        });
      } 

      if (Date.parse(this.form.endTime) < Date.parse(this.form.startTime)) {
        return Toast.fail({
          duration: 2000,
          message: "工作开始日期不能小于工作结束日期",
        });
      }

      let obj = {
        ...this.form,
        staffId: this.$route.query.id,
      }
      try {
        const res = await updateStaffWorkHistory(obj);
        this.$emit('workUpdata');
        console.log(res);
        this.show = false;
      }catch(error) {
        console.log(error);
      }
    },

    // 删除工作经历
    async deleteStaffWorkHistory() {
      try {
        const res = await deleteStaffWorkHistory(this.editWork.id);
        this.$emit('workUpdata');
        console.log(res);
        this.show = false;
      }catch(error) {
        console.log(error);
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.add-eduction {
  height: 100%;
  display: flex;
  flex-direction: column;

  .title {
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;

    .title-name {
      // width: 68px;
      height: 24px;
      font-family: PingFangSC-S0pxibold;
      font-weight: 600;
      font-size: 17px;
      color: #333333;
      text-align: center;
    }

    .save {
      // width: 28px;
      height: 20px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #7f7f7f;
    }
    .save:hover {
      // width: 28px;
      height: 20px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #ff9900;
    }
  }

  main {
    flex: 1;
  }

  footer {
    height: 56px;
    background: #ffffff;
    box-shadow: 0 0 0 0 #f0f3fa;
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-top: 1px solid #f0f3fa;

    .last {
      background-color: #f0f0f0;
      border: 1px solid #f0f0f0;
      width: 138px;
      height: 40px;
      color: #7f7f7f;
      font-weight: 500;
      font-size: 14px;
      text-align: center;
      border-radius: 21px;
    }
    .next {
      width: 197px;
      height: 40px;
      background: #ff9900;
      border-radius: 21px;
      border: 1px solid #ff9900;
    }
  }
  .duringSchool {
    height: 54px;
    display: flex;
    align-items: center;
    margin: 0 16px;
    position: relative;

    label {
      display: inline-block;
      width: 95.8px;
      font-size: 3.7234vw;
      box-sizing: border-box;
      // margin-right: 5.1vw;
      color: #646566;
      text-align: left;
      word-wrap: break-word;
      // padding: 0 4.26667vw;
    }

    .time {
      flex: 1;
      display: flex;
      align-items: center;
      span {
        width: 41.5px;
        color: #646566;
      }

      p {
        width: 83.5px;
        color: #c8c9cc;
        font-size: 3.7234vw;
      }

      .txtTime {
        color: #333333;
      }
    }
  }

  .duringSchool::after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    right: -0.73333vw;
    bottom: 0;
    left: 0.26667vw;
    border-bottom: 1px solid #ebedf0;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }

  .main-work {
    display: inline-block;
    font-size: 3.7234vw;
    box-sizing: border-box;
    margin-right: 5.1vw;
    color: #646566;
    text-align: left;
    word-wrap: break-word;
    padding: 10px 4.26667vw;
  }
}

/deep/.van-cell {
  padding: 3.46667vw 4.26667vw;
}

.must {
  &::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
}

.no-must {
  &::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #fff;
  }
}
</style>