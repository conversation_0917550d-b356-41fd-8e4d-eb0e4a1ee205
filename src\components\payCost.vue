<template>
  <div class="order-pay">
    <!-- <van-nav-bar v-show="showHome" title="支付中心" left-arrow @click-left="onleftClick"></van-nav-bar> -->
    <van-nav-bar
      v-show="showHome"
      title="支付中心"
      left-arrow
      @click-left="onleftClick"
    ></van-nav-bar>
    <div class="bill-name" v-if="contractDetail.contractName">
      {{ contractDetail.contractName }}
    </div>
    <div class="money"><span class="sepr">￥</span>{{ billDetail.amount }}</div>
    <div class="pay-text">实付金额</div>
    <div class="message-box">
      <div class="message-item">
        <label>客户名称</label>
        <span>{{ billDetail.customerName }}</span>
      </div>
      <div class="message-item" v-if="contractDetail.contractNo">
        <label>合同号</label>
        <!-- <label v-show="$route.query.type == 2">账单号:</label> -->
        <span>{{ contractDetail.contractNo }}</span>
      </div>
      <div class="message-item" v-show="$route.query.type == 1">
        <label>合同生成时间</label>
        <span>{{ billDetail.createTime }}</span>
      </div>
      <div class="message-item" v-show="$route.query.type == 2">
        <label>账单生成时间</label>
        <span>{{ billDetail.createTime }}</span>
      </div>
      <div class="message-item" v-show="$route.query.type == 2">
        <label>账单确认时间</label>
        <span>{{ billDetail.confirmTime }}</span>
      </div>
    </div>

    <!-- <div class="money">￥{{ billDetail.amount }}</div> -->
    <div class="pay-way-container wx-way">
      <div class="wx-way-title">
        选择支付方式
      </div>
      <div class="wx-way-item" @click="selectPayMode(1)">
        <div class="item-left" v-show="!isAliPay">
          <img class="wx-pay-img" src="../assets/ico_wechat.png" />
          <div class="way-name">
            微信支付
          </div>
        </div>

        <div class="item-left" v-show="isAliPay">
          <img class="wx-pay-img" src="../assets/ico_zfb.png" />
          <div class="way-name">
            支付宝支付
          </div>
        </div>

        <img
          class="wx-true-img"
          v-if="payModeType == 1"
          src="../assets/3.png"
        />
        <img class="wx-true-img" v-else src="../assets/4.png" />
      </div>

      <div class="wx-way-item" @click="selectPayMode(2)" v-if="isFromBill">
        <div class="item-left">
          <img class="wx-pay-img" src="../assets/1.png" />
          <div class="way-name">
            对公转账
          </div>
        </div>
        <img
          class="wx-true-img"
          v-if="payModeType == 2"
          src="../assets/3.png"
        />
        <img class="wx-true-img" v-else src="../assets/4.png" />
      </div>

      <div class="wx-way-item" @click="selectPayMode(4)">
        <div class="item-left">
          <img class="wx-pay-img" src="../assets/yuer.png" />
          <div class="way-name">
            余额支付
          </div>
        </div>
        <img
          class="wx-true-img"
          v-if="payModeType == 4"
          src="../assets/3.png"
        />
        <img class="wx-true-img" v-else src="../assets/4.png" />
      </div>
    </div>

    <div v-if="payModeType == 4">
      <van-cell
        title="支付账户"
        is-link
        @click="showPayAccount = true"
        v-if="customerAccountList && customerAccountList.length > 0"
      >
        {{ selectedAccount.cardNumber }} - {{ selectedAccount.money }}
      </van-cell>
      <van-cell title="支付账户" is-link @click="showPayAccount = true" v-else>
        暂无账户信息
      </van-cell>
      <!-- 弹出选择窗 -->
      <van-popup
        v-model="showPayAccount"
        round
        position="bottom"
        :style="{ height: '30%' }"
      >
        <div class="popChange">
          <div class="header">
            <div></div>
            <p>账户选择</p>
            <img
              src="../assets/ico_close_r.png"
              alt=""
              @click="showPayAccount = false"
            />
          </div>

          <div
            class="item"
            :class="{ active: item.id === selectedAccount.id }"
            v-for="item in customerAccountList"
            :key="item.id"
            @click="selectAccount(item)"
          >
            {{ item.cardNumber }}
            <div class="item_footer" v-if="item.id === selectedAccount.id">
              <img
                src="../assets/ico_choice_r.png"
                style="width: 16px;height: 16px;"
                alt=""
              />
            </div>
          </div>
        </div>
      </van-popup>
    </div>

    <div class="pay-btn-box">
      <!-- 如果合同不存在，去生成合同 -->
      <van-button
        class="wx-pay-btn"
        @click="generateContract"
        :loading="generateLoading"
        v-if="!contractDetail.contractNo && isFromBill"
        >生成合同</van-button
      >
      <!-- 合同已经存在 -->
      <van-button
        class="wx-pay-btn"
        :loading="loading"
        @click="payNow()"
        :disabled="(payModeType != 1 && payModeType != 4) || disabledPay"
        v-show="!disabledPay && contractDetail.contractNo"
      >
        立即支付 ￥{{ billDetail.amount }}
      </van-button>
      <van-button
        class="wx-pay-btn"
        :disabled="(payModeType != 1 && payModeType != 4) || disabledPay"
        v-show="disabledPay && contractDetail.contractNo"
      >
        已支付
      </van-button>
    </div>

    <van-popup
      v-model="show"
      :close-on-click-overlay="false"
      :round="true"
      :style="{ width: '70%', borderRadius: '7px' }"
    >
      <div class="pay-result">
        <p class="title">请确认支付是否已完成</p>
        <p class="success" @click="finishPay">已完成支付</p>
        <p class="faild" @click="hidePopUp">支付遇到问题，重新支付</p>
      </div>
    </van-popup>

    <van-overlay :show="showLoading" @click="show = false">
      <div class="wrapper" @click.stop>
        <div class="block" />
      </div>
    </van-overlay>

    <van-loading
      v-if="showLoading"
      size="24px"
      vertical
      type="spinner"
      color="#0094ff"
      >支付加载中...</van-loading
    >

    <!-- 余额支付验证码 -->
    <van-popup
      v-model="balancePay"
      :close-on-click-overlay="false"
      :round="true"
      :style="{ width: '90%', borderRadius: '7px' }"
    >
      <div class="sms-verify-popup">
        <div class="popup-header">
          <span></span>
          <span>余额支付短信验证</span>
          <van-icon name="cross" @click="balancePay = false" />
        </div>
        <div class="verify-code-container">
          <div class="verify-code-input">
            <input
              v-for="(digit, index) in 6"
              :key="index"
              v-model="smsCode[index]"
              type="tel"
              maxlength="1"
              @input="handleSmsInput(index)"
              @keydown="handleKeyDown($event, index)"
              ref="smsInputs"
            />
          </div>
          <div class="resend-code">
            <span v-if="countdown > 0">{{ countdown }}秒后重新发送</span>
            <span v-else class="resend-btn" @click="sendSmsCode">重新发送</span>
          </div>
        </div>
        <div class="confirm-btn">
          <van-button
            type="primary"
            block
            @click="confirmSmsCode"
            :disabled="!isSmsCodeComplete"
            >确定</van-button
          >
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  Popup,
  NavBar,
  Loading,
  Button,
  Icon,
  Toast,
  Overlay,
  Dialog,
  Cell
} from "vant";

import {
  payh5,
  contractPay,
  h5BillDetail,
  getContractDetailForH5,
  isPay,
  qrPay,
  saveBillVoucher,
  confirmBill,
  getCustomerAccountList,
  getCurrentCustomer,
  getVerifyCode,
  payCustomerAccount
} from "../api/delivery";
import { payByOther } from "../api/bill";

export default {
  components: {
    [NavBar.name]: NavBar,
    [Overlay.name]: Overlay,
    [Popup.name]: Popup,
    [Loading.name]: Loading,
    [Button.name]: Button,
    [Dialog.name]: Dialog,
    [Icon.name]: Icon,
    [Dialog.Component.name]: Dialog.Component,
    [Cell.name]: Cell
  },

  data() {
    return {
      show: false,
      contractDetail: {},
      billDetail: {},
      showLoading: true,
      disabledPay: true,
      loading: false,
      showHome: true,
      isAliPay: true,
      payType: "", //付款类型（4微信，5支付宝）
      payModeType: 1,
      copyMsg: "收到来自万店掌的续费账单，请您点击查看并完成缴费",
      type: this.$route.query.type,
      showPaymentDialog: false,
      attachmentList: [],
      accept: "image/jpeg,image/jpg,image/png,image/gif",
      isFromBill: false,
      isBalance: false,
      generateLoading: false,
      showPayAccount: false,
      accountList: [],
      balancePay: false,
      smsCode: ["", "", "", "", "", ""],
      countdown: 60,
      countdownTimer: null,
      selectedAccount: {},
      customerAccountList: [],
      cellphone: ""
    };
  },

  computed: {
    isSmsCodeComplete() {
      return this.smsCode.every(digit => digit !== "");
    }
  },

  created() {
    this.getCurrentCustomer();
    //根据type判断是从合同还是从账单跳转过来
    this.$route.query.type == 1 && this.getContractDetailForH5();
    this.$route.query.type == 2 && this.h5BillDetail();
    if (this.$route.query.type == 2) {
      this.isFromBill = true;
    }
    let pay = JSON.parse(sessionStorage.getItem("pay"));
    this.show = pay;

    // 如果选择了余额支付，初始化账户列表
    if (this.payModeType === 4) {
      this.getCustomerAccountList();
    }
  },

  mounted() {
    this.IsWeixinOrAlipay();
  },

  methods: {
    //选择支付方式
    selectPayMode(type) {
      this.payModeType = type;

      if (type == 4) {
        this.getCustomerAccountList();
        this.isBalance = true;
      }
      // if (type == 3) {
      //   this.otherPay();
      // } else if (type == 2) {
      //   this.showPaymentDialog = true;
      // }
    },

    // 找他人付
    async corporatePay() {
      const copyText = `对公转账\n\n户\u00A0\u00A0\u00A0名\u00A0\u00A0\u00A0\u00A0\u00A0苏州万店掌网络科技有限公司\n\n账\u00A0\u00A0\u00A0号\u00A0\u00A0\u00A0\u00A0\u00A032250198863600000746 \n\n开户行\u00A0\u00A0\u00A0\u00A0\u00A0中国建设银行苏州市高新技术产业
      \u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0开发区支行营业部\n\n合同号\u00A0\u00A0\u00A0\u00A0\u00A0${this.contractDetail.contractNo}\n\n金\u00A0\u00A0\u00A0额\u00A0\u00A0\u00A0\u00A0\u00A0${this.billDetail.amount}`;
      Dialog.confirm({
        message: copyText,
        confirmButtonText: "复制信息",
        messageAlign: "left",
        beforeClose: (action, done) => {
          if (action === "confirm") {
            this.$copyText(copyText).then(
              () => {
                Toast.success("复制成功，快去转发吧");
              },
              () => {
                Toast.fail("复制失败");
              }
            );
            done(false);
          } else {
            done();
          }
        }
      });
    },

    confirm() {
      console.log("点击了复制并转发按钮...");
    },

    // 找他人付
    async otherPay() {
      const params = {
        billDetailURL: `${window.location.href}&type=other`
      };
      try {
        const res = await payByOther(params);
        if (res) {
          const copyText = `他人代付\n\n${this.copyMsg}\n\n${res}`;
          Dialog.confirm({
            message: copyText,
            confirmButtonText: "复制链接",
            messageAlign: "left",
            beforeClose: (action, done) => {
              if (action === "confirm") {
                this.$copyText(copyText).then(
                  () => {
                    Toast.success("复制成功，快去转发吧");
                  },
                  () => {
                    Toast.fail("复制失败");
                  }
                );
                done(false);
              } else {
                done();
              }
            }
          });
        }
      } catch (err) {
        Toast.fail({
          duration: 2000,
          message: err.result
        });
      }
    },

    IsWeixinOrAlipay() {
      var ua = window.navigator.userAgent.toLowerCase();
      //判断是不是微信
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        this.payType = 4;
        this.isAliPay = false;
        this.showHome = false;
        return "WeiXIN";
      }
      //判断是不是支付宝
      if (ua.match(/AlipayClient/i) == "alipayclient") {
        this.payType = 5;
        this.isAliPay = true;
        this.showHome = false;
        return "Alipay";
      }
      //哪个都不是
      this.isAliPay = false;
      return "false";
    },

    /**获取账单详情 */
    async h5BillDetail() {
      this.billDetail = {};
      let res = await h5BillDetail({
        billId: this.$route.query.billId,
        isDetail: 0
      });
      this.showLoading = false;
      this.contractDetail = res.contractPayDetail || {};
      this.billDetail = res || {};
      this.disabledPay = res ? (res.isPay ? true : false) : true;
    },

    /**获取合同详情 */
    async getContractDetailForH5() {
      this.billDetail = {};
      try {
        let id = this.$route.query.contractId || "";
        const res = await getContractDetailForH5(id);
        this.showLoading = false;
        this.contractDetail = res || {};
        this.billDetail.amount =
          (res.price - res.paymentAmount).toFixed(2) || 0;
        this.billDetail.createTime = res.createTime;
        this.disabledPay = res
          ? res.paymentStatus == "3"
            ? true
            : false
          : true;
      } catch (error) {
        console.log(error);
      }
    },

    //取消弹出确认层
    hidePopUp() {
      this.show = false;
      sessionStorage.setItem("pay", false);
      this.$route.query.type == 1 && this.getContractDetailForH5();
      this.$route.query.type == 2 && this.h5BillDetail();
    },

    //完成付款  根据type去做判断
    async finishPay() {
      if (this.$route.query.type == 2) {
        let res = await isPay({ billId: this.$route.query.billId });
        if (!res) {
          this.show = false;
          sessionStorage.setItem("pay", false);
          Toast.fail({
            duration: 4000,
            message: "支付未完成，请重新完成支付"
          });
        } else {
          this.show = false;
          sessionStorage.setItem("pay", false);
          this.$router.push({
            name: "billDetail",
            query: {
              billId: this.$route.query.billId
            }
          });
        }
      } else {
        let id = this.$route.query.contractId || "";
        let res = await getContractDetailForH5(id);
        if (res.paymentStatus !== "3") {
          this.show = false;
          sessionStorage.setItem("pay", false);
          Toast.fail({
            duration: 4000,
            message: "支付未完成，请重新完成支付"
          });
        } else {
          this.show = false;
          sessionStorage.setItem("pay", false);
          this.$router.push({
            name: "contractDetail",
            query: {
              contractId: this.$route.query.contractId
            }
          });
        }
      }
    },

    /**立即支付   判断是否是微信内部支付？jsapi:h5api*/
    payNow() {
      if (this.payModeType == 4) {
        if (!this.cellphone) {
          return Toast.fail({
            message: "未查询到用户手机号",
            duration: 2000
          });
        }
        if (this.selectedAccount.money < this.billDetail.amount) {
          return Toast.fail({
            message: "余额不足！",
            duration: 2000
          });
        }
        // 余额支付，唤起余额支付短信验证码
        this.balancePay = true;
        this.smsCode = ["", "", "", "", "", ""];
        this.sendSmsCode(); // 自动发送验证码
      } else {
        if (this.IsWeixinOrAlipay() !== "false") {
          this.getAuth();
        } else {
          this.payInNoWx();
        }
      }
    },

    /** 立即支付 微信内部支付  2022/8/31弃用原方式换建行支付*/
    async getAuth() {
      // 动态二维码（发起支付）
      const obj = {
        amount: Number(this.billDetail.amount), //金额
        orderNo: this.contractDetail.contractNo,
        paymentMode: this.payType, //付款类型（4微信，5支付宝）
        openId: this.$route.query?.openId,
        mobilePhone: this.$route.query?.mobilePhone,
        oppositeAccountName: this.$route.query?.accountName,
        isOpenOrder: this.$route.query?.isOpenOrder
      };
      try {
        const res = await qrPay(obj);
        if (res) {
          this.loading = false;
          let u = navigator.userAgent;
          let isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1; //android终端
          let isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
          if (isAndroid) {
            window.open(res);
          } else if (isiOS) {
            window.location.href = res;
          }
        }
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    },

    /**唤起支付的方法 */
    onBridgeReady(res) {
      let that = this;
      window.WeixinJSBridge.invoke(
        "getBrandWCPayRequest",
        {
          appId: res.appId, //公众号名称，由商户传入
          timeStamp: res.timeStamp, //时间戳，自1970年以来的秒数
          nonceStr: res.nonceStr, //随机串
          package: res.package, // prepay_id=xxx
          signType: res.signType, //微信签名方式：
          paySign: res.paySign //微信签名
        },
        function(res) {
          // 使用以上方式判断前端返回,微信团队郑重提示：
          //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
          let result;
          if (res.err_msg == "get_brand_wcpay_request:ok") {
            result = true;
          } else {
            result = false;
          }
          //根据微信回调判断是否支付成功并跳转到相应的页面
          that.$router.push({
            name: "paySuccess",
            query: {
              result: result,
              type: that.$route.query.type,
              billId: that.$route.query.billId,
              contractId: that.$route.query.contractId
            }
          });
        }
      );
    },

    //立即支付 微信外部支付
    async payInNoWx() {
      let userAgentList = this.whichdevice(); //判断机型与平台
      let parmas = {
        billId: this.$route.query.billId,
        contractId: this.$route.query.contractId,
        pageType: `${this.$route.query?.specialType}-CI${this.$route.query.contractId}-BI${this.$route.query.billId}` //页面从哪来的，回调去那个详情页面查询：（格式） 页面类型-合同ID-账单ID
      };
      //预下单接口需要增加 客户端类型
      parmas.client = userAgentList.android
        ? "Android"
        : userAgentList.ios
        ? "iOS"
        : "Wap";

      let res =
        this.$route.query.type == 1
          ? await contractPay(parmas)
          : await payh5(parmas); //根据type判断是合同还是账单下单
      userAgentList.ios && (this.show = true); //单独处理ios 成功失败回调后没有重新进入，导致creat里面不显示弹框的问题
      sessionStorage.setItem("pay", true);
      res && (window.location.href = res);
    },

    onleftClick() {
      if (this.$route.query.type == 2) {
        this.$router.push({
          name: "billDetail",
          query: {
            billId: this.$route.query.billId
          }
        });
      } else {
        this.$router.push({
          name: "contractDetail",
          query: {
            contractId: this.$route.query.contractId
          }
        });
      }
    },

    //获取设备信息
    whichdevice() {
      var device = {};
      var ua = navigator.userAgent;
      var android = ua.match(/(Android);?[\s]+([\d.]+)?/);
      var ipad = ua.match(/(iPad).*OS\s([\d_]+)/);
      var ipod = ua.match(/(iPod)(.*OS\s([\d_]+))?/);
      var iphone = !ipad && ua.match(/(iPhone\sOS)\s([\d_]+)/);
      device.ios = device.android = device.iphone = device.ipad = device.androidChrome = false;
      // Android
      if (android) {
        device.os = "android";
        device.osVersion = android[2];
        device.android = true;
        device.androidChrome = ua.toLowerCase().indexOf("chrome") >= 0;
      }
      if (ipad || iphone || ipod) {
        device.os = "ios";
        device.ios = true;
      }
      // iOS
      if (iphone && !ipod) {
        device.osVersion = iphone[2].replace(/_/g, ".");
        device.iphone = true;
      }
      if (ipad) {
        device.osVersion = ipad[2].replace(/_/g, ".");
        device.ipad = true;
      }
      if (ipod) {
        device.osVersion = ipod[3] ? ipod[3].replace(/_/g, ".") : null;
        device.iphone = true;
      }
      // iOS 8+ changed UA
      if (device.ios && device.osVersion && ua.indexOf("Version/") >= 0) {
        if (device.osVersion.split(".")[0] === "10") {
          device.osVersion = ua
            .toLowerCase()
            .split("version/")[1]
            .split(" ")[0];
        }
      }
      // Webview
      device.webView =
        (iphone || ipad || ipod) && ua.match(/.*AppleWebKit(?!.*Safari)/i);
      // keng..
      device.isWeixin = /MicroMessenger/i.test(ua);
      return device;
    },
    /**
     * 生成合同
     */
    async generateContract() {
      const params = {
        id: Number(this.$route.query.billId),
        isSystem: true, //是否是系统确认
        isOtherPay: this.type === "other" ? "1" : "0"
      };
      this.generateLoading = true;
      try {
        await confirmBill(params);
        Toast.success("合同生成成功");
        // 如果是对公转账，需要把合同标记为对公转账，被标记为对公转账的合同不会被后续定时任务删除
        if (this.payModeType == 2) {
          await saveBillVoucher({
            id: this.$route.query.billId,
            paymentMode: 2
          });
        }
        // 查询合同id，跳转到合同详情
        await this.getH5BillDetail();
        if (this.contractDetail.id) {
          this.$router.push({
            name: "contractDetail",
            query: {
              contractId: this.contractDetail.id,
              billId: this.$route.query.billId,
              token: this.$route.query.token,
              isLogin: "1",
              specialType: "99" //随便定义的值，用来判断是否是从费用中心续费页面跳转过来的
            }
          });
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.generateLoading = false;
      }
    },
    async getH5BillDetail() {
      try {
        let res = await h5BillDetail({
          billId: this.$route.query.billId,
          isDetail: 0,
          isContract: 1
        });
        this.contractDetail = res.contractPayDetail || {};
      } catch (error) {
        console.log(error);
      }
    },

    // 获取对公转账账户列表
    async getCustomerAccountList() {
      try {
        let res = await getCustomerAccountList({
          type: 1
        });
        this.customerAccountList = res || [];
        if (this.customerAccountList.length > 0) {
          this.selectedAccount = this.customerAccountList[0];
          this.accountList = this.customerAccountList; // 保持兼容性
        }
        console.log(res, "res");
      } catch (error) {
        Toast.fail({
          message: error.result,
          duration: 2000
        });
      }
    },

    // 处理短信验证码输入
    handleSmsInput(index) {
      // 自动跳到下一个输入框
      if (this.smsCode[index] && index < 5) {
        this.$nextTick(() => {
          this.$refs.smsInputs[index + 1].focus();
        });
      }
    },

    // 处理键盘按键
    handleKeyDown(event, index) {
      // 处理删除键
      if (event.key === "Backspace" && !this.smsCode[index] && index > 0) {
        this.$nextTick(() => {
          this.$refs.smsInputs[index - 1].focus();
        });
      }
    },

    // 发送短信验证码
    async sendSmsCode() {
      // 这里添加发送短信验证码的API调用
      let res = await getVerifyCode({
        phone: this.cellphone,
        amount: this.billDetail.amount
      });
      console.log(res, "res");
      // 清除可能存在的旧定时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
      }

      // 记录开始时间
      const startTime = Date.now();
      this.countdown = 60;

      // 开始倒计时，使用更精确的计时方式
      this.countdownTimer = setInterval(() => {
        // 计算已经过去的秒数
        const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);
        this.countdown = 60 - elapsedSeconds;

        if (this.countdown <= 0) {
          clearInterval(this.countdownTimer);
          this.countdown = 0;
        }
      }, 1000);

      Toast("验证码已发送");
    },

    // 确认短信验证码
    async confirmSmsCode() {
      console.log(1111);

      const verificationCode = this.smsCode.join("");
      console.log(verificationCode);

      try {
        // 这里添加验证短信验证码的API调用
        let res = await payCustomerAccount({
          phone: this.cellphone,
          verifyCode: verificationCode,
          contractId: this.$route.query.contractId,
          cardNumber: this.selectedAccount.cardNumber,
          amount: this.billDetail.amount
        });
        console.log(res, "res");

        // 假设验证成功
        Toast.success("支付成功");
        this.balancePay = false;

        // 更新支付状态
        if (this.$route.query.type == 1) {
          this.getContractDetailForH5();
        } else {
          this.h5BillDetail();
        }

        // 跳转到合同详情页
        this.$router.push({
          name: "contractDetail",
          query: {
            contractId: this.$route.query.contractId,
            token: this.$route.query.token
          }
        });
      } catch (error) {
        Toast.fail({
          message: error.result,
          duration: 2000
        });
      }
    },

    // 选择账户
    selectAccount(account) {
      console.log("选择账户:", account);
      this.selectedAccount = JSON.parse(JSON.stringify(account)); // 深拷贝确保引用不同
      this.showPayAccount = false;
    },

    // 获取当前客户信息
    async getCurrentCustomer() {
      try {
        let res = await getCurrentCustomer();
        console.log(res, "res");
        this.cellphone = res.cellphone;
      } catch (error) {
        Toast.fail({
          message: error.result,
          duration: 2000
        });
      }
    }
  }
};
</script>

<style scoped lang="scss">
/deep/ .van-nav-bar .van-icon {
  color: #333;
  font-size: 18px;
}

.myput {
  ::v-deep .van-dialog .van-dialog__message {
    text-align: left !important;
  }
}

.order-pay {
  height: 100%;
  width: 100%;
  background: #ffffff;
  .message-box {
    padding: 0 16px;
    .message-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 48px;
      label {
        font-weight: 400;
        font-size: 14px;
        color: #7f7f7f;
        line-height: 26px;
      }
      span {
        font-size: 14px;
        color: #000000;
        line-height: 26px;
        font-weight: 400;
      }
    }
  }
  .bill-name {
    color: #000000;
    font-weight: bold;
    box-sizing: border-box;
    margin: 20px auto;
    text-align: center;
  }

  /deep/.van-nav-bar__title {
    width: 111px;
    height: 24px;
    font-family: PingFangSC-Semibold;
    font-weight: bold;
    font-size: 17px;
    color: #333333;
    text-align: center;
  }
  .money {
    font-size: 40px;
    color: #000000;
    font-family: D-DIN-PRO-SNaNpxiBold;
    line-height: 48px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 9px;
    .sepr {
      font-size: 20px;
      color: #000000;
    }
  }
  .pay-text {
    text-align: center;
    font-size: 14px;
    color: #333333;
    line-height: 22px;
    margin-bottom: 32px;
  }

  .code-way {
    width: 60%;
    margin: 0 auto;
    .code-tip {
      font-family: PingFangSC-Regular;
      font-size: 13px;
      color: #ff1111;
      letter-spacing: -0.31px;
      margin: 5px auto;
    }
    .code-img {
      width: 100%;
      -webkit-touch-callout: none;
    }
  }
  .wx-way {
    margin: 0 16px;

    .wx-way-title {
      font-size: 12px;
      color: #333333;
      line-height: 20px;
      margin-top: 28px;
      margin-bottom: 8px;
    }
    .wx-way-item {
      // background: rgba(0, 0, 0, 0.03);
      border-radius: 6px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 0 15px 0;
      height: 8px;
      margin-bottom: 5px;
      .item-left {
        display: flex;
        align-items: center;
        .wx-pay-img {
          width: 20px;
          margin-right: 10px;
        }
        .way-name {
          font-family: PingFangSC-Regular;
          font-size: 13px;
          color: rgba(0, 0, 0, 0.5);
          letter-spacing: -0.36px;
        }
      }
      .wx-true-img {
        width: 20px;
      }
    }
  }
  .box-btn-code-pay {
    height: 38px;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 32px;
    width: 100%;
    background-color: #f5f1ef;
  }
  .code-btn {
    margin: auto;

    border: 1px solid #ff5300;
    border-radius: 19px;
    width: 160px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Regular;
    font-size: 15px;
    color: #ff5300;
    letter-spacing: -0.36px;
  }
  .code-diasbled {
    color: rgba(0, 0, 0, 0.5);
    border-color: rgba(0, 0, 0, 0.5);
  }

  .pay-btn-box {
    width: 100vw;
    height: 52px;
    background: #ffffff;
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .wx-pay-btn {
      width: 343px;
      height: 38px;
      background: #ff9900;
      border-radius: 6px;
      margin: 0 auto;
      color: #fff;
    }
  }
}
.pay-result {
  p {
    text-align: center;
    padding: 5px 10px;
    font-size: 14px;
  }
  .title {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #f2f2f2;
  }

  .success {
    height: 30px;
    line-height: 30px;
    border-bottom: 1px solid #f2f2f2;
    color: #ff1111;
  }

  .faild {
    height: 35px;
    line-height: 35px;
    color: #ccc;
  }

  .footer-box1 {
    text-align: center;
    margin: 10px 0;
    .btn1,
    .btn2 {
      border-radius: 5px;
    }
    .btn1 {
      margin-right: 10px;
      width: 90px;
    }
  }
}

.pop_item {
  height: 35px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}

.sms-verify-popup {
  padding: 20px;
  box-sizing: border-box;
  height: 238px;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    span {
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      font-size: 16px;
      color: #000000;
    }
  }

  .verify-code-container {
    margin-bottom: 20px;

    .verify-code-input {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;

      input {
        width: 40px;
        height: 40px;
        border: 1px solid #ddd;
        border-radius: 4px;
        text-align: center;
        font-size: 18px;
        -webkit-appearance: none;
        appearance: none;
        padding: 0;
        margin: 0;
        background-color: #fff;
        caret-color: #ff9900;

        &::-webkit-inner-spin-button,
        &::-webkit-outer-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }

        -webkit-tap-highlight-color: transparent;

        -webkit-text-size-adjust: 100%;

        &:focus {
          border-color: #ff9900;
          outline: none;
          transition: border-color 0.2s ease;
        }
      }
    }

    .resend-code {
      text-align: center;
      font-size: 14px;
      color: #999;

      .resend-btn {
        color: #ff9900;
      }
    }
  }

  .confirm-btn {
    display: flex;
    justify-content: center;
    /deep/ .van-button {
      background-color: #ff9900;
      border-color: #ff9900;
      height: 40px;
      font-size: 16px;
      width: 132px;
      border-radius: 6px;
    }
  }
}

.popChange {
  display: flex;
  flex-direction: column;
  align-items: center;
  .header {
    width: 100%;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    box-sizing: border-box;

    p {
      height: 24px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: #000000;
      line-height: 24px;
      text-align: center;
      flex: 1;
    }
    img {
      width: 16px;
      height: 16px;
    }
  }

  .item {
    position: relative;
    width: 343px;
    height: 56px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    padding: 17px 16px;
    box-sizing: border-box;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    line-height: 22px;
    margin-bottom: 16px;

    .item_footer {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 24px;
      height: 24px;
      background: #ff9900;
      border-radius: 6px 0 6px 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .active {
    border: 1px solid #ff9900;
    color: #ff9900;
  }
}

@supports (-webkit-touch-callout: none) {
  .verify-code-input input {
    line-height: 40px;
    font-size: 16px;
  }
}

::v-deep .van-cell__title {
  flex: none;
}
</style>
