<template>
  <van-popup
    v-model="show"
    position="right"
    :style="{ height: '100%', width: '100%' }"
  >
    <div class="personPopup">
      <div class="header">
        <img src="../../../assets/img/ico_back-t.png" @click="show = false" />
        <div class="title">选择人员</div>
        <div></div>
      </div>

      <div class="search">
        <van-search
          v-model="name"
          placeholder="请输入姓名"
          @input="handleinput"
        />
      </div>

      <div class="checkPerson" v-if="personCheck && personCheck.length > 0">
        <div style="margin-bottom: 8px;">已选人员：</div>
        <van-tag
          v-for="item in personCheck"
          :key="item.id"
          type="primary"
          closeable
          color="#FF9900"
          @close="personCheck.splice(personCheck.indexOf(item), 1)"
          >{{ item.showName }}</van-tag
        >
      </div>

      <main>
        <div class="list">
          <van-checkbox-group v-model="personCheck">
            <van-checkbox
              v-for="item in personList"
              :key="item.id"
              :name="item"
              checked-color="#FF9900"
              class="checked"
              >{{ item.showName }}</van-checkbox
            >
          </van-checkbox-group>
        </div>
      </main>

      <footer>
        <div>已选 {{ personCheck.length }} 人</div>

        <div>
          <van-button class="cancal" type="default" @click="show = false"
            >取消</van-button
          >
          <van-button class="btn" color="#FF9900" @click="sumbitPerson"
            >提交</van-button
          >
        </div>
      </footer>
    </div>
  </van-popup>
</template>

<script>
import { getEnterpriseByLogin } from "@/api/advanceShip";
import {
  Popup,
  Checkbox,
  CheckboxGroup,
  button,
  Toast,
  Search,
  Tag
} from "vant";
export default {
  components: {
    [Popup.name]: Popup,
    [Checkbox.name]: Checkbox,
    [CheckboxGroup.name]: CheckboxGroup,
    [button.name]: button,
    [Toast.name]: Toast,
    [Search.name]: Search,
    [Tag.name]: Tag
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      personCheck: [], //已选人数
      personList: [], //人员列表
      name: ""
    };
  },

  watch: {
    value(val) {
      this.show = val;
      this.getEnterpriseByLogin();
    },
    show(val) {
      this.$emit("input", val);
      if (!val) {
        this.personCheck = [];
        this.name = "";
      }
    }
  },

  methods: {
    // 模糊查询
    handleinput(val) {
      this.getEnterpriseByLogin(val);
    },
    // 获取人员列表
    async getEnterpriseByLogin(val) {
      try {
        const res = await getEnterpriseByLogin({
          type: 1,
          name: val || "",
          pageNumber: 1,
          pageSize: 50
        });
        this.personList = res.data || [];
      } catch (error) {
        console.log(error);
      }
    },

    // 加签人员提交
    sumbitPerson() {
      let assigns = this.personCheck.map((item) => {
        return item.showName;
      });
      this.$emit("sumbitPerson", assigns?.join(","));
      this.show = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.personPopup {
  height: 100%;

  display: flex;
  flex-direction: column;
  overflow: hidden;
  .header {
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    flex-shrink: 0;
    background-color: #fff;

    img {
      width: 20px;
      height: 20px;
    }

    .title {
      width: 96px;
      height: 24px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #000000;
      text-align: center;
      line-height: 24px;
    }
  }

  .search {
    padding: 8px 16px;
    input {
      height: 33px;
      width: 100%;
      background: #f7f7f7;
      border: none;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 15px;
      color: #7f7f7f;
      border-radius: 4px;
    }

    input::-webkit-input-placeholder {
      color: #a1a1a1;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 15px;
      color: #b2b2b2;
      letter-spacing: 0;
      text-align: left;
      padding-left: 12px;
    }
  }

  .checkPerson {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #7f7f7f;

    .van-tag {
      margin:0 8px 8px 0;
    }
  }

  main {
    flex: 1;
    padding: 0 16px;
    background-color: #fff;
    border-radius: 4px;
    overflow: auto;

    .list {
      .checked {
        height: 56px;
        display: flex;
        align-items: center;
        font-size: 14px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  footer {
    height: 48px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    background-color: #fff;
    font-size: 13px;
    display: flex;
    justify-content: space-between;

    .btn {
      width: 64px;
      height: 38px;
      border: 0.5px solid #e5e5e5;
      border-radius: 6px;
    }

    .cancal {
      width: 64px;
      height: 38px;
      border: 0.5px solid #e5e5e5;
      border-radius: 6px;
      margin-right: 16px;
    }
  }
}

::v-deep .van-checkbox__icon .van-icon {
  font-size: 14px;
}
</style>