.receivable_list {
  width: 343px;
  flex: 1;
  background: #ffffff;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .table_header {
    display: flex;
    height: 42px;
    padding: 0 16px;
    line-height: 42px;
    justify-content: space-between;
    font-weight: 400;
    font-size: 13px;
    color: #7f7f7f;
    letter-spacing: 0;

    div {
      &:nth-child(1) {
        width: 40%;
        text-align: left;
      }
      &:nth-child(2) {
        width: 30%;
        text-align: right;
      }
      &:nth-child(3) {
        width: 30%;
        text-align: right;
      }
    }
  }

  .table_body {
    flex: 1;
    overflow: auto;
    .table_content {
      display: flex;
      min-height: 44px;
      padding: 0 16px;
      font-family: PingFangSC-Medium;
      font-weight: 550;
      font-size: 14px;
      color: #333333;
      letter-spacing: 0;
      align-items: center;
      justify-content: space-between;

      .table_title {
        width: 40%;
        text-align: left;
        // 超出部分隐藏
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .table_arrears {
        width: 30%;
        text-align: right;
      }
      .table_overdue {
        width: 30%;
        text-align: right;
      }
    }
    // 激活状态
    .active {
      background: #fafafa;
    }
  }
}
