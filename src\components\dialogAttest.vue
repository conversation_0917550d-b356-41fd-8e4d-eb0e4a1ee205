<template>
  <van-dialog v-model="show" :show="show" :showConfirmButton="false">
    <div slot="title" class="title-name">
      实名认证
      <img
        @click="show = false"
        src="https://ovopark.oss-cn-hangzhou.aliyuncs.com/2023/12/15/ico_close.png"
        alt=""
      />
    </div>
    <div class="dialog-box">
      <div class="dialog-title">为了您的资金安全，请您完成实名认证</div>
      <div class="dialog-title title-color">仅用于付款方身份确认</div>
      <div class="dialog-box-btn">
        <p @click="attestation(1)">个人实名</p>
        <p @click="attestation(2)">企业实名</p>
      </div>
    </div>
  </van-dialog>
</template>
  
  <script>
// components
import { Dialog } from "vant";
export default {
  components: {
    [Dialog.Component.name]: Dialog.Component
  },
  props: {
    showDialog: {
      type: Boolean,
      default: false
    },
    customerBankId: {
      type: [String, Number],
      default: ""
    },
    sourcePage: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      show: false
    };
  },
  watch: {
    showDialog(val) {
      this.show = val;
    }
  },
  methods: {
    // 认证
    attestation(val) {
      const routeName = val == 1 ? "personAuthent" : "groupAuthent";
      this.$router.push({
        name: routeName,
        query: {
          contractId: this.$route.query?.contractId,
          orderNo: this.$route.query?.orderNo,
          customerBankId: this.customerBankId,
          sourcePage: this.sourcePage
        }
      });
    }
  }
};
</script>
  
<style lang="scss" scoped>
.title-name {
  height: 24px;
  font-family: PingFangSC-SNaNpxibold;
  font-weight: 600;
  font-size: 17px;
  color: #000000;
  text-align: center;
  position: relative;
  margin-bottom: 33px;

  img {
    width: 16px;
    height: 16px;
    position: absolute;
    right: 16px;
  }
}
.dialog-box {
  // height: 200px;
  padding: 0 16px 22px 16px;

  .dialog-title {
    margin-top: 8px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: left;
  }

  .title-color {
    color: #7f7f7f;
  }

  .dialog-box-btn {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
    p {
      width: 122px;
      height: 40px;
      background: #ffffff;
      border-radius: 8px;
      color: #ff9900;
      font-weight: 400;
      border: 1px solid #e5e5e5;
      font-family: PingFangSC-SNaNpxibold;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .whyLogin {
    margin: 12px 0;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #02a7f0; //蓝色
    text-align: center;
  }
}
</style>