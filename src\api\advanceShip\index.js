import urls from "./config";
import { get, postJson, deleteReq } from "../request";

export function getEarlyOrderList(params = {}) {
  return get(urls.getEarlyOrderList, params);
}

export function getEarlyOrderDetail(params = {}) {
  return get(urls.getEarlyOrderDetail + "/" + params);
}

export function getContractDetail(params = {}) {
  return get(urls.getContractDetail + "/" + params);
}

export function saveEarlyApply(params = {}) {
  return postJson(urls.saveEarlyApply, params);
}

export function getEnterpriseByLogin(params = {}) {
  return get(urls.getEnterpriseByLogin, params);
}

export function earlyOrderExamine(params = {}) {
  return postJson(urls.earlyOrderExamine, params);
}

// 取消提前发货
export function cancelEarlyApply(params = {}) {
  return get(urls.cancelEarlyApply, params);
}

// 删除提前发货撤销变更单
export function removeEarlyApply(params = {}) {
  return deleteReq(urls.removeEarlyApply, params);
}
