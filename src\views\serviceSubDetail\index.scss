.servicePage {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f7f7;

  main {
    min-height: 0;
    flex: 1;
    overflow: auto;

    .price-presentation {
      .presentation {
        width: 375px;
        height: 375px;
      }
      .price {
        background-color: #fff;
        padding: 12px 16px;
        color: #fff;

        .price-top {
          background: #ff67001a;
          border-radius: 6px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          color: #ff6700;

          .unit {
            width: 10px;
            height: 24px;
            font-family: PingFangSC-SNaNpxibold;
            font-weight: 600;
            font-size: 16px;
            color: #ff6700;
            text-align: center;
            line-height: 24px;
          }

          .price-num {
            height: 36px;
            font-family: PingFangSC-SNaNpxibold;
            font-weight: 600;
            font-size: 28px;
            color: #ff6700;
            line-height: 36px;
          }
        }

        .text {
          text-align: right;
          font-family: PingFangSC-SNaNpxibold;
          text-align: right;
          font-weight: 500;
          font-size: 14px;

          p {
            font-weight: 600;
            font-size: 16px;
          }
        }

        .product-name {
          padding: 12px 0 0 16px;
          // height: 24px;
          font-family: PingFangSC-SNaNpxibold;
          font-weight: 600;
          font-size: 16px;
          color: #000000;
          line-height: 24px;
        }
      }

      // 未优惠
      .undiscounted {
        background-color: #fff;
        padding: 12px 16px;

        .price-top {
          margin-bottom: 8px;
          color: #ff6700;
          .unit {
            width: 10px;
            height: 24px;
            font-family: PingFangSC-SNaNpxibold;
            font-weight: 600;
            font-size: 16px;
            color: #ff505a;
            text-align: center;
            line-height: 24px;
          }
          .price-num {
            width: 53px;
            height: 36px;
            font-family: PingFangSC-SNaNpxibold;
            font-weight: 600;
            font-size: 28px;
            color: #ff505a;
            line-height: 36px;
          }
        }

        .product-name {
          // width: 96px;
          // height: 24px;
          font-family: PingFangSC-SNaNpxibold;
          font-weight: 600;
          font-size: 16px;
          color: #000000;
          line-height: 24px;
        }
      }
    }

    .select {
      margin: 6px 0;
      background: #ffffff;
      padding: 16px;
      display: flex;
      justify-content: space-between;

      p {
        height: 22px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #000000;
        line-height: 22px;
      }

      .icon {
        width: 16px;
        height: 16px;
      }
    }

    .product-detail {
      background-color: #fff;
      padding: 0 16px 33px;

      p {
        position: relative;
        width: 28px;
        height: 44px;
        font-family: PingFangSC-Regular;
        font-weight: 500;
        font-size: 14px;
        color: #000000;
        line-height: 44px;
        margin-left: 8px;

        &::before {
          content: "";
          position: absolute;
          left: -8px;
          top: 15px;
          background-color: #ff9900;
          width: 3px;
          height: 14px;
        }
      }

      .product-detail-img {
        width: 343px;
        height: 100%;
        margin: 12px 0;
      }

      .detail-txt {
        width: 343px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        letter-spacing: 0;
        line-height: 22px;
      }
    }
  }
  footer {
    height: 56px;
    background-color: #fff;
    border-top: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    justify-content: center;

    button {
      background: none;
      color: inherit;
      border: none;
      padding: 0;
      font: inherit;
      cursor: pointer;
      outline: inherit;
    }

    .btn {
      color: #fff;
      width: 343px;
      height: 40px;
      background: #ff9900;
      border-radius: 8px;
    }
  }
}

.pop {
  height: 100%;
  background: #f7f6f8;
  display: flex;
  flex-direction: column;

  .popHeader {
    max-height: 144px;
    padding: 0 16px 14px 16px;
    background: #ffffff;
    border-radius: 12px 12px 0 0;
    display: flex;
    flex-direction: column;
    margin-bottom: 6px;

    .popHeader-top {
      display: flex;
      align-items: center;
      height: 48px;
      justify-content: space-between;

      img {
        width: 20px;
        height: 20px;
      }
    }

    .address {
      position: relative;
      background: #ffffff;
      margin-bottom: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .address-txt {
        p {
          margin-bottom: 4px;
          height: 20px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #333333;
          letter-spacing: 0;
          line-height: 20px;
        }
        .content {
          height: 24px;
          font-family: PingFangSC-SNaNpxibold;
          font-weight: 600;
          font-size: 16px;
          color: #000000;
          letter-spacing: 0;
          line-height: 24px;
        }
      }
      .icon {
        width: 16px;
        height: 16px;
      }
    }
  }

  .popContent {
    flex: 1;
    background-color: #fff;
    .product {
      background: #ffffff;
      padding: 12px 16px;
      display: flex;

      .product-img {
        width: 80px;
        height: 80px;
      }

      .product-message {
        margin-left: 12px;

        p {
          margin-bottom: 4px;
        }

        .title {
          height: 22px;
          font-family: PingFangSC-SNaNpxibold;
          font-weight: 600;
          font-size: 14px;
          color: #000000;
          line-height: 22px;
        }

        .content {
          width: 245px;
          height: 20px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #000000cc;
          line-height: 20px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .price {
          height: 26px;
          font-family: PingFangSC-SNaNpxibold;
          font-weight: 600;
          font-size: 18px;
          color: #ff505a;
          line-height: 26px;
        }
      }
    }

    .setMeal {
      background-color: #fff;
      padding: 16px;

      p {
        height: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #000000;
        margin-bottom: 12px;
      }

      .set-product {
        display: flex;
        flex-wrap: wrap;

        .set-product-list {
          width: 145px;
          height: 32px;
          background: #f7f7f7;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          line-height: 20px;
          margin: 0 8px 8px 0;
        }

        .active-set-product-list {
          width: 145px;
          height: 32px;
          background: #ff99000f;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #ff9900;
          line-height: 20px;
          margin: 0 8px 8px 0;
        }
      }
    }
  }

  .popFooter {
    height: 56px;
    background-color: #fff;
    border-top: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    justify-content: center;

    button {
      background: none;
      color: inherit;
      border: none;
      padding: 0;
      font: inherit;
      cursor: pointer;
      outline: inherit;
    }

    .btn {
      color: #fff;
      width: 343px;
      height: 40px;
      background: #ff9900;
      border-radius: 8px;
    }
  }
}
