<template>
  <div></div>
</template>

<script>
import { orderSelect2 } from "@/api/serviceSubDetail";
export default {
  data() {
    return {};
  },
  mounted() {
    let { outTradeNo, pageType } = this.$route.query;
    console.log(outTradeNo, pageType, 6666);
    
    if (outTradeNo) {
      this.orderSelect2(outTradeNo);
    }
  },
  methods: {
    async orderSelect2(outTradeNo) {
      try {
        const res = await orderSelect2({
          outTradeNo
        });
        console.log(res, 6666);
        if (res.tradeState) {
          this.$router.push({
            name: "serviceFail",
            query: {
              tradeState: res.tradeState
            }
          });
        }
      } catch (error) {
        console.log(error);
      }
    }
  }
};
</script>

<style>
</style>