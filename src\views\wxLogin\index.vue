<template>
  <div class="wx-login">微信登录中...</div>
</template>

<script>
export default {
  data() {
    return {};
  },
  created() {
    const ENVIRONMENT = location.host.indexOf("ovopark") != -1; //false 测试服 true 正式服
    let suffix = ENVIRONMENT ? ".ovopark.com" : ".wandianzhang.com";
    let code = this.$route.query?.code;
    if (code) {
      window.location.href = `https://sign${suffix}/#/index?code=${code}`;
    }
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.wx-login {
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>