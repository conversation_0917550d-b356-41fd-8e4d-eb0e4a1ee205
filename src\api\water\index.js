import urls from "./config";
import { get, postJson,post } from "../request";

/**
 * 详细信息
 */
export function payMessageCenter(params = {}) {
  return get(urls.payMessageCenter, params);
}

export function paymentAdvancedQuery(params = {}) {
  return postJson(urls.paymentAdvancedQuery, params);
}

export function getPaymentInfoByContractId(params = {}) {
  return get(urls.getPaymentInfoByContractId, params);
}

export function relatedContract(params = {}) {
  return postJson(urls.relatedContract, params);
}

export function getPayBackApproveListBySerial(params = {}) {
  return get(urls.getPayBackApproveListBySerial, params);
}

export function approvePayment(params = {}) {
  return postJson(urls.approvePayment, params);
}

export function getPaymentDetail(params = {}) {
  return get(urls.getPaymentDetail, params);
}

export function clearPayCustomer(params = {}) {
  return post(urls.clearPayCustomer, params);
}
