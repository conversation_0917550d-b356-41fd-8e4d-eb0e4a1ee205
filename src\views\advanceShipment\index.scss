.ship-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f7f7f7;
  overflow: hidden;
  header {
    height: 44px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;

    .title {
      width: 64px;
      height: 24px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #000000;
      text-align: center;
      line-height: 24px;
    }

    img {
      width: 20px;
      height: 20px;
    }
  }
  .main-box {
    overflow: auto;
    flex: 1;
  }
  main {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 12px;

    .list {
      margin-top: 12px;
      width: 343px;
      background: #ffffff;
      border-radius: 6px;

      .list-top {
        border-bottom: 0.5px solid #e5e5e5;
        padding: 12px 16px;
        .list-top-no {
          height: 22px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;

          .contract {
            width: 117px;
            height: 22px;
            font-family: PingFangSC-Medium;
            font-weight: 550;
            font-size: 14px;
            color: #000000;
            line-height: 22px;
          }

          .status {
            width: 42px;
            height: 22px;
            font-family: PingFangSC-Medium;
            font-weight: 550;
            font-size: 14px;
            color: #208bee;
            text-align: right;
            line-height: 22px;
          }

          .statusTwo {
            width: 42px;
            height: 22px;
            font-family: PingFangSC-Medium;
            font-weight: 550;
            font-size: 14px;
            color: #52c41a;
            text-align: right;
            line-height: 22px;
          }

          .statusThree {
            width: 42px;
            height: 22px;
            font-family: PingFangSC-Medium;
            font-weight: 550;
            font-size: 14px;
            color: #ff0000;
            text-align: right;
            line-height: 22px;
          }

          .statusFour {
            width: 42px;
            height: 22px;
            font-family: PingFangSC-Medium;
            font-weight: 550;
            font-size: 14px;
            color: #7f7f7f;
            text-align: right;
            line-height: 22px;
          }
        }
        .list-top-content {
          height: 20px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 12px;
          color: #7f7f7f;
          line-height: 20px;
        }
      }

      .list-bottom {
        padding: 12px 16px;

        .item {
          display: flex;
          margin-bottom: 8px;
          font-size: 13px;

          label {
            width: 78px;
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            color: #7f7f7f;
            line-height: 20px;
            margin-right: 8px;
          }

          .content {
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 13px;
            color: #333333;
            line-height: 20px;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .popup {
    padding: 0 16px;
    height: 100%;
    display: flex;
    flex-direction: column;
    .header {
      height: 44px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      img {
        width: 20px;
        height: 20px;
      }

      .title {
        width: 96px;
        height: 24px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #000000;
        text-align: center;
        line-height: 24px;
      }
    }

    .main {
      flex: 1;
      .item {
        margin: 8px 0;

        p {
          height: 44px;
          line-height: 44px;
          font-family: PingFangSC-SNaNpxibold;
          font-weight: 600;
          font-size: 14px;
          color: #000000;
        }

        .v-input {
          height: 40px;
          background: #f7f7f7;
          border-radius: 6px;
          display: flex;
          align-items: center;
          padding: 0 12px;

          input {
            height: 33px;
            width: 100%;
            background: #f7f7f7;
            border: none;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 15px;
            color: #7f7f7f;
          }

          input::-webkit-input-placeholder {
            /* WebKit browsers，webkit内核浏览器 */
            color: #a1a1a1;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 15px;
            color: #b2b2b2;
            letter-spacing: 0;
          }
        }

        .item-date {
          display: flex;
          align-items: center;
          .v-date {
            width: 159px;
            height: 40px;
            background: #f7f7f7;
            border-radius: 6px;
            display: flex;
            align-items: center;
            padding: 0 12px;

            input {
              height: 33px;
              width: 88%;
              background: #f7f7f7;
              border: none;
              font-family: PingFangSC-Regular;
              font-weight: 400;
              font-size: 15px;
              color: #7f7f7f;
            }

            input::-webkit-input-placeholder {
              /* WebKit browsers，webkit内核浏览器 */
              color: #a1a1a1;
              font-family: PingFangSC-Regular;
              font-weight: 400;
              font-size: 15px;
              color: #b2b2b2;
              letter-spacing: 0;
              text-align: center;
            }
          }

          .line {
            width: 15px;
            height: 1px;
            background: #e5e5e5;
            margin: 0 6px;
          }
        }
      }

      .item-status {
        display: flex;
        flex-wrap: wrap;
        .item-list {
          box-sizing: border-box;
          width: 106px;
          height: 40px;
          background: #f7f7f7;
          border-radius: 6px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 16px;
          color: #7f7f7f;
          margin-right: 12px;
          margin-bottom: 12px;
        }

        .active-item-list {
          box-sizing: border-box;
          font-weight: 500;
          background: #ff99001a;
          color: #ff9900;
          border: 1px solid #ff9900;
        }
      }
    }

    .footer {
      width: 100%;
      height: 52px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .resect {
        width: 120px;
        height: 44px;
        background: #ffffff;
        border: 0.5px solid #e5e5e5;
        border-radius: 8px;
        font-size: 17px;
      }

      .sure {
        width: 215px;
        height: 44px;
        background: #ff9900;
        border-radius: 8px;
        font-size: 17px;
      }
    }
  }
}
