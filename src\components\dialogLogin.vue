<template>
  <van-dialog v-model="show" :show="showDialog" :showConfirmButton="false">
    <div slot="title" class="title-name">
      登录
      <img
        @click="show = false"
        src="https://ovopark.oss-cn-hangzhou.aliyuncs.com/2023/12/15/ico_close.png"
        alt=""
      />
    </div>
    <div class="dialog-box">
      <div class="dialog-title">为了您的资金安全，请登录后再付款</div>

      <div class="dialog-box-btn">
        <p @click="show = false">直接付款</p>
        <p @click="login" class="login">登录</p>
      </div>

      <div class="whyLogin" @click="viewVideo">实名操作流程？</div>
    </div>
  </van-dialog>
</template>
  
  <script>
// components
import { Dialog } from "vant";
export default {
  components: {
    [Dialog.Component.name]: Dialog.Component
  },
  props: {
    showDialog: {
      type: Boolean,
      default: false
    },
    sourcePage: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      show: false
    };
  },
  watch: {
    showDialog: {
      handler(val) {
        this.show = val;
      },
      immediate: true
    }
  },
  methods: {
    // 登录
    login() {
      let query = {
        sourcePage: this.sourcePage
      };

      if (this.sourcePage == "contractPage") {
        query.contractId = this.$route.query?.contractId;
      } else {
        query.orderNo = this.$route.query?.orderNo;
      }

      this.$router.push({
        name: "realNameAuth",
        query: query
      });
    },

    // 付款为什么要登录？
    viewVideo() {
      window.location.href =
        "https://ovopark.oss-cn-hangzhou.aliyuncs.com/2024/03/12/video.mp4";
    }
  }
};
</script>
  
<style lang="scss" scoped>
.title-name {
  height: 24px;
  font-family: PingFangSC-SNaNpxibold;
  font-weight: 600;
  font-size: 17px;
  color: #000000;
  text-align: center;
  position: relative;
  margin-bottom: 33px;

  img {
    width: 16px;
    height: 16px;
    position: absolute;
    right: 16px;
  }
}
.dialog-box {
  // height: 200px;
  padding: 0 16px 22px 16px;

  .dialog-title {
    margin-top: 8px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: left;
  }

  .dialog-box-btn {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
    p {
      width: 122px;
      height: 40px;
      background: #ff99000f;
      border-radius: 8px;
      color: #ff9900;
      font-weight: 400;
      font-family: PingFangSC-SNaNpxibold;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .login {
      background: #ff9900;
      color: #ffffff;
    }
  }

  .whyLogin {
    margin-top: 16px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #b2b2b2; //蓝色
    text-align: center;
  }
}
</style>