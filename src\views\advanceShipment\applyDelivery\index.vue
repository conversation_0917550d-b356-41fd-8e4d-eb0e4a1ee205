<template>
  <div class="applyDelivery">
    <div class="header">
      <img src="../../../assets/img/ico_back-t.png" @click="onClickLeft" />
      <div class="title">申请提前发货</div>
      <div></div>
    </div>

    <div class="main">
      <div class="title">
        <div class="title-no">
          <p>{{ contractList.orderNo || "" }}</p>
          <p>合同编号：{{ contractList.contractNo || "" }}</p>
        </div>

        <div class="title-name">
          <div class="title-name-one">
            <p>销售负责人</p>
            <p>{{ contractList.userName || "" }}</p>
          </div>
          <div class="title-name-two">
            <p>甲方名称</p>
            <p>{{ contractList.customerName || "" }}</p>
          </div>
        </div>
      </div>

      <div class="basic">
        <div class="basic-title">基础信息</div>

        <div class="item">
          <label for="">品牌名称</label>
          <div class="content">{{ contractList.straightClient || "" }}</div>
        </div>

        <div class="item">
          <label for="">签约时间</label>
          <div class="content">{{ contractList.signDate || "" }}</div>
        </div>

        <div class="item">
          <label for="">甲方代表</label>
          <div class="content">{{ contractList.signUser || "" }}</div>
        </div>

        <div class="item">
          <label for="">付款方式</label>
          <div class="content">
            {{ contractList.payType | payTypeFil(this) }}
          </div>
        </div>

        <div class="item">
          <label for="">合同来源</label>
          <div class="content">
            {{ contractList.contractSource | sourceFil(this) }}
          </div>
        </div>

        <div class="item">
          <label for="">门店名称</label>
          <div class="content">{{ contractList.customerRemark || "" }}</div>
        </div>

        <div class="item">
          <label for="">保修期</label>
          <div class="content">{{ contractList.warrantyTime || "" }}</div>
        </div>

        <div class="item">
          <label for="">对应门店数</label>
          <div class="content">{{ contractList.shopCount || "0" }}</div>
        </div>

        <div class="item">
          <label for="">项目情况说明(内部可见)</label>
          <div class="content">
            {{ contractList.projectRemark || "" }}
          </div>
        </div>
      </div>

      <div class="message">
        <div class="message-title">申请信息</div>
        <div class="message-box">
          <van-field
            v-model="earlyDeliverReason"
            rows="2"
            autosize
            type="textarea"
            placeholder="请输入申请信息"
          />
        </div>
        <div class="item">
          <label for="" class="must">预计回款时期</label>
          <div
            class="content"
            style="display: flex; align-items: center"
            @click="showDate = true"
          >
            {{ expectedPayTime }}
            <img
              style="width: 16px; height: 16px"
              src="../../../assets/img/ico_rightarrow.png"
            />
          </div>
        </div>
        <div class="item">
          <label for="">创建人</label>
          <div class="content">{{ username || "" }}</div>
        </div>
        <div class="item">
          <label for="">创建日期</label>
          <div class="content">
            {{ this.$moment(new Date()).format("YYYY-MM-DD") }}
          </div>
        </div>
      </div>
    </div>

    <div class="footer">
      <div class="radio">
        <div class="radio-box" @click="isChecked = !isChecked">
          <img
            v-if="isChecked"
            src="../../../assets/img/ico_selected.png"
            alt=""
          />
          <img v-else src="../../../assets/img/ico_unselect.png" alt="" />
        </div>
        <p>
          先发货申请，如有未按约定时间回款，销售责任人自行承担本次合同损失30%责任
        </p>
      </div>
      <div class="btn">
        <van-button class="resect" @click="backToApp()">取消</van-button>
        <van-button
          class="sure"
          :disabled="!isChecked"
          color="#FF9900"
          @click="saveEarlyApply"
          >提交</van-button
        >
      </div>
    </div>

    <!-- 选择日期 -->
    <van-popup
      round
      position="bottom"
      :style="{ height: '40%' }"
      v-model="showDate"
    >
      <van-datetime-picker
        v-model="currentDate"
        type="date"
        title="选择年月日"
        :columns-order="['year', 'month', 'day']"
        :formatter="formatter"
        @confirm="confirm"
        @cancel="showDate = false"
      />
    </van-popup>
  </div>
</template>
  
  <script>
import { Button, DatetimePicker, Field, Popup, Toast } from "vant";
import { getContractDetail, saveEarlyApply } from "../../../api/advanceShip";
import { getAllDict } from "../../../api/delivery";
import { findDeeply } from "../../../common/utils";
export default {
  components: {
    [Button.name]: Button,
    [DatetimePicker.name]: DatetimePicker,
    [Field.name]: Field,
    [Popup.name]: Popup,
    [Toast.name]: Toast
  },
  data() {
    return {
      id: "",
      username: "",
      isChecked: false,
      contractList: {},
      showDate: false,
      currentDate: new Date(),
      expectedPayTime: this.$moment(new Date()).format("YYYY-MM-DD"),
      earlyDeliverReason: "",
      payTypeList: [], //付款方式字典项
      cluingSourceFlatList: [] //合同来源字典项
    };
  },
  watch: {},
  created() {
    this.getAllDict();
  },
  mounted() {
    this.id = this.$route.query.id;
    this.username = this.$route.query.username;
    this.$route.query.id && this.getContractDetail(this.$route.query.id);
  },
  methods: {
    formatter(type, val) {
      if (type === "year") {
        return val + "年";
      }
      if (type === "month") {
        return val + "月";
      }
      if (type === "day") {
        return val + "日";
      }
      return val;
    },

    // 选择日期
    confirm() {
      this.showDate = false;
      this.expectedPayTime = this.$moment(this.currentDate).format(
        "YYYY-MM-DD"
      );
    },

    // 根据合同id获取合同详情
    async getContractDetail(id) {
      try {
        const res = await getContractDetail(id);
        this.contractList = res;
      } catch (error) {
        console.log(error);
      }
    },

    // 提前发货单保存提交
    async saveEarlyApply() {
      if (!this.earlyDeliverReason) {
        return Toast.fail("请输入申请信息！");
      }
      if (!this.expectedPayTime) {
        return Toast.fail("请选择预计回款时间！");
      }

      let params = {
        contractId: this.id, //合同Id
        earlyDeliverReason: this.earlyDeliverReason, //提前发货原因
        expectedPayTime: this.expectedPayTime //预计回款时间
      };
      try {
        await saveEarlyApply(params);
        Toast.success("提交成功！");
        this.backToApp();
      } catch (error) {
        console.log(error);
      }
    },

    onClickLeft() {
      this.backToApp();
    },

    /**
     * 全部字典项
     */
    async getAllDict() {
      const res = await getAllDict();
      if (res) {
        this.getOption(res);
      }
    },

    /**
     * 字典项
     */
    getOption(res) {
      const payTypeList = findDeeply(res, (item) => item.type === "pay_type");
      const cluingSourceFlatList = findDeeply(
        res,
        (item) => item.type === "contract_source"
      );
      this.payTypeList = [...payTypeList.children] || [];
      this.cluingSourceFlatList = [...cluingSourceFlatList.children] || [];
    }
  },

  filters: {
    payTypeFil(val, that) {
      if (val) {
        const obj =
          that.payTypeList.find((item) => {
            return item.value === val;
          }) || {};
        return obj.dname;
      }
    },
    sourceFil(val, that) {
      if (val) {
        const obj =
          that.cluingSourceFlatList.find((item) => {
            return item.value === val;
          }) || {};
        return obj.dname;
      }
    }
  }
};
</script>
  
  <style lang="scss" scoped>
.applyDelivery {
  display: flex;
  flex-direction: column;
  height: 100%;
  .header {
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    flex-shrink: 0;

    img {
      width: 20px;
      height: 20px;
    }

    .title {
      width: 96px;
      height: 24px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #000000;
      text-align: center;
      line-height: 24px;
    }
  }

  .main {
    flex: 1;
    background: #f7f7f7;
    padding: 16px;
    overflow: auto;
    .title {
      background: #fff;
      border-radius: 6px;
      margin-bottom: 12px;
      padding: 12px 16px;

      .title-no {
        border-bottom: 0.5px solid #e5e5e5;
        padding-bottom: 12px;
        :first-child {
          height: 26px;
          font-family: PingFangSC-Medium;
          font-weight: 550;
          font-size: 18px;
          color: #000000;
          line-height: 26px;
          margin-bottom: 4px;
        }
        :nth-child(2) {
          height: 22px;
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #7f7f7f;
          line-height: 22px;
        }
      }

      .title-name {
        border: 0.5px solid #e5e5e5;
        border-radius: 4px;
        margin-top: 12px;
        padding: 12px;

        .title-name-one,
        .title-name-two {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          :first-child {
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #7f7f7f;
            line-height: 20px;
          }

          :nth-child(2) {
            height: 20px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #333333;
            text-align: right;
            line-height: 20px;
          }
        }

        .title-name-two {
          margin-bottom: 0;
        }
      }
    }

    .basic {
      background: #fff;
      border-radius: 6px;
      margin-bottom: 12px;
      font-size: 13px;
      padding: 12px 16px;

      .basic-title {
        height: 48px;
        line-height: 48px;
        font-size: 16px;
        font-weight: 550;
      }

      .item {
        height: 40px;
        display: flex;
        align-items: center;

        label {
          width: 150px;
          color: #7f7f7f;
        }

        .content {
          flex: 1;
          text-align: right;
          // 超出部分显示省略号
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      label {
        color: #7f7f7f;
      }
    }

    .message {
      background: #fff;
      border-radius: 6px;
      padding: 0 16px;
      margin-bottom: 42px;

      .message-title {
        height: 48px;
        line-height: 48px;
        font-size: 16px;
        font-weight: 550;

        // 再标题后面加入必填标识符
        &:after {
          content: "*";
          color: #ff0000;
          padding-left: 3px;
        }
      }

      .message-box {
        border: 1px solid #e5e5e5;
        border-radius: 4px;
        margin-bottom: 12px;

        .van-cell {
          padding: 10px;
        }
      }

      .item {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 13px;
      }

      label {
        color: #7f7f7f;
      }
    }
  }

  .footer {
    .radio {
      padding: 8px 16px;
      background: #fff7ea;
      display: flex;
      .radio-box {
        width: 40px;
        img {
          width: 16px;
          height: 16px;
        }
      }

      p {
        flex: 1;
        height: 40px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #7f7f7f;
        line-height: 20px;
      }
    }

    .btn {
      padding: 0 16px;
      height: 52px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .resect {
        width: 116px;
        height: 38px;
        background: #ffffff;
        border: 0.5px solid #e5e5e5;
        border-radius: 8px;
        font-size: 14px;
      }

      .sure {
        width: 215px;
        height: 38px;
        background: #ff9900;
        border-radius: 8px;
        font-size: 14px;
      }
    }
  }
}

.must {
  &:after {
    content: "*";
    color: #ff0000;
    padding-left: 3px;
  }
}
</style>