<template>
  <div class="receivable_list">
    <!-- table header -->
    <div class="table_header">
      <div>组织架构</div>
      <div @click="sortArrearsSort()">
        欠款(万)
        <img style="width: 12px; height: 12px;" :src="activeIcon" alt="" />
      </div>
      <div @click="sortOverdueSortSort()">
        逾期(万)
        <img style="width: 12px; height: 12px;" :src="activeOverIcon" alt="" />
      </div>
    </div>
    <!-- content -->
    <div class="table_body" v-if="showDetail">
      <div
        class="table_content"
        :class="{ active: activeIndex === index }"
        v-for="(item, index) in detail.list"
        :key="item.id"
      >
        <div class="table_title">
          {{ item.customerName }}
        </div>
        <div class="table_arrears">
          {{ item.arrearsAmount }}
          <span style="display: inline-block; width: 12px;"></span>
        </div>
        <div class="table_overdue">
          {{ item.overdueMoney }}
        </div>
      </div>
    </div>

    <van-empty description="暂无数据" v-else />
  </div>
</template>
<script>
import { getNextCountOverdueBillByUser } from "@/api/delivery";
import { Empty, Toast } from "vant";
export default {
  components: {
    [Empty.name]: Empty,
    [Toast.name]: Toast
  },

  props: {},
  data() {
    return {
      activeIcon:
        "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/05/ico_sort1.png",
      activeOverIcon:
        "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/05/ico_sort1.png",
      detail: {},
      activeIndex: -1,
      showDetail: false,
      arrearsSort: "", //欠款排序 (1欠款金额正序，2欠款金额倒序)
      overdueSort: "" //逾期排序 (1逾期金额正序，2逾期金额正序)
    };
  },
  watch: {
    // 改为深度监听
    "$store.state.accountDate": {
      handler: function(val) {
        this.getNextCountOverdueBillByUser();
        console.log("watch中:", val);
      },
      deep: true //深度监听设置为 true
    },
    overdueSort(val) {
      if (!val) {
        this.activeOverIcon =
          "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/05/ico_sort1.png";
      }
    },
    arrearsSort(val) {
      if (!val) {
        this.activeIcon =
          "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/05/ico_sort1.png";
      }
    }
  },
  mounted() {
    this.getNextCountOverdueBillByUser();
  },

  methods: {
    // 应收账单下层级列表接口
    async getNextCountOverdueBillByUser() {
      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        loadingType: "spinner"
      });
      try {
        const res = await getNextCountOverdueBillByUser({
          type: this.$route.query.type,
          nextType: 3,
          userCode: this.$route.query.userCode, //负责人code（nextType为3必填）
          startDate: this.$store.state.accountDate.startDate,
          endDate: this.$store.state.accountDate.endDate,
          arrearsSort: this.arrearsSort,
          overdueSort: this.overdueSort
        });
        this.detail = res || {};
        this.showDetail = res?.list.length > 0;
        this.$store.commit("setArrearsAmount", res.arrearsAmount);
        this.$store.commit("setOverdueMoney", res.overdueMoney);
        Toast.clear();
      } catch (error) {
        console.log(error);
      }
    },

    // 欠款排序
    sortArrearsSort() {
      this.overdueSort = "";
      if (this.arrearsSort == "") {
        this.arrearsSort = 1;
      } else if (this.arrearsSort == 1) {
        this.arrearsSort = 2;
      } else if (this.arrearsSort == 2) {
        this.arrearsSort = 1;
      }
      if (this.arrearsSort == 1) {
        this.activeIcon =
          "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/09/ico_sort2.png";
      } else if (this.arrearsSort == 2) {
        this.activeIcon =
          "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/09/ico_sort3.png";
      }

      this.getNextCountOverdueBillByUser();
    },

    // 逾期排序
    sortOverdueSortSort() {
      this.arrearsSort = "";
      if (this.overdueSort == "") {
        this.overdueSort = 1;
      } else if (this.overdueSort == 1) {
        this.overdueSort = 2;
      } else if (this.overdueSort == 2) {
        this.overdueSort = 1;
      }
      if (this.overdueSort == 1) {
        this.activeOverIcon =
          "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/09/ico_sort2.png";
      } else if (this.overdueSort == 2) {
        this.activeOverIcon =
          "https://ovopark-oss-dev.oss-cn-hangzhou.aliyuncs.com/ovopark-data-assets/211/2024/11/09/ico_sort3.png";
      }

      this.getNextCountOverdueBillByUser();
    }
  }
};
</script>
<style scoped lang="scss">
@import "../account.scss";
</style>
