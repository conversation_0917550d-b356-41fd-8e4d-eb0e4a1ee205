<template>
  <div class="grid">
    <div v-for="(item, i) in fileList" :key="i" class="grid-box">
      <div class="box-content-container">
        <div class="box-content">
          <video
            :src="item.fileUrl"
            v-if="
              item.fileType === 1 ||
                (item.fileUrl && /.mp4|.flv|.avi|.swf|.MP4/.test(item.fileUrl))
            "
            class="upload-img"
            @click="showVideo(item)"
          ></video>
          <img
            :src="item.fileUrl"
            @click="showBigImg(item)"
            class="upload-img"
            v-else
          />
          <div class="file-mask" v-show="item.progress !== 100">
            <span class="progress-text">{{ item.progress || 0 }}%</span>
          </div>
          <div class="del-box" @click="onHandleDel(item)">
            <img
              class="del-icon-box"
              :src="require('./<EMAIL>')"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 选择区域 -->
    <div
      class="grid-box upload-area"
      @click="onChoose"
      v-if="showUpload && fileList.length < max"
    >
      <div class="box-content-container">
        <div class="box-content">
          <img :src="require('./<EMAIL>')" class="add-icon" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    fileList: {
      type: Array,
      default() {
        return [];
      }
    },
    max: {
      type: Number
    },
    showUpload: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      visibles: false,
      url: "",
      videoUrl: ""
    };
  },
  methods: {
    onChoose() {
      if (this.disabled) return;
      this.$emit("on-choose");
    },
    onHandleDel(item) {
      this.$emit("on-del", item);
    },
    /**查看大图 */
    showBigImg(e) {
      this.url = e.fileurl || "";
      this.visible = true;
    },
    showVideo(e) {
      this.videoUrl = e.fileurl || "";
      this.visibles = true;
    }
  }
};
</script>

<style scoped lang="scss">
.grid {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
  // margin-right: -10px;

  .upload-box {
    display: flex;
    width: 140px;
    height: 140px;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    border: 1px solid #e1e1e1;
    color: #2d8cf0;
  }

  .upload-img {
    width: 100%;
    height: 100%;
  }

  .add-box {
    // display: flex;
    // height: 100%;
    // justify-content: center;
    // align-items: center;
    // border: 1px solid #e1e1e1;
    // border-radius: 6px;
    width: 11.2vw;
    height: 11.2vw;
    background: #ff9900;
    border-radius: 50%;
    color: #fff;
    font-size: 8.53333vw;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    margin-bottom: 2.13333vw;
  }

  .add-icon {
    width: 20px;
    height: 20px;
  }

  .grid-box {
    width: 100%;
    height: 100%;

    .box-content-container {
      width: 100%;
      height: 100%;

      .box-content {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 9px;
        position: relative;

        img {
          border-radius: 9px;
        }
      }

      .file-mask {
        display: flex;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.3);
      }

      .progress-text {
        font-size: 20px;
        color: #fff;
      }
    }
  }

  .del-box {
    position: absolute;
    top: 0;
    right: 0;

    .del-icon-box {
      width: 16px;
      height: 16px;
    }
  }
}
.mask {
  background-color: #a0a1a5;
  cursor: not-allowed;
}
</style>
