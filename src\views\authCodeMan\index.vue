<template>
  <div class="auth-page-container">
    <van-nav-bar
      title="授权码管理"
      left-arrow
      @click-left="onClickLeftNav"
      @click-right="onClickRightNav"
    >
      <template #right>
        <van-icon name="filter-o" size="20" />
      </template>
    </van-nav-bar>

    <!-- 筛选弹出层 -->
    <van-popup
      v-model="showFilter"
      position="right"
      :style="{ width: '100%', height: '100%' }"
    >
      <div class="filter-container">
        <div class="filter-header">
          <span></span>
          <span class="title">筛选</span>
          <van-icon name="cross" @click="showFilter = false" />
        </div>

        <div class="filter-content">
          <div class="filter-item">
            <div class="label">门店名称</div>
            <van-field
              v-model="filterForm.storeName"
              placeholder="请输入门店名称"
              clearable
            />
          </div>

          <div class="filter-item">
            <div class="label">显示名</div>
            <van-field
              v-model="filterForm.displayName"
              placeholder="请输入显示名"
              clearable
            />
          </div>

          <div class="filter-item">
            <div class="label">激活状态</div>
            <div class="status-options">
              <div
                class="status-btn"
                :class="{ active: filterForm.activeStates.includes('1') }"
                @click="toggleActiveState('1')"
              >
                已激活
              </div>
              <div
                class="status-btn"
                :class="{ active: filterForm.activeStates.includes('0') }"
                @click="toggleActiveState('0')"
              >
                未激活
              </div>
            </div>
          </div>
        </div>

        <div class="filter-footer">
          <van-button plain block @click="resetFilter">重置</van-button>
          <van-button 
            type="primary" 
            block 
            @click="confirmFilter"
            :disabled="!hasFilterConditions"
          >确认</van-button>
        </div>
      </div>
    </van-popup>

    <!-- 使用组件 -->
    <store-selector
      :visible.sync="showStoreSelector"
      :current-software-id="currentTabType"
      @confirm="handleStoreConfirm"
    />

    <account-selector
      :visible.sync="showAccountSelector"
      :current-software-id="currentTabType"
      @confirm="handleAccountConfirm"
    />

    <div class="main-layout">
      <van-sidebar
        v-model="activeTab"
        @change="onTabChange"
        class="sidebar-nav"
      >
        <van-sidebar-item v-for="(tab, index) in tabs" :key="index">
          <template #title>
            <div class="tab-title-custom">
              <div>{{ tab.title }}</div>
              <div class="tab-counts">
                {{ tab.currentCount }}/{{ tab.totalCount }}
              </div>
            </div>
          </template>
        </van-sidebar-item>
      </van-sidebar>

      <div class="content-area">
        <van-loading
          v-if="tabs.length === 0 && !isError && !loading"
          type="spinner"
          size="24px"
          vertical
          >加载中...</van-loading
        >

        <div v-if="isError" class="error-message">
          <van-icon name="warning-o" size="32" color="#ee0a24" />
          <p>加载失败，请重试</p>
          <van-button size="small" type="primary" @click="fetchSoftwareTypes"
            >重新加载</van-button
          >
        </div>

        <van-list
          v-model="loading"
          :finished="finished"
          :finished-text="currentItems.length > 0 ? '没有更多了' : ''"
          @load="onLoadItems"
          class="auth-list"
        >
          <div
            v-for="(item, index) in currentItems"
            :key="item.id + index"
            class="auth-item-card"
          >
            <div class="item-id">{{ item.authorizationCode }}</div>
            <div class="item-details">
              <p>到期日期: {{ item.expirationTime }}</p>
              <p>
                {{ item.accountFlag ? "显示名:" : "门店名称:" }}
                {{ item.accountFlag ? item.username : item.deptName }}
              </p>
              <div class="action-button-container">
                <p>有效月数: {{ item.activeMonthNum }}</p>

                <van-button
                  v-if="item.source != 2"
                  :type="item.activeState == 1 ? 'default' : 'primary'"
                  size="small"
                  class="action-button"
                  @click="handleItemAction(item)"
                >
                  {{ item.activeState == 1 ? "变更" : "激活" }}
                </van-button>
              </div>
            </div>
          </div>
          <div
            v-if="!loading && currentItems.length === 0 && finished"
            class="empty-data"
          >
            暂无数据
          </div>
        </van-list>
      </div>
    </div>
  </div>
</template>

<script>
import {
  NavBar,
  Icon,
  Sidebar,
  SidebarItem,
  List,
  Button,
  Toast,
  Popup,
  Field,
  Loading
} from "vant";
import StoreSelector from "./components/StoreSelector.vue";
import AccountSelector from "./components/AccountSelector.vue";
import {
  getAuthCodeSoftwareTypeByH5,
  getAuthCodePageByTypeForH5,
  activeAuthorizationCodeByList,
  changeDeptAuthorizationCodeByList,
  activeAccountAuthorizationCodeByList,
  changeAccountAuthorizationCodeByList
} from "@/api/authCode";

export default {
  name: "AuthorizationPage",
  components: {
    VanNavBar: NavBar,
    VanIcon: Icon,
    VanSidebar: Sidebar,
    VanSidebarItem: SidebarItem,
    VanList: List,
    VanButton: Button,
    VanPopup: Popup,
    VanField: Field,
    StoreSelector,
    AccountSelector,
    VanLoading: Loading
  },
  data() {
    return {
      activeTab: 0, // 默认激活第一个标签
      tabs: [], // 将从API获取软件类型数据
      allItems: {}, // 将从API获取授权码数据
      currentItems: [],
      loading: false,
      finished: false,
      currentPage: 1, // 用于分页加载
      itemsPerPage: 10, // 每页加载数量
      showFilter: false,
      filterForm: {
        storeName: "",
        displayName: "",
        status: "",
        activeStates: [] // 使用数组存储多选的激活状态
      },
      // 选择门店相关
      showStoreSelector: false,
      // 选择账号相关
      showAccountSelector: false,
      currentChangingItem: null,
      // 新增API数据相关
      numsMap: {}, // 存储API返回的numsMap数据
      list: {}, // 存储API返回的list数据
      isError: false // API请求错误状态
    };
  },
  computed: {
    currentTabType() {
      return this.tabs[this.activeTab]?.type;
    },
    hasFilterConditions() {
      return (
        this.filterForm.storeName.trim() !== "" || 
        this.filterForm.displayName.trim() !== "" || 
        this.filterForm.activeStates.length > 0
      );
    }
  },
  watch: {
    activeTab() {
      this.resetAndLoadItems();
    }
  },
  created() {
    this.fetchSoftwareTypes(); // 获取软件类型数据
  },
  methods: {
    // 获取软件类型数据
    async fetchSoftwareTypes() {
      try {
        this.loading = true;
        const response = await getAuthCodeSoftwareTypeByH5();

        // 处理API返回的数据
        this.numsMap = response.numsMap || {};
        this.list = response.list || {};

        // 构建tabs数据
        this.tabs = Object.keys(this.numsMap).map(key => {
          const numData = this.numsMap[key];
          const title = this.list[key] || key;
          return {
            title,
            currentCount: numData[0] || 0, // 第一个数字是已激活数量
            totalCount: numData[1] || 0, // 第二个数字是总数量
            type: key
          };
        });

        // 如果有tabs数据，加载第一个tab的内容
        if (this.tabs.length > 0) {
          this.activeTab = 0;
          this.resetAndLoadItems();
        }
      } catch (error) {
        console.error("Failed to fetch software types:", error);
        Toast("获取软件类型失败");
        this.isError = true;
      } finally {
        this.loading = false;
      }
    },

    onClickLeftNav() {
      this.backToApp();
    },

    onClickRightNav() {
      this.showFilter = true;
    },

    onTabChange(index) {
      this.activeTab = index;
      this.currentPage = 1; // 重置页码
      this.currentItems = []; // 清空当前项目
      this.finished = false; // 重置完成状态
    },

    resetAndLoadItems() {
      this.currentItems = [];
      this.currentPage = 1;
      this.finished = false;
      this.loading = true; // 开始加载时，loading应为true，以便首次触发onLoadItems
      this.onLoadItems(); // 主动调用一次加载
    },

    async onLoadItems() {
      this.loading = true;
      try {
        const tabType = this.currentTabType;
        if (!tabType) {
          this.finished = true;
          this.loading = false;
          return;
        }

        // 构建API请求参数
        const params = {
          no: this.currentPage,
          limit: this.itemsPerPage,
          softwareId: tabType
        };

        // 添加筛选条件
        if (this.filterForm.storeName) {
          params.deptName = this.filterForm.storeName;
        }

        if (this.filterForm.displayName) {
          params.username = this.filterForm.displayName;
        }

        // 处理激活状态，使用逗号分隔的字符串
        if (
          this.filterForm.activeStates &&
          this.filterForm.activeStates.length > 0
        ) {
          params.activeStateList = this.filterForm.activeStates.join(",");
        }

        // 调用API获取授权码数据
        const response = await getAuthCodePageByTypeForH5(params);

        // 处理返回的数据
        const records = response?.records || [];
        const total = response?.total || 0;

        // 注：API返回records中的source字段，当值为2时表示特殊来源，需屏蔽激活/变更按钮
        
        // 直接使用接口返回的数据结构
        this.currentItems.push(...records);

        // 如果是第一页，初始化allItems对象
        if (this.currentPage == 1) {
          this.$set(this.allItems, tabType, []);
        }

        // 更新allItems对象
        this.allItems[tabType] = [
          ...(this.allItems[tabType] || []),
          ...records
        ];

        this.loading = false;

        // 判断是否还有更多数据
        if (this.currentItems.length >= total) {
          this.finished = true;
        } else {
          this.currentPage++;
        }

        // 更新tab上的计数
        if (this.tabs[this.activeTab]) {
          const activeCount = this.numsMap[tabType]?.[0] || 0;
          const totalCount = this.numsMap[tabType]?.[1] || 0;
          this.$set(this.tabs[this.activeTab], "currentCount", activeCount);
          this.$set(this.tabs[this.activeTab], "totalCount", totalCount);
        }
      } catch (error) {
        console.error("Failed to load items:", error);
        this.loading = false;
        this.finished = true; // 出错时也标记为完成，避免无限加载
      }
    },
    handleItemAction(item) {
      this.currentChangingItem = item;
      // 需要根据accountFlag去区分
      if (item.accountFlag) {
        this.showAccountSelector = true;
      } else {
        this.showStoreSelector = true;
      }
    },
    resetFilter() {
      this.filterForm = {
        storeName: "",
        displayName: "",
        status: "",
        activeStates: []
      };
      // 关闭筛选弹窗
      // this.showFilter = false;
      // 重置后立即加载数据
      this.currentPage = 1;
      this.currentItems = [];
      this.finished = false;
      this.resetAndLoadItems();
    },
    confirmFilter() {
      // 应用筛选条件并重新加载数据
      this.showFilter = false;
      this.currentPage = 1; // 重置页码
      this.currentItems = []; // 清空当前项目
      this.finished = false; // 重置完成状态
      this.resetAndLoadItems(); // 重新加载数据
    },
    // 处理从StoreSelector组件返回的确认事件
    handleStoreConfirm(selectedStore) {
      if (this.currentChangingItem) {
        // 根据激活状态调用不同的API
        if (this.currentChangingItem.activeState == 1) {
          // 已激活，调用变更API
          this.changeAuthCode(selectedStore);
        } else {
          // 未激活，调用激活API
          this.activateAuthCode(selectedStore);
        }
      }
    },
    // 处理从AccountSelector组件返回的确认事件
    handleAccountConfirm(selectedAccount) {
      if (!this.currentChangingItem) {
        Toast.fail("操作已取消或数据已失效");
        return;
      }

      try {
        // 根据激活状态调用不同的API
        if (this.currentChangingItem.activeState == 1) {
          // 已激活，调用变更API
          this.changeAccountAuthCode(selectedAccount);
        } else {
          // 未激活，调用激活API
          this.activateAccountAuthCode(selectedAccount);
        }
      } catch (error) {
        console.error("处理账号确认失败:", error);
        Toast.fail("操作失败，请重试");
      }
    },
    // 激活授权码
    async activateAuthCode(selected) {
      try {
        // 获取激活日期：优先使用入网时间，其次是创建时间
        let activationDate;
        if (this.currentChangingItem.deviceRegisterTime) {
          // 优先使用入网时间
          activationDate = this.currentChangingItem.deviceRegisterTime;
        } else if (this.currentChangingItem.createTime) {
          // 其次使用创建时间
          activationDate = this.currentChangingItem.createTime;
        }

        if (!activationDate) {
          Toast("没有找到激活日期");
          return;
        }

        const params = {
          id: this.currentChangingItem.id,
          deptId: selected.deptId,
          activationTime: activationDate
        };

        // 显示加载提示
        Toast.loading({
          message: "正在激活...",
          forbidClick: true,
          duration: 0
        });

        await activeAuthorizationCodeByList(params);

        // 关闭加载提示
        Toast.clear();

        // 显示成功提示
        Toast.success("激活成功");

        // 更新当前项的状态
        const itemInAllData = this.allItems[this.currentTabType].find(
          i => i.id === this.currentChangingItem.id
        );

        if (itemInAllData) {
          itemInAllData.activeState = "1";
          itemInAllData.deptName = selected.name;
        }

        this.currentChangingItem = null;
        this.resetAndLoadItems(); // 重新加载数据
        this.updateSoftwareTypeCounts(); // 更新软件类型计数
      } catch (error) {
        // 关闭加载提示
        Toast.clear();
        // 网络错误或其他异常
        console.error("激活授权码失败:", error);
        Toast.fail(error.result || error.message || "激活失败，请稍后重试");
      }
    },
    // 变更授权码
    async changeAuthCode(selected) {
      try {
        // 获取变更日期：优先使用入网时间，其次是创建时间
        let changeDate;
        if (this.currentChangingItem.deviceRegisterTime) {
          // 优先使用入网时间
          changeDate = this.currentChangingItem.deviceRegisterTime;
        } else if (this.currentChangingItem.createTime) {
          // 其次使用创建时间
          changeDate = this.currentChangingItem.createTime;
        }

        if (!changeDate) {
          Toast("没有找到变更日期");
          return;
        }

        const params = {
          id: this.currentChangingItem.id,
          deptId: selected.deptId,
          expirationTime: changeDate
        };

        // 显示加载提示
        Toast.loading({
          message: "正在变更...",
          forbidClick: true,
          duration: 0
        });

        await changeDeptAuthorizationCodeByList(params);

        // 关闭加载提示
        Toast.clear();

        // 显示成功提示
        Toast.success("变更成功");

        // 更新当前项的状态
        const itemInAllData = this.allItems[this.currentTabType].find(
          i => i.id === this.currentChangingItem.id
        );

        if (itemInAllData) {
          itemInAllData.deptName = selected.name;
        }

        this.currentChangingItem = null;
        this.resetAndLoadItems(); // 重新加载数据
        this.updateSoftwareTypeCounts(); // 更新软件类型计数
      } catch (error) {
        // 关闭加载提示
        Toast.clear();
        // 网络错误或其他异常
        console.error("变更授权码失败:", error);
        Toast.fail(error.result || error.message || "变更失败，请稍后重试");
      }
    },
    // 切换激活状态选择
    toggleActiveState(state) {
      const index = this.filterForm.activeStates.indexOf(state);
      if (index > -1) {
        // 如果已选中，则取消选中
        this.filterForm.activeStates.splice(index, 1);
      } else {
        // 如果未选中，则添加选中
        this.filterForm.activeStates.push(state);
      }
    },
    // 激活账号授权码
    async activateAccountAuthCode(selectedAccount) {
      try {
        // 构建API请求参数
        const params = {
          id: this.currentChangingItem.id,
          usercode: selectedAccount.usercode,
          username: selectedAccount.name,
          oldUserid: selectedAccount.oldUserid
        };

        // 显示加载提示
        Toast.loading({
          message: "正在激活...",
          forbidClick: true,
          duration: 0
        });

        // 调用API激活授权码
        await activeAccountAuthorizationCodeByList(params);

        // 关闭加载提示
        Toast.clear();

        Toast.success("激活成功");
        // 重置当前项
        this.currentChangingItem = null;
        // 重新加载数据
        this.resetAndLoadItems();
        this.updateSoftwareTypeCounts(); // 更新软件类型计数
      } catch (error) {
        // 关闭加载提示
        Toast.clear();
        // 网络错误或其他异常
        console.error("激活授权码失败:", error);
        Toast.fail(error.result || error.message || "激活失败，请稍后重试");
      }
    },
    // 变更账号授权码
    async changeAccountAuthCode(selectedAccount) {
      try {
        // 构建API请求参数
        const params = {
          id: this.currentChangingItem.id,
          usercode: selectedAccount.usercode,
          username: selectedAccount.name,
          oldUserid: selectedAccount.oldUserid
        };

        // 显示加载提示
        Toast.loading({
          message: "正在变更...",
          forbidClick: true,
          duration: 0
        });

        // 调用API变更授权码
        await changeAccountAuthorizationCodeByList(params);

        // 关闭加载提示
        Toast.clear();

        Toast.success("变更成功");
        // 重置当前项
        this.currentChangingItem = null;
        // 重新加载数据
        this.resetAndLoadItems();
        this.updateSoftwareTypeCounts(); // 更新软件类型计数
      } catch (error) {
        // 关闭加载提示
        Toast.clear();
        // 网络错误或其他异常
        console.error("变更授权码失败:", error);
        Toast.fail(error.result || error.message || "变更失败，请稍后重试");
      }
    },
    // 更新软件类型计数
    async updateSoftwareTypeCounts() {
      try {
        const response = await getAuthCodeSoftwareTypeByH5();

        // 更新numsMap数据
        this.numsMap = response.numsMap || {};

        // 更新tabs中的计数
        this.tabs.forEach((tab, index) => {
          const numData = this.numsMap[tab.type] || [0, 0];
          this.$set(this.tabs[index], "currentCount", numData[0] || 0);
          this.$set(this.tabs[index], "totalCount", numData[1] || 0);
        });
      } catch (error) {
        console.error("更新软件类型计数失败:", error);
      }
    }
  }
};
</script>

<style scoped lang="scss">
.auth-page-container {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 占满整个视窗高度 */
  background-color: #f7f8fa; /* 页面背景色 */
}

.van-nav-bar {
  flex-shrink: 0; /* 防止导航栏被压缩 */
}

/* 根据图片调整导航栏右侧图标颜色 */
.van-nav-bar__right .van-icon {
  color: #333333; /* 或者图片中的实际颜色 */
}

.main-layout {
  display: flex;
  flex-grow: 1; /* 主内容区域占据剩余空间 */
  overflow: hidden; /* 防止内部滚动影响父级 */
}

.sidebar-nav {
  width: 112px !important; /* 根据图片调整宽度 */
  flex-shrink: 0;
  background-color: #f7f8fa; /* 侧边栏背景色，与图片一致 */
  height: 100%; /* 撑满父容器高度 */
  overflow-y: auto;
}

.van-sidebar-item {
  padding: 18px 8px; /* 调整内边距使文字居中且有足够空间 */
  text-align: center;
  font-size: 14px;
  color: #333333;
}
.van-sidebar-item--select {
  background-color: #fff; /* 选中项背景色（可选） */
}
.van-sidebar-item--select::before {
  background-color: #fff; /* 选中项左侧指示条颜色 */
}

.tab-title-custom {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  line-height: 1.4;
}

.tab-counts {
  font-size: 14px;
  color: #333333; /* 未选中时的数字颜色 */
}
.van-sidebar-item--select .tab-counts {
  color: #333333; /* 选中时的数字颜色 */
  font-size: 14px;
}

.content-area {
  flex-grow: 1;
  padding: 12px;
  overflow-y: auto; /* 内容区域独立滚动 */
  background-color: #fff; /* 内容区域背景色 */
}

.auth-item-card {
  background-color: #ffffff;
  border-bottom: 1px solid #ebedf0; /* 边框颜色 */
  border-radius: 6px; /* 圆角 */
  padding: 12px 0;
  margin-bottom: 10px;
  position: relative; /* 为了按钮的绝对定位 */
  font-size: 12px; /* 基础字体大小 */
  color: #646566; /* 文本颜色 */
}

.item-id {
  font-weight: 600;
  color: #333333;
  font-size: 14px;
  margin-bottom: 8px;
}

.item-details p {
  margin-bottom: 8px;
  line-height: 1.5;
}

.action-button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;

  /* 激活按钮样式 - 橙色背景 */
  .van-button--primary {
    border-color: #ff9900;
    width: 62px;
    height: 24px;
    background: #ff9900;
    border-radius: 14px;
  }

  /* 变更按钮样式 - 白色背景，橙色文字和边框 */
  .van-button--default {
    color: #ff9900;
    border-color: #ff9900;
    background-color: #fff;
    width: 62px;
    height: 24px;
    border: 0.5px solid #ff9900;
    border-radius: 14px;
  }

  .van-button--default:active {
    /* 点击效果 */
    background-color: #fff5f0;
  }

  p {
    margin-bottom: 0;
  }
}

::v-deep .van-nav-bar .van-icon {
  color: #333333;
}

::v-deep .van-nav-bar__arrow {
  font-size: 20px;
}

.empty-data {
  text-align: center;
  color: #969799;
  padding: 20px 0;
}

.filter-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #ebedf0;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .van-icon {
      font-size: 20px;
      color: #333;
    }
  }

  .filter-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    .filter-item {
      margin-bottom: 20px;

      .label {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }

      .status-options {
        display: flex;
        gap: 12px;

        .status-btn {
          width: 106px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          border-radius: 6px;
          font-size: 14px;
          color: #333;
          background: #f7f7f7;
          border: 1px solid #f7f7f7;

          cursor: pointer;

          &.active {
            background: #fff5e6;
            border-color: #ff9900;
            border: 1px solid #ff9900;
            color: #ff9900;
          }
        }
      }
    }
  }

  .filter-footer {
    padding: 16px;
    display: flex;
    gap: 8px;

    .van-button {
      flex: 1;
      height: 40px;
      border-radius: 8px;

      &--default {
        border-color: #dcdee0;
        color: #333;
      }

      &--primary {
        background: #ff9900;
        border-color: #ff9900;
      }
    }
    
    ::v-deep .van-button--disabled {
      background: #f5f5f5 !important;
      border-color: #e8e8e8 !important;
      color: #c8c9cc !important;
      opacity: 1 !important;
    }
  }
}

// 修改 Field 样式
::v-deep .van-field {
  padding: 10px 16px;
  background: #f7f8fa;
  border-radius: 4px;

  .van-field__control {
    color: #333;
    font-size: 14px;

    &::placeholder {
      color: #999;
    }
  }
}

::v-deep .sidebar-nav {
  width: 90px;
}

.error-message {
  text-align: center;
  padding: 20px;
  color: #ee0a24;

  .van-icon {
    margin-bottom: 10px;
  }

  p {
    margin-bottom: 10px;
  }

  .van-button {
    background: #ff9900;
    border-color: #ff9900;
    color: #fff;
  }
}
</style>
