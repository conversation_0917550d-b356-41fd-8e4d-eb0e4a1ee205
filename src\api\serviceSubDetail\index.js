import urls from "./config";
import { get, postJson } from "../request";

export function getAllDistrictInfo(params) {
  return get(urls.getAllDistrictInfo, params);
}

export function getProductDetail(params) {
  return get(urls.getProductDetail, params);
}

export function saveOrUpdateAddress(params) {
  return postJson(urls.saveOrUpdateAddress, params);
}

export function getAddressList(params) {
  return get(urls.getAddressList, params);
}

export function removeAddress(params) {
  return get(urls.removeAddress, params);
}

export function setDefaultAddress(params) {
  return get(urls.setDefaultAddress, params);
}

export function getPackageDetail(params) {
  return get(urls.getPackageDetail, params);
}

export function saveOrder(params) {
  return postJson(urls.saveOrder, params);
}

export function payH5(params) {
  return postJson(urls.payH5, params);
}

export function orderSelect2(params) {
  return get(urls.orderSelect2, params);
}

export function getCustomerAccountList(params) {
  return get(urls.getCustomerAccountList, params);
}

