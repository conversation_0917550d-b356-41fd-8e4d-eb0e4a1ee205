<template>
  <div class="passPage">
    <!-- <van-nav-bar  left-arrow></van-nav-bar> -->
    <van-nav-bar v-show="showHome" title="支付中心"></van-nav-bar>
    <!-- 成功 -->
    <div class="pass-body" v-show="status">
      <div class="image">
        <img src="../assets/pass.png" alt="" />
      </div>
      <div class="payPass">支付成功</div>
      <div class="content">感谢使用微信支付</div>
    </div>
    <!-- 失败 -->
    <div class="pass-body" v-show="!status">
      <div class="image">
        <img src="../assets/fail.png" alt="" />
      </div>
      <div class="payPass">支付失败</div>
      <div class="content">支付遇到问题，请尝试重新支付</div>
    </div>

    <div class="button" v-show="$route.query.billId">
      <van-button class="btn" @click="viewDetail">返回首页</van-button>
    </div>
    <div class="button" v-show="$route.query.contractId">
      <van-button class="btn" @click="viewContractDetail">返回首页</van-button>
    </div>
    <div class="button" v-show="showRecharge">
      <van-button class="btn" @click="backRecharge">返回储值页</van-button>
    </div>
  </div>
</template>

<script>
import { NavBar, Button } from "vant";
import { getUserAgentInfo } from "../common/utils";
import { orderSelect2 } from "@/api/serviceSubDetail";

import axios from "axios";

export default {
  components: {
    [Button.name]: Button,
    [NavBar.name]: NavBar
  },

  data() {
    return {
      status: false,
      showHome: true,
      showRecharge: false
    };
  },

  created() {
    let { outTradeNo, pageType } = this.$route.query;
    let result = { pt: null };
    try {
      if (pageType) {
        const parsedResult = this.parsePageType(pageType);
        if (parsedResult) {
          result = {
            pt: parsedResult.pt
          };
        }
      }

      if (outTradeNo) {
        this.orderSelect2(outTradeNo);
        if (result.pt === 77) {
          this.showRecharge = true;
        }
      } else {
        this.finishPay();
      }
    } catch (error) {
      console.error("支付回调数据处理错误:", error);
    }
  },

  mounted() {
    /**判断h5环境?不显示返回首页:返回首页 */
    if (getUserAgentInfo().match(/MicroMessenger/i) == "micromessenger") {
      this.showHome = false;
    }
  },

  methods: {
    /**查看订单详情 */
    viewDetail() {
      if (this.$route.query.type == 2) {
        if (window.ovopark.browser.android) {
          window.ovopark.action("openAppPage", {
            module: "CRM_EXPENSE_CENTER"
          });
        } else {
          window.location.href =
            "ioswdzforappstore://push/crm?type=ivan_view&page=ExpenseCenterTopViewController";
        }
      }
    },
    viewContractDetail() {
      if (this.$route.query.type == 1) {
        this.$router.push({
          name: "contractDetail",
          query: {
            contractId: this.$route.query.contractId,
            token: this.$route.query.token,
            isLogin: "1"
          }
        });
      }
    },

    /**
     * 解析页面类型
     * @param {string} pageType - 页面类型字符串
     * @returns {Object} 解析结果
     */
    parsePageType(pageType) {
      if (!pageType) return null;
      const regex = /(?<pt>\d+)-CI(?<ci>\d*)-BI(?<bi>\d*)/;
      const match = pageType.match(regex);
      if (!match) return null;
      return {
        pt: parseInt(match.groups.pt) || null, // 页面来源
        ci: match.groups.ci ? parseInt(match.groups.ci) : null, // 合同ID
        bi: match.groups.bi ? parseInt(match.groups.bi) : null // 账单ID
      };
    },

    //完成付款  根据type去做判断
    finishPay() {
      const { pageType } = this.$route.query;
      const result = this.parsePageType(pageType);
      if (!result) {
        this.status = false;
        return;
      }

      const isOnline = process.env.NODE_ENV === "production";
      const token = isOnline
        ? "9720D5065024EF8A0DCD889402D39DA06980B790199361F2812C742AB117B27E99204AD0F9AD0889A69C9E987187F5B5"
        : "FD7BE69FC461212C90C3A7D625EEF388228747AA3F92FF0937ED694150185DF756045069D33DD5D695A082A115927D77552A8336D8CE73D63E9D24C51675365B";

      if (result.pt == "99") {
        axios
          .get("/api/crm/contract/v1/getContractDetailForH5/" + result.ci, {
            headers: {
              authenticator: token
            }
          })
          .then(res => {
            if (res.data.data.paymentStatus != "3") { //原先取值有问题
              this.status = false;
            } else {
              this.status = true;
            }
          });
      } else if (result.pt == "88") {
        axios
          .get("/api/crm/bill/v1/isPay?billId=" + this.$route.query.billId, {
            headers: {
              authenticator: token
            }
          })
          .then(res => {
            this.status = res ? true : false;
          });
      } else {
        let id = this.$route.query.contractId || "";
        axios
          .get("/api/crm/contract/v1/getContractDetailForH5/" + id, {
            headers: {
              authenticator: token
            }
          })
          .then(res => {
            if (res.data.data.paymentStatus != "3") {
              this.status = false;
            } else {
              this.status = true;
            }
          });
      }
    },

    // 判断支付是否成功
    async orderSelect2(outTradeNo) {
      try {
        const res = await orderSelect2({
          outTradeNo
        });
        if (res.tradeState == "NOTPAY") {
          this.status = false;
        } else if (res.tradeState == "SUCCESS") {
          this.status = true;
        }
      } catch (error) {
        console.log(error);
      }
    },

    backRecharge() {
      // 返回储值页
      this.$router.push({
        name: "recharge"
      });
    }
  }
};
</script>

<style scoped lang="scss">
.passPage {
  height: 100%;
  width: 100%;
  background-color: #fff;
}
/deep/.van-nav-bar__title {
  height: 24px;
  font-family: PingFangSC-Semibold;
  font-weight: bold;
  font-size: 17px;
  color: #333333;
  text-align: center;
}
.image {
  margin-top: 78px;
  display: flex;
  justify-content: center;
}
.payPass {
  display: flex;
  justify-content: center;
  margin-top: 12px;
  font-size: 16px;
  color: #333333;
  letter-spacing: 0;
}
.content {
  display: flex;
  justify-content: center;
  font-size: 13px;
  color: #b2b2b2;
  margin-top: 6px;
}
.button {
  margin-top: 120px;
  display: flex;
  justify-content: space-around;
  .btn {
    border: 1px solid #e5e5e5;
  }
}
/deep/.van-button--normal {
  width: 150px;
  height: 44px;
  padding: 0 4vw;
  border-radius: 6px;
}
</style>
