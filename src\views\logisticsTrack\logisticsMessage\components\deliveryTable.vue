<template>
  <table cellspacing="0">
    <thead>
      <tr>
        <th>序号</th>
        <th>产品名称</th>
        <th>数量</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="(item, index) in tableData" :key="index">
        <td :class="item.isOtherProduct ? 'disabled' : 'w-20'">
          {{ index + 1 }}
        </td>
        <td :class="item.isOtherProduct ? 'disabledNum' : ''">
          {{ item.materialName }}
        </td>
        <td :class="item.isOtherProduct ? 'disabled' : 'w-20'">
          {{ item.realQty }}
        </td>
      </tr>
      <tr>
        <td></td>
        <td>汇总</td>
        <td>{{ total }}</td>
      </tr>
    </tbody>
  </table>
</template>

<script>
export default {
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    tableData(val) {
      if (val) {
        let num = 0;
        val.forEach((item) => {
          num += item.realQty;
        });
        this.total = num;
      }
    }
  },
  data() {
    return {
      total: 0
    };
  }
};
</script>

<style lang="scss" scoped>
table {
  width: 100%;
  box-sizing: border-box;

  thead {
    tr {
      background: rgba(247, 247, 247, 1);
    }
  }
  th,
  td {
    text-align: center;
    line-height: 38px;
  }
  tr:nth-child(2n) {
    background: rgba(247, 247, 247, 1);
  }
}
.w-20 {
  width: 20%;
}

.disabled {
  width: 20%;
  color: #ccc;
}

.disabledNum {
  color: #ccc;
}
</style>