<template>
  <div class="contract-repayment">
    <!-- 顶部导航栏 -->
    <div class="header">
      <van-nav-bar title="合同/回款额" left-arrow @click-left="onClickLeft" />
    </div>

    <!-- 内容区域 -->
    <div class="content-container">
      <!-- 个人数据部分 -->
      <div class="personal-data">
        <div class="section-title">个人数据</div>
        <div class="filter-tabs">
          <div class="tab-wrapper">
            <div
              :class="['tab-item', { active: currentTab === 'time' }]"
              @click="handleTabClick('time')"
            >
              <span>{{ getDateTypeLabel() }}</span>
              <van-icon name="arrow-down" size="11" />
            </div>
            <div
              :class="['tab-item', { active: currentTab === 'custom' }]"
              @click="handleTabClick('custom')"
            >
              <span>{{ customDateRangeText }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 表格部分 -->
      <div class="table-container">
        <!-- 当有数据时显示表格 -->
        <template v-if="tableData && tableData.length > 0">
          <!-- 主页面固定表格结构 -->
          <div class="fixed-table-container">
            <!-- 固定表头 -->
            <div class="fixed-table-header" ref="mainTableHeader">
              <div class="table-header-row">
                <div
                  :class="[
                    'column-amount',
                    column.field === 'customerName' ? 'column-name' : ''
                  ]"
                  v-for="(column, index) in columnFields"
                  :key="index"
                  @click="
                    column.field !== 'customerName'
                      ? handleSort(column.field)
                      : null
                  "
                >
                  <div class="column-title">{{ column.label }}</div>
                  <div
                    class="sort-icons"
                    v-if="column.field !== 'customerName'"
                  >
                    <van-icon
                      name="arrow-up"
                      :class="[
                        'sort-icon',
                        {
                          active:
                            currentSort === column.field && sortOrder === 'asc'
                        }
                      ]"
                    />
                    <van-icon
                      name="arrow-down"
                      :class="[
                        'sort-icon',
                        {
                          active:
                            currentSort === column.field && sortOrder === 'desc'
                        }
                      ]"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 可滚动内容区域 -->
            <div class="scrollable-table-body" ref="mainTableBody">
              <div
                class="table-row"
                v-for="(item, index) in tableData"
                :key="index"
                @click="handleRowClick(item)"
              >
                <div class="column-amount column-name" :title="item.customerName">
                  {{ item.customerName }}
                </div>
                <div class="column-amount">{{ item.contractAmount }}</div>
                <div class="column-amount">{{ item.hardwareAmount }}</div>
                <div class="column-amount">{{ item.softwareAmount }}</div>
                <div class="column-amount">
                  {{ item.constructionAmount }}
                </div>
                <div class="column-amount">{{ item.repaymentAmount }}</div>
              </div>
            </div>

            <!-- 固定合计行 -->
            <div
              class="fixed-table-footer"
              ref="mainTableFooter"
              v-if="tableData && tableData.length > 0"
            >
              <div class="table-footer-row summary-row">
                <div class="column-amount column-name">汇总</div>
                <div class="column-amount">
                  {{ totalData.contractAmount }}
                </div>
                <div class="column-amount">
                  {{ totalData.hardwareAmount }}
                </div>
                <div class="column-amount">
                  {{ totalData.softwareAmount }}
                </div>
                <div class="column-amount">
                  {{ totalData.constructionAmount }}
                </div>
                <div class="column-amount">
                  {{ totalData.repaymentAmount }}
                </div>
              </div>
            </div>
          </div>

          <!-- 将查看更多按钮放在表格主体外，但仍在表格容器内 -->
          <div class="view-more-btn-container" v-if="total > 5">
            <div class="view-more-btn" @click="showAllData">
              <span>查看更多</span>
              <van-icon name="arrow-down" size="14" />
            </div>
          </div>
        </template>
        <!-- 无数据时显示空状态 -->
        <template v-else>
          <div class="empty-state-container">
            <van-empty description="暂无数据" />
          </div>
        </template>
      </div>

      <!-- 图表部分 -->
      <div class="chart-section">
        <div class="chart-container">
          <div class="chart-header">
            <div class="title-area">
              <div class="chart-title">合同/回款额</div>
              <div class="unit-text">单位：万元</div>
            </div>
            <div class="legend-area">
              <div class="legend-item">
                <div class="legend-dot contract-dot"></div>
                <span>合同额</span>
              </div>
              <div class="legend-item">
                <div class="legend-dot repayment-dot"></div>
                <span>回款额</span>
              </div>
            </div>
          </div>
          <div v-if="hasContractChartData" ref="contractChart" class="chart"></div>
          <div v-else class="chart-empty">
            <van-empty description="暂无图表数据" />
          </div>
        </div>

        <div class="chart-container">
          <div class="chart-header">
            <div class="title-area">
              <div class="chart-title">合同/回款年度累计趋势</div>
              <div class="unit-text">单位：万元</div>
            </div>
            <div class="legend-area">
              <div class="legend-item">
                <div class="legend-dot contract-dot"></div>
                <span>合同额</span>
              </div>
              <div class="legend-item">
                <div class="legend-dot repayment-dot"></div>
                <span>回款额</span>
              </div>
            </div>
          </div>
          <div v-if="hasTrendChartData" ref="trendChart" class="chart"></div>
          <div v-else class="chart-empty">
            <van-empty description="暂无趋势数据" />
          </div>
        </div>
      </div>
    </div>

    <!-- 时间段选择弹窗 -->
    <van-popup
      v-model="showDatePopup"
      position="bottom"
      round
      :style="{ height: '242px' }"
    >
      <div class="date-popup">
        <div class="popup-header">
          <div class="date-popup-title">时间段</div>
          <van-icon
            name="cross"
            class="close-icon"
            @click="showDatePopup = false"
          />
        </div>

        <div class="date-type-tabs">
          <div
            v-for="(item, index) in dateTypeOptions"
            :key="index"
            :class="['date-type-item', { active: dateType === item.value }]"
            @click="selectDateType(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 自定义日期选择弹窗 -->
    <van-popup
      v-model="showCustomDatePopup"
      position="bottom"
      round
      :style="{ height: 'auto' }"
    >
      <div class="custom-date-popup">
        <div class="popup-header">
          <div class="date-popup-title">自定义时间</div>
          <van-icon
            name="cross"
            class="close-icon"
            @click="showCustomDatePopup = false"
          />
        </div>

        <div class="date-input-container">
          <div class="date-input">
            <input
              type="text"
              readonly
              :value="customStartDate"
              placeholder="开始"
              class="custom-date-input"
              :class="{
                active: activeInput === 'start',
                selected: !!customStartDate
              }"
              @click="handleDateInputClick('start')"
            />
          </div>
          <span class="date-separator">—</span>
          <div class="date-input">
            <input
              type="text"
              readonly
              :value="customEndDate"
              placeholder="结束"
              class="custom-date-input"
              :class="{
                active: activeInput === 'end',
                selected: !!customEndDate,
                disabled: !isEndDateEnabled
              }"
              @click="handleDateInputClick('end')"
            />
          </div>
        </div>

        <div class="date-picker-container" v-if="isPickerVisible">
          <div class="date-picker-column">
            <div
              v-for="year in yearList"
              :key="'year-' + year"
              :class="[
                'date-picker-item',
                selectedDate && selectedDate.getFullYear() === year
                  ? 'active'
                  : ''
              ]"
              @click="selectYear(year)"
            >
              {{ year }}年
            </div>
          </div>
          <div class="date-picker-column">
            <div
              v-for="month in 12"
              :key="'month-' + month"
              :class="[
                'date-picker-item',
                selectedDate && selectedDate.getMonth() + 1 === month
                  ? 'active'
                  : ''
              ]"
              @click="selectMonth(month)"
            >
              {{ month.toString().padStart(2, "0") }}月
            </div>
          </div>
          <div class="date-picker-column">
            <div
              v-for="day in getDaysInMonth()"
              :key="'day-' + day"
              :class="[
                'date-picker-item',
                selectedDate && selectedDate.getDate() === day ? 'active' : ''
              ]"
              @click="selectDay(day)"
            >
              {{ day.toString().padStart(2, "0") }}日
            </div>
          </div>
        </div>

        <div class="date-confirm-btn">
          <van-button type="warning" block @click="confirmCustomDate"
            >确定</van-button
          >
        </div>
      </div>
    </van-popup>

    <!-- 所有数据弹窗 -->
    <van-popup
      v-model="showAllDataPopup"
      position="bottom"
      closeable
      round
      close-icon-position="top-right"
      :style="{ height: '80%' }"
      @closed="handleAllDataPopupClosed"
    >
      <div class="all-data-popup">
        <div class="popup-title">个人数据</div>

        <!-- 弹窗内的时间段选择 -->
        <div class="popup-filter-tabs">
          <div class="tab-wrapper">
            <div
              :class="['tab-item', { active: popupCurrentTab === 'time' }]"
              @click="handlePopupTabClick('time')"
            >
              <span>{{ getPopupDateTypeLabel() }}</span>
              <van-icon name="arrow-down" size="11" />
            </div>
            <div
              :class="['tab-item', { active: popupCurrentTab === 'custom' }]"
              @click="handlePopupTabClick('custom')"
            >
              <span>{{ popupCustomDateRangeText }}</span>
            </div>
          </div>
        </div>

        <!-- 固定表格结构 -->
        <div class="fixed-table-container">
          <!-- 固定表头 -->
          <div class="fixed-table-header" ref="popupTableHeader">
            <div class="table-header-row">
              <div
                :class="[
                  'column-amount',
                  column.field === 'customerName' ? 'column-name' : ''
                ]"
                v-for="(column, index) in columnFields"
                :key="index"
                @click="
                  column.field !== 'customerName'
                    ? handlePopupSort(column.field)
                    : null
                "
              >
                <div class="column-title">{{ column.label }}</div>
                <div class="sort-icons" v-if="column.field !== 'customerName'">
                  <van-icon
                    name="arrow-up"
                    :class="[
                      'sort-icon',
                      {
                        active:
                          popupCurrentSort === column.field &&
                          popupSortOrder === 'asc'
                      }
                    ]"
                  />
                  <van-icon
                    name="arrow-down"
                    :class="[
                      'sort-icon',
                      {
                        active:
                          popupCurrentSort === column.field &&
                          popupSortOrder === 'desc'
                      }
                    ]"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 可滚动内容区域 -->
          <div class="scrollable-table-body" ref="popupTableBody">
            <template v-if="allData && allData.length > 0">
              <div
                class="table-row"
                v-for="(item, index) in allData"
                :key="index"
                @click="handleRowClick(item)"
              >
                <div class="column-amount column-name" :title="item.customerName">
                  {{ item.customerName }}
                </div>
                <div class="column-amount">{{ item.contractAmount }}</div>
                <div class="column-amount">{{ item.hardwareAmount }}</div>
                <div class="column-amount">{{ item.softwareAmount }}</div>
                <div class="column-amount">
                  {{ item.constructionAmount }}
                </div>
                <div class="column-amount">
                  {{ item.repaymentAmount }}
                </div>
              </div>

              <!-- 加载更多 -->
              <van-list
                v-model="pageLoading"
                :finished="!hasMoreData"
                finished-text="没有更多数据了"
                loading-text="加载中..."
                :immediate-check="false"
                @load="loadMorePage"
              />
            </template>
            <!-- 无数据时显示空状态 -->
            <template v-else>
              <div class="empty-state-container">
                <van-empty description="暂无数据" />
              </div>
            </template>
          </div>

          <!-- 固定合计行 -->
          <div
            class="fixed-table-footer"
            ref="popupTableFooter"
            v-if="allData && allData.length > 0"
          >
            <div class="table-footer-row summary-row">
              <div class="column-amount column-name">汇总</div>
              <div class="column-amount">
                {{ totalData.contractAmount }}
              </div>
              <div class="column-amount">
                {{ totalData.hardwareAmount }}
              </div>
              <div class="column-amount">
                {{ totalData.softwareAmount }}
              </div>
              <div class="column-amount">
                {{ totalData.constructionAmount }}
              </div>
              <div class="column-amount">
                {{ totalData.repaymentAmount }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 弹窗时间段选择弹窗 -->
    <van-popup
      v-model="showPopupDatePopup"
      position="bottom"
      round
      :style="{ height: '242px' }"
    >
      <div class="date-popup">
        <div class="popup-header">
          <div class="date-popup-title">时间段</div>
          <van-icon
            name="cross"
            class="close-icon"
            @click="showPopupDatePopup = false"
          />
        </div>

        <div class="date-type-tabs">
          <div
            v-for="(item, index) in dateTypeOptions"
            :key="index"
            :class="[
              'date-type-item',
              { active: popupDateType === item.value }
            ]"
            @click="selectPopupDateType(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 弹窗自定义日期选择弹窗 -->
    <van-popup
      v-model="showPopupCustomDatePopup"
      position="bottom"
      round
      :style="{ height: 'auto' }"
    >
      <div class="custom-date-popup">
        <div class="popup-header">
          <div class="date-popup-title">自定义时间</div>
          <van-icon
            name="cross"
            class="close-icon"
            @click="showPopupCustomDatePopup = false"
          />
        </div>

        <div class="date-input-container">
          <div class="date-input">
            <input
              type="text"
              readonly
              :value="popupCustomStartDate"
              placeholder="开始"
              class="custom-date-input"
              :class="{
                active: popupActiveInput === 'start',
                selected: !!popupCustomStartDate
              }"
              @click="handlePopupDateInputClick('start')"
            />
          </div>
          <span class="date-separator">—</span>
          <div class="date-input">
            <input
              type="text"
              readonly
              :value="popupCustomEndDate"
              placeholder="结束"
              class="custom-date-input"
              :class="{
                active: popupActiveInput === 'end',
                selected: !!popupCustomEndDate,
                disabled: !popupIsEndDateEnabled
              }"
              @click="handlePopupDateInputClick('end')"
            />
          </div>
        </div>

        <div class="date-picker-container" v-if="popupIsPickerVisible">
          <div class="date-picker-column">
            <div
              v-for="year in yearList"
              :key="'year-' + year"
              :class="[
                'date-picker-item',
                popupSelectedDate && popupSelectedDate.getFullYear() === year
                  ? 'active'
                  : ''
              ]"
              @click="selectPopupYear(year)"
            >
              {{ year }}年
            </div>
          </div>
          <div class="date-picker-column">
            <div
              v-for="month in 12"
              :key="'month-' + month"
              :class="[
                'date-picker-item',
                popupSelectedDate && popupSelectedDate.getMonth() + 1 === month
                  ? 'active'
                  : ''
              ]"
              @click="selectPopupMonth(month)"
            >
              {{ month.toString().padStart(2, "0") }}月
            </div>
          </div>
          <div class="date-picker-column">
            <div
              v-for="day in getPopupDaysInMonth()"
              :key="'day-' + day"
              :class="[
                'date-picker-item',
                popupSelectedDate && popupSelectedDate.getDate() === day
                  ? 'active'
                  : ''
              ]"
              @click="selectPopupDay(day)"
            >
              {{ day.toString().padStart(2, "0") }}日
            </div>
          </div>
        </div>

        <div class="date-confirm-btn">
          <van-button type="warning" block @click="confirmPopupCustomDate"
            >确定</van-button
          >
        </div>
      </div>
    </van-popup>

    <!-- 蒙层Loading效果 -->
    <div v-if="pageLoading" class="loading-overlay">
      <div class="loading-content">
        <van-loading type="spinner" size="24px" color="#FF9900" />
        <div class="loading-text">数据加载中...</div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  NavBar,
  Icon,
  Image as VanImage,
  Popup,
  Field,
  Button,
  Loading,
  List,
  Empty
} from "vant";
import * as echarts from "echarts/core";
import { LineChart } from "echarts/charts";
import {
  TooltipComponent,
  GridComponent,
  LegendComponent
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";

import {
  getSalesContractPage,
  getSalesContractEchars,
  getSalesContractTotal
} from "api/contractRepayment";

// 注册必要的组件
echarts.use([
  TooltipComponent,
  GridComponent,
  LegendComponent,
  LineChart,
  CanvasRenderer
]);

export default {
  components: {
    [NavBar.name]: NavBar,
    [Icon.name]: Icon,
    [VanImage.name]: VanImage,
    [Popup.name]: Popup,
    [Field.name]: Field,
    [Button.name]: Button,
    [Loading.name]: Loading,
    [List.name]: List,
    [Empty.name]: Empty
  },

  data() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();

    // 生成年份列表：从2015年到当前年份
    const yearList = [];
    for (let year = 2015; year <= currentYear; year++) {
      yearList.push(year);
    }

    return {
      // 表格显示的数据（从API获取）
      tableData: [],
      // 分页相关状态
      pageLoading: false,
      page: 1,
      pageSize: 10,
      hasMoreData: true,
      allData: [], // 存储弹窗中的所有数据
      totalData: {}, // 存储汇总行数据
      showAllDataPopup: false, // 控制底部弹窗显示
      // 排序相关状态
      currentSort: "contractAmount", // 当前排序的字段，默认为'contractAmount'
      sortOrder: "desc", // 排序方向: 'asc'升序, 'desc'降序(默认)
      // 列字段映射
      columnFields: [
        { label: "客户名称", field: "customerName" },
        { label: "合同金额(万)", field: "contractAmount" },
        { label: "硬件金额(万)", field: "hardwareAmount" },
        { label: "软件金额(万)", field: "softwareAmount" },
        { label: "施工金额(万)", field: "constructionAmount" },
        { label: "回款金额(万)", field: "repaymentAmount" }
      ],
      // 保留原有状态
      chartTimeRange: "month",
      chartDataMonth: null,
      chartDataYear: {
        xAxis: [
          "1月",
          "2月",
          "3月",
          "4月",
          "5月",
          "6月",
          "7月",
          "8月",
          "9月",
          "10月",
          "11月",
          "12月"
        ],
        contract: [350, 420, 480, 520, 580, 620, 690, 720, 760, 830, 890, 950],
        repayment: [320, 380, 420, 480, 520, 590, 650, 690, 720, 780, 840, 890]
      },
      annualTrendData: {
        xAxis: [
          "1月",
          "2月",
          "3月",
          "4月",
          "5月",
          "6月",
          "7月",
          "8月",
          "9月",
          "10月",
          "11月",
          "12月"
        ],
        contract: [
          220,
          500,
          850,
          1270,
          1750,
          2300,
          2950,
          3670,
          4430,
          5260,
          6150,
          7100
        ],
        repayment: [
          200,
          440,
          720,
          1050,
          1450,
          1930,
          2580,
          3270,
          3990,
          4770,
          5610,
          6500
        ]
      },

      currentTab: "time", // 'time' 或 'custom'
      dateType: "today", // 默认选择今天
      showDatePopup: false,
      showCustomDatePopup: false,
      customStartDate: "", // 默认为空，不预先选择
      customEndDate: "", // 默认为空，不预先选择
      datePickerMode: "start", // 'start' 或 'end'
      selectedDate: null, // 当前选择的日期，默认为null
      isPickerVisible: false, // 控制日期选择器的显示
      yearList: yearList, // 可选年份列表，从2015年到当前年份
      currentDate: currentDate,
      isEndDateEnabled: false, // 控制结束日期是否可选
      dateTypeOptions: [
        { label: "今天", value: "today" },
        { label: "昨日", value: "yesterday" },
        { label: "本周", value: "week" },
        { label: "本月", value: "month" },
        { label: "今年", value: "year" }
      ],

      total: 0,
      activeInput: "", // 当前激活的输入框：'start', 'end' 或 ''
      // 弹窗独立的时间状态
      popupCurrentTab: "time",
      popupCustomStartDate: "",
      popupCustomEndDate: "",
      popupDateType: "today",
      popupIsPickerVisible: false,
      popupSelectedDate: null,
      popupIsEndDateEnabled: false,
      popupActiveInput: "",
      popupCurrentSort: "contractAmount",
      popupSortOrder: "desc",
      showPopupDatePopup: false,
      showPopupCustomDatePopup: false,
      isScrollSyncing: false, // 防止滚动同步时的循环调用
      scrollHandlers: null, // 存储弹窗滚动事件处理函数的引用
      mainScrollHandlers: null // 存储主页面滚动事件处理函数的引用
    };
  },

  async mounted() {
    // 初始化路由参数
    this.initRouteParams();
    // 初始化获取数据
    this.pageLoading = true;
    try {
      await this.initData();
    } finally {
      this.pageLoading = false;
    }
  },

  beforeDestroy() {
    // 组件销毁前解绑所有滚动事件
    this.unbindScrollEvents();
    this.unbindMainScrollEvents();
  },

  computed: {
    // 自定义日期范围显示文本
    customDateRangeText() {
      if (
        this.currentTab !== "custom" ||
        (!this.customStartDate && !this.customEndDate)
      ) {
        return "自定义";
      }

      // 如果有开始和结束日期，格式化为 "YYYY-MM-DD 至 YYYY-MM-DD"
      const startFormatted = this.formatDateToHyphen(this.customStartDate);
      const endFormatted = this.formatDateToHyphen(
        this.customEndDate || this.customStartDate
      );

      return `${startFormatted} 至 ${endFormatted}`;
    },
    popupCustomDateRangeText() {
      if (
        this.popupCurrentTab !== "custom" ||
        (!this.popupCustomStartDate && !this.popupCustomEndDate)
      ) {
        return "自定义";
      }

      // 如果有开始和结束日期，格式化为 "YYYY-MM-DD 至 YYYY-MM-DD"
      const startFormatted = this.formatDateToHyphen(this.popupCustomStartDate);
      const endFormatted = this.formatDateToHyphen(
        this.popupCustomEndDate || this.popupCustomStartDate
      );

      return `${startFormatted} 至 ${endFormatted}`;
    },

    // 检查合同图表是否有数据
    hasContractChartData() {
      const data = this.chartTimeRange === "month" ? this.chartDataMonth : this.chartDataYear;
      return data && data.xAxis && data.xAxis.length > 0;
    },

    // 检查趋势图表是否有数据
    hasTrendChartData() {
      return this.annualTrendData && this.annualTrendData.xAxis && this.annualTrendData.xAxis.length > 0;
    }
  },

  methods: {
    // 获取timeType对应的数值
    getTimeTypeValue(dateType) {
      const typeMap = {
        today: 1, // 实时（今天）
        yesterday: 2, // 昨日
        week: 3, // 本周
        month: 4, // 本月
        year: 5, // 今年
        custom: 6 // 自定义
      };

      return typeMap[dateType] || "";
    },

    // 通用的tooltip格式化方法
    createTooltipFormatter(chartType = 'default') {
      return (params) => {
        const timeLabel = params[0].axisValue;
        let html = `<div style="padding: 8px;">`;
        
        // 根据图表类型设置时间显示
        if (chartType === 'trend') {
          html += `<div style="font-weight: bold; margin-bottom: 6px; color: #333;">${timeLabel}月</div>`;
        }  else {
          html += `<div style="font-weight: bold; margin-bottom: 6px; color: #333;">${timeLabel}日</div>`;
        }
        
        params.forEach((param) => {
          const color = param.color;
          const value = param.value;
          const name = param.seriesName;
          html += `<div style="display: flex; align-items: center; margin-bottom: 4px;">`;
          html += `<span style="display: inline-block; width: 8px; height: 8px; background-color: ${color}; border-radius: 50%; margin-right: 6px;"></span>`;
          html += `<span style="color: #666; margin-right: 8px;">${name}:</span>`;
          html += `<span style="font-weight: bold; color: #333;">${value}万元</span>`;
          html += `</div>`;
        });
        
        html += `</div>`;
        return html;
      };
    },

    // 通用的tooltip配置
    getTooltipConfig(chartType = 'default') {
      return {
        trigger: "axis",
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#E5E5E5',
        borderWidth: 1,
        textStyle: {
          color: '#333',
          fontSize: 12
        },
        extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border-radius: 4px;',
        formatter: this.createTooltipFormatter(chartType)
      };
    },

    // 初始化路由参数
    initRouteParams() {
      const timeType = this.$route.query.timeType;
      if (timeType) {
        // 根据timeType参数设置对应的dateType
        const timeTypeMap = {
          '1': 'today',
          '2': 'yesterday', 
          '3': 'week',
          '4': 'month',
          '5': 'year'
        };
        
        if (timeTypeMap[timeType]) {
          this.dateType = timeTypeMap[timeType];
          this.currentTab = 'time';
        }
      }
    },

    // 初始化数据 - 只加载表格显示的前5条数据
    async initData() {
      // 重置页码
      this.page = 1;
      // 调用接口获取前5条数据
      await this.getSalesContractPage();
      await this.getSalesContractEcharsMonth();
      await this.getSalesContractEcharsYear();
      // 获取汇总数据
      await this.getSalesContractTotal();

      // 绑定主页面滚动同步事件
      this.$nextTick(() => {
        this.bindMainScrollEvents();
      });
    },

    async getSalesContractPage() {
      try {
        const params = {
          no: 1,
          limit: 5, // 初始加载5条
          timeType:
            this.currentTab === "time"
              ? this.getTimeTypeValue(this.dateType)
              : this.getTimeTypeValue("custom"), // 时间类型对应的数值
          sortField: this.currentSort || "contractAmount", // 排序字段，默认为合同金额
          sortFieldFlag:
            this.sortOrder === "asc" ? 0 : this.sortOrder === "desc" ? 1 : 1 // 排序标志: 0-升序，1-降序，默认降序
        };

        // 只有当timeType为6（自定义）时才传递startTime和endTime参数
        const timeTypeValue =
          this.currentTab === "time"
            ? this.getTimeTypeValue(this.dateType)
            : this.getTimeTypeValue("custom");

        if (timeTypeValue === 6) {
          params.startTime = this.customStartDate
            ? this.formatDateToHyphen(this.customStartDate)
            : "";
          params.endTime = this.customEndDate
            ? this.formatDateToHyphen(this.customEndDate)
            : "";
        }

        const res = await getSalesContractPage({
          ...params
        });

        if (res && res.records) {
          // 处理返回数据
          this.total = res.total;
          this.tableData = res.records || [];

          // 确保DOM更新后重新绑定滚动事件
          this.$nextTick(() => {
            this.bindMainScrollEvents();
          });
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 折线图数据 - 本月
    async getSalesContractEcharsMonth() {
      try {
        const res = await getSalesContractEchars({
          type: 1,
          timeType: 4 // 本月
        });

        if (res && res) {
          // 转换接口数据为图表所需格式
          this.chartDataMonth = {
            xAxis: res.times || [],
            contract: res.contractAmounts || [],
            repayment: res.repaymentAmounts || []
          };
          console.log("本月图表数据加载成功:", this.chartDataMonth);

          // 触发图表重新渲染
          this.$nextTick(() => {
            this.updateCharts();
          });
        } else {
          console.error("获取本月图表数据失败:", res?.msg || "接口返回异常");
        }
      } catch (error) {
        console.error("获取本月图表数据异常:", error);
      }
    },

    // 折线图数据 - 本年
    async getSalesContractEcharsYear() {
      try {
        const res = await getSalesContractEchars({
          type: 1,
          timeType: 5 // 本年
        });

        if (res) {
          // 转换接口数据为图表所需格式
          const yearData = {
            xAxis: res.times || [],
            contract: res.contractAmounts || [],
            repayment: res.repaymentAmounts || []
          };

          // 更新本年图表数据
          this.chartDataYear = yearData;

          // 年度累计趋势使用相同的本年数据
          this.annualTrendData = yearData;

          console.log("本年图表数据加载成功:", yearData);

          // 触发图表重新渲染
          this.$nextTick(() => {
            this.updateCharts();
          });
        } else {
          console.error("获取本年图表数据失败:", res?.msg || "接口返回异常");
        }
      } catch (error) {
        console.error("获取本年图表数据异常:", error);
      }
    },

    // 获取汇总数据
    async getSalesContractTotal() {
      try {
        const params = {
          timeType:
            this.currentTab === "time"
              ? this.getTimeTypeValue(this.dateType)
              : this.getTimeTypeValue("custom")
        };

        // 只有当timeType为6（自定义）时才传递startTime和endTime参数
        const timeTypeValue =
          this.currentTab === "time"
            ? this.getTimeTypeValue(this.dateType)
            : this.getTimeTypeValue("custom");

        if (timeTypeValue === 6) {
          params.startTime = this.customStartDate
            ? this.formatDateToHyphen(this.customStartDate)
            : "";
          params.endTime = this.customEndDate
            ? this.formatDateToHyphen(this.customEndDate)
            : "";
        }

        const res = await getSalesContractTotal(params);

        if (res) {
          // 更新汇总数据
          this.totalData = {
            contractAmount: res.contractAmount || 0,
            hardwareAmount: res.hardwareAmount || 0,
            softwareAmount: res.softwareAmount || 0,
            constructionAmount: res.constructionAmount || 0,
            repaymentAmount: res.repaymentAmount || 0
          };
          console.log("汇总数据加载成功:", this.totalData);
        }
      } catch (error) {
        console.error("获取汇总数据异常:", error);
      } finally {
        // 确保totalData有默认值
        if (!this.totalData || Object.keys(this.totalData).length === 0) {
          this.totalData = {
            contractAmount: 0,
            hardwareAmount: 0,
            softwareAmount: 0,
            constructionAmount: 0,
            repaymentAmount: 0
          };
        }
      }
    },

    // 获取弹窗汇总数据
    async getPopupSalesContractTotal() {
      try {
        const params = {
          timeType:
            this.popupCurrentTab === "time"
              ? this.getTimeTypeValue(this.popupDateType)
              : this.getTimeTypeValue("custom")
        };

        // 只有当timeType为6（自定义）时才传递startTime和endTime参数
        const timeTypeValue =
          this.popupCurrentTab === "time"
            ? this.getTimeTypeValue(this.popupDateType)
            : this.getTimeTypeValue("custom");

        if (timeTypeValue === 6) {
          params.startTime = this.popupCustomStartDate
            ? this.formatDateToHyphen(this.popupCustomStartDate)
            : "";
          params.endTime = this.popupCustomEndDate
            ? this.formatDateToHyphen(this.popupCustomEndDate)
            : "";
        }

        const res = await getSalesContractTotal(params);

        if (res) {
          // 更新汇总数据 - 修正数据访问路径，与主页面保持一致
          this.totalData = {
            contractAmount: res.contractAmount || 0,
            hardwareAmount: res.hardwareAmount || 0,
            softwareAmount: res.softwareAmount || 0,
            constructionAmount: res.constructionAmount || 0,
            repaymentAmount: res.repaymentAmount || 0
          };
        }
      } catch (error) {
        console.error("获取弹窗汇总数据异常:", error);
      } finally {
        // 确保totalData有默认值
        if (!this.totalData || Object.keys(this.totalData).length === 0) {
          this.totalData = {
            contractAmount: 0,
            hardwareAmount: 0,
            softwareAmount: 0,
            constructionAmount: 0,
            repaymentAmount: 0
          };
        }
      }
    },

    // 将自定义日期格式转为带连字符的格式：YYYY-MM-DD
    formatDateToHyphen(dateStr) {
      if (!dateStr) return "";

      const regex = /(\d{4})年(\d{2})月(\d{2})日/;
      const match = dateStr.match(regex);

      if (match) {
        const year = match[1];
        const month = match[2];
        const day = match[3];
        return `${year}-${month}-${day}`;
      }

      return "";
    },

    onClickLeft() {
      this.backToApp();
    },

    handleRowClick(item) {
      // 如果是汇总行，不跳转
      if (item.customerName === "汇总") {
        return;
      }
    },

    getDateTypeLabel() {
      const found = this.dateTypeOptions.find(
        item => item.value === this.dateType
      );
      return found ? found.label : "今天";
    },

    getPopupDateTypeLabel() {
      const found = this.dateTypeOptions.find(
        item => item.value === this.popupDateType
      );
      return found ? found.label : "今天";
    },

    handleTabClick(tab) {
      this.currentTab = tab;
      if (tab === "time") {
        this.showDatePopup = true;
      } else {
        // 不再设置默认日期，保持输入框为空
        this.showCustomDatePopup = true;
        this.isPickerVisible = false; // 初始不显示选择器
      }
    },

    async selectDateType(type) {
      this.dateType = type;
      this.showDatePopup = false;
      this.currentTab = "time";

      // 清除自定义日期，避免切换回自定义时仍显示之前的日期
      this.customStartDate = "";
      this.customEndDate = "";
      this.isEndDateEnabled = false;
      
      // 重新加载表格数据和汇总数据
      this.pageLoading = true;
      try {
        await this.getSalesContractPage();
        await this.getSalesContractEcharsMonth();
        await this.getSalesContractEcharsYear();
        await this.getSalesContractTotal();

        // 确保DOM更新后重新绑定滚动事件
        this.$nextTick(() => {
          this.bindMainScrollEvents();
        });
      } finally {
        this.pageLoading = false;
      }
    },

    // 处理日期输入框点击
    handleDateInputClick(mode) {
      if (mode === "end" && !this.isEndDateEnabled) {
        // 如果点击结束日期，但尚未选择开始日期，则提示并不做处理
        this.$toast("请先选择开始日期");
        return;
      }

      // 设置当前激活的输入框
      this.activeInput = mode;

      this.datePickerMode = mode;
      this.isPickerVisible = true;

      // 设置初始选择日期
      if (mode === "start" && this.customStartDate) {
        this.selectedDate = this.parseCustomDate(this.customStartDate);
      } else if (mode === "end" && this.customEndDate) {
        this.selectedDate = this.parseCustomDate(this.customEndDate);
      } else {
        // 默认显示当前日期
        this.selectedDate = new Date();

        // 如果是选择结束日期，确保默认日期不小于开始日期
        if (mode === "end" && this.customStartDate) {
          const startDate = this.parseCustomDate(this.customStartDate);
          if (this.selectedDate < startDate) {
            this.selectedDate = new Date(startDate);
          }
        }
      }
    },

    // 解析自定义格式的日期字符串
    parseCustomDate(dateStr) {
      if (!dateStr) return new Date();

      const regex = /(\d{4})年(\d{2})月(\d{2})日/;
      const match = dateStr.match(regex);

      if (match) {
        const year = parseInt(match[1]);
        const month = parseInt(match[2]) - 1;
        const day = parseInt(match[3]);
        return new Date(year, month, day);
      }

      return new Date();
    },

    // 获取当前选择年月的天数
    getDaysInMonth() {
      if (!this.selectedDate) return 31;

      const year = this.selectedDate.getFullYear();
      const month = this.selectedDate.getMonth();
      // 下个月的第0天就是当前月的最后一天
      const lastDay = new Date(year, month + 1, 0).getDate();

      return lastDay;
    },

    selectYear(year) {
      if (!this.selectedDate) {
        this.selectedDate = new Date();
      }
      const newDate = new Date(this.selectedDate);
      newDate.setFullYear(year);

      // 如果是选择结束日期，确保不小于开始日期
      if (this.datePickerMode === "end" && this.customStartDate) {
        const startDate = this.parseCustomDate(this.customStartDate);
        const currentStartYear = startDate.getFullYear();

        // 如果选择的年份小于开始日期的年份，使用开始日期的年份
        if (year < currentStartYear) {
          newDate.setFullYear(currentStartYear);
          newDate.setMonth(startDate.getMonth());
          newDate.setDate(startDate.getDate());
        } else if (year === currentStartYear) {
          // 如果年份相同，确保月份和日期不小于开始日期
          if (newDate.getMonth() < startDate.getMonth()) {
            newDate.setMonth(startDate.getMonth());
            newDate.setDate(startDate.getDate());
          } else if (
            newDate.getMonth() === startDate.getMonth() &&
            newDate.getDate() < startDate.getDate()
          ) {
            newDate.setDate(startDate.getDate());
          }
        }
      }

      this.selectedDate = newDate;
      this.updateCustomDate();
    },

    selectMonth(month) {
      if (!this.selectedDate) {
        this.selectedDate = new Date();
      }
      const newDate = new Date(this.selectedDate);

      // 如果是选择结束日期，确保不小于开始日期
      if (this.datePickerMode === "end" && this.customStartDate) {
        const startDate = this.parseCustomDate(this.customStartDate);
        const currentYear = newDate.getFullYear();
        const startYear = startDate.getFullYear();

        if (currentYear === startYear) {
          // 同年情况下，确保月份不小于开始日期的月份
          if (month - 1 < startDate.getMonth()) {
            newDate.setMonth(startDate.getMonth());
            newDate.setDate(startDate.getDate());
            this.selectedDate = newDate;
            this.updateCustomDate();
            return;
          } else if (
            month - 1 === startDate.getMonth() &&
            newDate.getDate() < startDate.getDate()
          ) {
            // 同月情况下，确保日期不小于开始日期
            newDate.setDate(startDate.getDate());
          }
        }
      }

      newDate.setMonth(month - 1);

      // 处理月份天数问题，避免溢出
      const currentDay = newDate.getDate();
      const lastDay = new Date(newDate.getFullYear(), month, 0).getDate();
      if (currentDay > lastDay) {
        newDate.setDate(lastDay);
      }

      this.selectedDate = newDate;
      this.updateCustomDate();
    },

    selectDay(day) {
      if (!this.selectedDate) {
        this.selectedDate = new Date();
      }

      const newDate = new Date(this.selectedDate);

      // 如果是选择结束日期，确保不小于开始日期
      if (this.datePickerMode === "end" && this.customStartDate) {
        const startDate = this.parseCustomDate(this.customStartDate);
        const currentYear = newDate.getFullYear();
        const currentMonth = newDate.getMonth();
        const startYear = startDate.getFullYear();
        const startMonth = startDate.getMonth();

        if (currentYear === startYear && currentMonth === startMonth) {
          // 同年同月情况下，确保日期不小于开始日期的日期
          if (day < startDate.getDate()) {
            newDate.setDate(startDate.getDate());
            this.selectedDate = newDate;
            this.updateCustomDate();
            return;
          }
        }
      }

      newDate.setDate(day);
      this.selectedDate = newDate;
      this.updateCustomDate();

      // 选择完日期后，关闭日期选择器
      this.isPickerVisible = false;
      // 清除激活状态
      this.clearActiveInput();
    },

    updateCustomDate() {
      if (!this.selectedDate) return;

      const date = this.selectedDate;
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const formattedDate = `${year}年${month
        .toString()
        .padStart(2, "0")}月${day.toString().padStart(2, "0")}日`;

      if (this.datePickerMode === "start") {
        this.customStartDate = formattedDate;
        // 启用结束日期选择
        this.isEndDateEnabled = true;

        // 如果已经有结束日期，且结束日期小于新的开始日期，则清空结束日期
        if (this.customEndDate) {
          const endDate = this.parseCustomDate(this.customEndDate);
          if (endDate < this.selectedDate) {
            this.customEndDate = "";
          }
        }
      } else {
        this.customEndDate = formattedDate;
      }
    },

    updateCharts() {
      this.$nextTick(() => {
        if (this.hasContractChartData) {
          this.initContractChart();
        }
        if (this.hasTrendChartData) {
          this.initTrendChart();
        }
      });
    },

    initContractChart() {
      const chartDom = this.$refs.contractChart;
      if (!chartDom) return;

      const myChart = echarts.init(chartDom);

      // 根据当前选择的时间范围选择数据
      const chartData =
        this.chartTimeRange === "month"
          ? this.chartDataMonth
          : this.chartDataYear;

      const option = {
        tooltip: this.getTooltipConfig('contract'),
        legend: {
          show: false
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "5%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: chartData.xAxis
        },
        yAxis: {
          type: "value",
          splitLine: {
            lineStyle: {
              type: "dashed",
              color: "#E5E5E5"
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: "合同额",
            type: "line",
            data: chartData.contract,
            smooth: true,
            symbol: "circle",
            symbolSize: 6,
            itemStyle: {
              color: "#FF9900"
            },
            lineStyle: {
              color: "#FF9900",
              width: 2
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(255, 153, 0, 0.2)"
                  },
                  {
                    offset: 1,
                    color: "rgba(255, 153, 0, 0)"
                  }
                ]
              }
            }
          },
          {
            name: "回款额",
            type: "line",
            data: chartData.repayment,
            smooth: true,
            symbol: "circle",
            symbolSize: 6,
            itemStyle: {
              color: "#4ECB73"
            },
            lineStyle: {
              color: "#4ECB73",
              width: 2
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(78, 203, 115, 0.2)"
                  },
                  {
                    offset: 1,
                    color: "rgba(78, 203, 115, 0)"
                  }
                ]
              }
            }
          }
        ]
      };

      myChart.setOption(option);

      window.addEventListener("resize", function() {
        myChart.resize();
      });
    },

    initTrendChart() {
      const chartDom = this.$refs.trendChart;
      if (!chartDom) return;

      const myChart = echarts.init(chartDom);

      // 始终使用年度累计数据，不受本月/今年切换的影响
      const chartData = this.annualTrendData;

      const option = {
        tooltip: this.getTooltipConfig('trend'),
        legend: {
          show: false
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "5%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: chartData.xAxis
        },
        yAxis: {
          type: "value",
          splitLine: {
            lineStyle: {
              type: "dashed",
              color: "#E5E5E5"
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: "合同额",
            type: "line",
            data: chartData.contract,
            smooth: true,
            symbol: "circle",
            symbolSize: 6,
            itemStyle: {
              color: "#FF9900"
            },
            lineStyle: {
              color: "#FF9900",
              width: 2
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(255, 153, 0, 0.2)"
                  },
                  {
                    offset: 1,
                    color: "rgba(255, 153, 0, 0)"
                  }
                ]
              }
            }
          },
          {
            name: "回款额",
            type: "line",
            data: chartData.repayment,
            smooth: true,
            symbol: "circle",
            symbolSize: 6,
            itemStyle: {
              color: "#4ECB73"
            },
            lineStyle: {
              color: "#4ECB73",
              width: 2
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(78, 203, 115, 0.2)"
                  },
                  {
                    offset: 1,
                    color: "rgba(78, 203, 115, 0)"
                  }
                ]
              }
            }
          }
        ]
      };

      myChart.setOption(option);

      window.addEventListener("resize", function() {
        myChart.resize();
      });
    },

    async confirmCustomDate() {
      // 检查是否有选择日期，如果没有则不做任何操作
      if (!this.customStartDate) {
        this.$toast("请选择开始日期");
        return;
      }

      // 确保结束日期存在，如果不存在则使用开始日期
      if (!this.customEndDate) {
        this.customEndDate = this.customStartDate;
      }

      // 清除激活状态
      this.clearActiveInput();

      this.showCustomDatePopup = false;
      this.currentTab = "custom";
      
      // 重新加载数据
      this.pageLoading = true;
      try {
        await this.getSalesContractPage();
        await this.getSalesContractEcharsMonth();
        await this.getSalesContractEcharsYear();
        await this.getSalesContractTotal();

        // 确保DOM更新后重新绑定滚动事件
        this.$nextTick(() => {
          this.bindMainScrollEvents();
        });
      } finally {
        this.pageLoading = false;
      }
    },

    changeChartTimeRange(range) {
      this.chartTimeRange = range;
      this.updateMainChart(); // 只更新上方图表
    },

    updateMainChart() {
      this.$nextTick(() => {
        this.initContractChart();
      });
    },

    // 显示全部数据
    showAllData() {
      // 打开弹窗，准备加载数据
      this.showAllDataPopup = true;
      this.page = 1;
      this.hasMoreData = true;
      this.allData = []; // 清空之前的数据

      // 初始化弹窗的时间状态为主页面的状态
      this.popupCurrentTab = this.currentTab;
      this.popupDateType = this.dateType;
      this.popupCustomStartDate = this.customStartDate;
      this.popupCustomEndDate = this.customEndDate;
      this.popupIsEndDateEnabled = this.isEndDateEnabled;

      // 立即加载第一页数据和汇总数据
      this.$nextTick(() => {
        this.loadMorePage();
        this.getPopupSalesContractTotal();
      });
    },

    // 加载下一页数据
    async loadMorePage() {
      if (!this.hasMoreData) return;

      try {
        // 如果是第一页，从1开始，否则增加页码
        if (this.allData.length === 0) {
          this.page = 1;
        } else {
          this.page += 1;
        }

        const params = {
          no: this.page, // 使用当前页码
          limit: 10, // 每次加载10条
          timeType:
            this.popupCurrentTab === "time"
              ? this.getTimeTypeValue(this.popupDateType)
              : this.getTimeTypeValue("custom"),
          sortField: this.popupCurrentSort || "contractAmount",
          sortFieldFlag:
            this.popupSortOrder === "asc"
              ? 0
              : this.popupSortOrder === "desc"
              ? 1
              : 1 // 默认降序
        };

        // 只有当timeType为6（自定义）时才传递startTime和endTime参数
        const timeTypeValue =
          this.popupCurrentTab === "time"
            ? this.getTimeTypeValue(this.popupDateType)
            : this.getTimeTypeValue("custom");

        if (timeTypeValue === 6) {
          params.startTime = this.popupCustomStartDate
            ? this.formatDateToHyphen(this.popupCustomStartDate)
            : "";
          params.endTime = this.popupCustomEndDate
            ? this.formatDateToHyphen(this.popupCustomEndDate)
            : "";
        }

        // 设置加载状态
        this.pageLoading = true;

        const res = await getSalesContractPage({
          ...params
        });

        if (res && res.records) {
          // 处理返回数据
          const data = res.records || [];

          // 追加数据到弹窗表格
          this.allData = [...this.allData, ...data];

          // 使用总数据量来准确判断是否还有更多数据
          const totalCount = res.total || 0;
          this.hasMoreData = this.allData.length < totalCount;

          // 绑定滚动事件（确保DOM更新后绑定）
          this.$nextTick(() => {
            this.bindScrollEvents();
          });
        } else {
          // 接口返回异常处理
          this.$toast.fail(res?.msg || "获取数据失败");
          this.hasMoreData = false;
        }
      } catch (error) {
        console.log(error);
        this.$toast.fail("获取数据异常");
        this.hasMoreData = false;
      } finally {
        this.pageLoading = false;
      }
    },

    handleAllDataPopupClosed() {
      // 解绑滚动事件
      this.unbindScrollEvents();

      // 弹窗关闭后重置数据状态
      this.page = 1;
      this.hasMoreData = true;
      this.allData = [];
      // 注意：不重置totalData，因为主页面还需要显示汇总数据
      // 重置弹窗的时间选择状态
      this.popupCurrentTab = "time";
      this.popupDateType = "today";
      this.popupCustomStartDate = "";
      this.popupCustomEndDate = "";
      this.popupIsEndDateEnabled = false;
      this.popupIsPickerVisible = false;
      this.popupSelectedDate = null;
      this.popupActiveInput = "";
      this.popupCurrentSort = "contractAmount";
      this.popupSortOrder = "desc";
      this.showPopupDatePopup = false;
      this.showPopupCustomDatePopup = false;
      this.isScrollSyncing = false;
    },

    handleSort(field) {
      if (this.currentSort === field) {
        // 如果点击的是当前排序的字段，则切换排序顺序
        this.sortOrder =
          this.sortOrder === "asc"
            ? "desc"
            : this.sortOrder === "desc"
            ? ""
            : "asc";
      } else {
        // 如果点击的是新字段，则设置为升序排序
        this.currentSort = field;
        this.sortOrder = "asc";
      }

      // 重新加载数据
      this.initData();
    },

    // 清除激活状态
    clearActiveInput() {
      this.activeInput = "";
    },

    handlePopupTabClick(tab) {
      this.popupCurrentTab = tab;
      if (tab === "time") {
        this.showPopupDatePopup = true;
      } else {
        // 不再设置默认日期，保持输入框为空
        this.showPopupCustomDatePopup = true;
        this.popupIsPickerVisible = false; // 初始不显示选择器
      }
    },

    selectPopupDateType(type) {
      this.popupDateType = type;
      this.showPopupDatePopup = false;
      this.popupCurrentTab = "time";

      // 清除自定义日期，避免切换回自定义时仍显示之前的日期
      this.popupCustomStartDate = "";
      this.popupCustomEndDate = "";
      this.popupIsEndDateEnabled = false;

      // 重新加载弹窗数据
      this.reloadPopupData();
    },

    handlePopupDateInputClick(mode) {
      if (mode === "end" && !this.popupIsEndDateEnabled) {
        // 如果点击结束日期，但尚未选择开始日期，则提示并不做处理
        this.$toast("请先选择开始日期");
        return;
      }

      // 设置当前激活的输入框
      this.popupActiveInput = mode;

      this.datePickerMode = mode;
      this.popupIsPickerVisible = true;

      // 设置初始选择日期
      if (mode === "start" && this.popupCustomStartDate) {
        this.popupSelectedDate = this.parseCustomDate(
          this.popupCustomStartDate
        );
      } else if (mode === "end" && this.popupCustomEndDate) {
        this.popupSelectedDate = this.parseCustomDate(this.popupCustomEndDate);
      } else {
        // 默认显示当前日期
        this.popupSelectedDate = new Date();

        // 如果是选择结束日期，确保默认日期不小于开始日期
        if (mode === "end" && this.popupCustomStartDate) {
          const startDate = this.parseCustomDate(this.popupCustomStartDate);
          if (this.popupSelectedDate < startDate) {
            this.popupSelectedDate = new Date(startDate);
          }
        }
      }
    },

    selectPopupYear(year) {
      if (!this.popupSelectedDate) {
        this.popupSelectedDate = new Date();
      }
      const newDate = new Date(this.popupSelectedDate);
      newDate.setFullYear(year);

      // 如果是选择结束日期，确保不小于开始日期
      if (this.datePickerMode === "end" && this.popupCustomStartDate) {
        const startDate = this.parseCustomDate(this.popupCustomStartDate);
        const currentStartYear = startDate.getFullYear();

        // 如果选择的年份小于开始日期的年份，使用开始日期的年份
        if (year < currentStartYear) {
          newDate.setFullYear(currentStartYear);
          newDate.setMonth(startDate.getMonth());
          newDate.setDate(startDate.getDate());
        } else if (year === currentStartYear) {
          // 如果年份相同，确保月份和日期不小于开始日期
          if (newDate.getMonth() < startDate.getMonth()) {
            newDate.setMonth(startDate.getMonth());
            newDate.setDate(startDate.getDate());
          } else if (
            newDate.getMonth() === startDate.getMonth() &&
            newDate.getDate() < startDate.getDate()
          ) {
            newDate.setDate(startDate.getDate());
          }
        }
      }

      this.popupSelectedDate = newDate;
      this.updatePopupCustomDate();
    },

    selectPopupMonth(month) {
      if (!this.popupSelectedDate) {
        this.popupSelectedDate = new Date();
      }
      const newDate = new Date(this.popupSelectedDate);

      // 如果是选择结束日期，确保不小于开始日期
      if (this.datePickerMode === "end" && this.popupCustomStartDate) {
        const startDate = this.parseCustomDate(this.popupCustomStartDate);
        const currentYear = newDate.getFullYear();
        const startYear = startDate.getFullYear();

        if (currentYear === startYear) {
          // 同年情况下，确保月份不小于开始日期的月份
          if (month - 1 < startDate.getMonth()) {
            newDate.setMonth(startDate.getMonth());
            newDate.setDate(startDate.getDate());
            this.popupSelectedDate = newDate;
            this.updatePopupCustomDate();
            return;
          } else if (
            month - 1 === startDate.getMonth() &&
            newDate.getDate() < startDate.getDate()
          ) {
            // 同月情况下，确保日期不小于开始日期
            newDate.setDate(startDate.getDate());
          }
        }
      }

      newDate.setMonth(month - 1);

      // 处理月份天数问题，避免溢出
      const currentDay = newDate.getDate();
      const lastDay = new Date(newDate.getFullYear(), month, 0).getDate();
      if (currentDay > lastDay) {
        newDate.setDate(lastDay);
      }

      this.popupSelectedDate = newDate;
      this.updatePopupCustomDate();
    },

    selectPopupDay(day) {
      if (!this.popupSelectedDate) {
        this.popupSelectedDate = new Date();
      }

      const newDate = new Date(this.popupSelectedDate);

      // 如果是选择结束日期，确保不小于开始日期
      if (this.datePickerMode === "end" && this.popupCustomStartDate) {
        const startDate = this.parseCustomDate(this.popupCustomStartDate);
        const currentYear = newDate.getFullYear();
        const currentMonth = newDate.getMonth();
        const startYear = startDate.getFullYear();
        const startMonth = startDate.getMonth();

        if (currentYear === startYear && currentMonth === startMonth) {
          // 同年同月情况下，确保日期不小于开始日期的日期
          if (day < startDate.getDate()) {
            newDate.setDate(startDate.getDate());
            this.popupSelectedDate = newDate;
            this.updatePopupCustomDate();
            return;
          }
        }
      }

      newDate.setDate(day);
      this.popupSelectedDate = newDate;
      this.updatePopupCustomDate();

      // 选择完日期后，关闭日期选择器
      this.popupIsPickerVisible = false;
      // 清除激活状态
      this.clearPopupActiveInput();
    },

    updatePopupCustomDate() {
      if (!this.popupSelectedDate) return;

      const date = this.popupSelectedDate;
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const formattedDate = `${year}年${month
        .toString()
        .padStart(2, "0")}月${day.toString().padStart(2, "0")}日`;

      if (this.datePickerMode === "start") {
        this.popupCustomStartDate = formattedDate;
        // 启用结束日期选择
        this.popupIsEndDateEnabled = true;

        // 如果已经有结束日期，且结束日期小于新的开始日期，则清空结束日期
        if (this.popupCustomEndDate) {
          const endDate = this.parseCustomDate(this.popupCustomEndDate);
          if (endDate < this.popupSelectedDate) {
            this.popupCustomEndDate = "";
          }
        }
      } else {
        this.popupCustomEndDate = formattedDate;
      }
    },

    handlePopupSort(field) {
      if (this.popupCurrentSort === field) {
        // 如果点击的是当前排序的字段，则切换排序顺序
        this.popupSortOrder =
          this.popupSortOrder === "asc"
            ? "desc"
            : this.popupSortOrder === "desc"
            ? ""
            : "asc";
      } else {
        // 如果点击的是新字段，则设置为升序排序
        this.popupCurrentSort = field;
        this.popupSortOrder = "asc";
      }

      // 重新加载弹窗数据
      this.reloadPopupData();
    },

    clearPopupActiveInput() {
      this.popupActiveInput = "";
    },

    confirmPopupCustomDate() {
      // 检查是否有选择日期，如果没有则不做任何操作
      if (!this.popupCustomStartDate) {
        this.$toast("请选择开始日期");
        return;
      }

      // 确保结束日期存在，如果不存在则使用开始日期
      if (!this.popupCustomEndDate) {
        this.popupCustomEndDate = this.popupCustomStartDate;
      }

      // 清除激活状态
      this.clearPopupActiveInput();

      this.showPopupCustomDatePopup = false;
      this.popupCurrentTab = "custom";

      // 重新加载弹窗数据
      this.reloadPopupData();
    },

    getPopupDaysInMonth() {
      if (!this.popupSelectedDate) return 31;

      const year = this.popupSelectedDate.getFullYear();
      const month = this.popupSelectedDate.getMonth();
      // 下个月的第0天就是当前月的最后一天
      const lastDay = new Date(year, month + 1, 0).getDate();

      return lastDay;
    },

    reloadPopupData() {
      // 重置分页状态
      this.page = 1;
      this.hasMoreData = true;
      this.allData = [];
      // 注意：不重置totalData，避免汇总数据闪烁消失

      // 重新加载数据和汇总
      this.$nextTick(() => {
        this.loadMorePage();
        this.getPopupSalesContractTotal();
      });
    },

    // 同步表头、内容和合计行的水平滚动
    syncHorizontalScroll(sourceElement, targetElements) {
      if (this.isScrollSyncing) return;

      this.isScrollSyncing = true;
      const scrollLeft = sourceElement.scrollLeft;

      targetElements.forEach(element => {
        if (element && element !== sourceElement) {
          element.scrollLeft = scrollLeft;
        }
      });

      this.$nextTick(() => {
        this.isScrollSyncing = false;
      });
    },

    // 绑定滚动事件
    bindScrollEvents() {
      // 先解绑之前的事件，避免重复绑定
      this.unbindScrollEvents();

      this.$nextTick(() => {
        const header = this.$refs.popupTableHeader;
        const body = this.$refs.popupTableBody;
        const footer = this.$refs.popupTableFooter;

        // 确保表头和内容区域都存在才绑定事件
        if (header && body) {
          const elements = [header, body, footer].filter(Boolean);

          // 只有当有元素时才绑定事件
          if (elements.length > 0) {
            // 保存事件处理函数的引用，方便后续解绑
            this.scrollHandlers = elements.map(element => {
              const handler = () => {
                this.syncHorizontalScroll(element, elements);
              };
              element.addEventListener("scroll", handler, { passive: true });
              return { element, handler };
            });

            console.log(
              "滚动事件已绑定，包含元素数量:",
              elements.length,
              "包含合计行:",
              !!footer
            );
          }
        }
      });
    },

    // 绑定主页面滚动同步事件
    bindMainScrollEvents() {
      // 先解绑之前的事件，避免重复绑定
      this.unbindMainScrollEvents();

      this.$nextTick(() => {
        const header = this.$refs.mainTableHeader;
        const body = this.$refs.mainTableBody;
        const footer = this.$refs.mainTableFooter;

        // 确保表头和内容区域都存在才绑定事件
        if (header && body) {
          const elements = [header, body, footer].filter(Boolean);

          // 只有当有元素时才绑定事件
          if (elements.length > 0) {
            // 保存事件处理函数的引用，方便后续解绑
            this.mainScrollHandlers = elements.map(element => {
              const handler = () => {
                this.syncHorizontalScroll(element, elements);
              };
              element.addEventListener("scroll", handler, { passive: true });
              return { element, handler };
            });

            console.log(
              "主页面滚动事件已绑定，包含元素数量:",
              elements.length,
              "包含合计行:",
              !!footer
            );
          }
        }
      });
    },

    // 解绑主页面滚动事件
    unbindMainScrollEvents() {
      if (this.mainScrollHandlers) {
        this.mainScrollHandlers.forEach(({ element, handler }) => {
          if (element && handler) {
            element.removeEventListener("scroll", handler);
          }
        });
        this.mainScrollHandlers = null;
      }
    },

    // 解绑滚动事件
    unbindScrollEvents() {
      if (this.scrollHandlers) {
        this.scrollHandlers.forEach(({ element, handler }) => {
          if (element && handler) {
            element.removeEventListener("scroll", handler);
          }
        });
        this.scrollHandlers = null;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./index.scss";

.empty-state-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 200px;
  background-color: #fff;
  border-radius: 8px;
}

/* 蒙层Loading样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 24px 32px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(4px);
}

.loading-text {
  margin-top: 12px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

/* 图表空状态样式 */
.chart-empty {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
